(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6029],{56029:function(e,t,s){"use strict";s.d(t,{Z:function(){return O}});var r=s(85893),a=s(67294),i=s(33299),o=s(9008),n=s.n(o),c=s(41664),l=s.n(c),d=s(11163),h=s(40282),u=s.n(h),m=s(48240),p=s.n(m),g=()=>{var e,t,s;let[i,o]=(0,a.useState)({hours:0,minutes:0,seconds:0}),[n,c]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=()=>{let e=new Date,t=new Date(e);t.setHours(19,30,0,0);let s=e.getTimezoneOffset();t.setMinutes(t.getMinutes()+(s+-300)),e>t&&t.setDate(t.getDate()+1);let r=t-e;return r<=0?(c(!0),{hours:0,minutes:0,seconds:0}):{hours:Math.floor(r/36e5),minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}};o(e());let t=setInterval(()=>{let s=e();o(s),0===s.hours&&0===s.minutes&&0===s.seconds&&(c(!0),clearInterval(t))},1e3);return()=>clearInterval(t)},[]);let l=e=>e.toString().padStart(2,"0");return(0,r.jsxs)("div",{className:p().shippingCutoff,children:[(0,r.jsx)("div",{className:p().cutoffMessage,children:"Express shipping cuts off at 7:30 PM EST today. Please plan accordingly!"}),(0,r.jsxs)("div",{className:p().cutoffTimer,children:[(0,r.jsx)("div",{className:p().timerDisplay,children:(0,r.jsx)("span",{className:p().timerDigits,children:(e=i.hours,t=i.minutes,s=i.seconds,"".concat(e%12||12,":").concat(l(t),":").concat(l(s)," ").concat(e>=12?"PM":"AM"))})}),(0,r.jsxs)("div",{className:p().shippingMethod,children:[(0,r.jsx)("span",{className:p().methodName,children:"FedEx Standard Overnight"}),(0,r.jsxs)("span",{className:p().freeShipping,children:[(0,r.jsx)("strong",{children:"FREE"})," when you spend over $1,000.00"]})]})]})]})},x=s(27936),_=s.n(x),j=()=>{let[e,t]=(0,a.useState)(""),[s,i]=(0,a.useState)(!1),[o,n]=(0,a.useState)([]),c=(0,a.useRef)(null),l=(0,d.useRouter)(),h=[{id:1,name:"iPhone Parts",slug:"categories/iphone-parts"},{id:2,name:"Samsung Parts",slug:"categories/samsung-parts"},{id:3,name:"iPad Parts",slug:"categories/ipad-parts"},{id:4,name:"MacBook Parts",slug:"categories/macbook-parts"},{id:5,name:"Repair Tools",slug:"categories/repair-tools"},{id:6,name:"iPhone 15 Screen",slug:"products/iphone-15-screen"},{id:7,name:"iPhone 14 Battery",slug:"products/iphone-14-battery"},{id:8,name:"Samsung S24 Screen",slug:"products/samsung-s24-screen"},{id:9,name:"iPad Pro Screen",slug:"products/ipad-pro-screen"},{id:10,name:"Screwdriver Set",slug:"products/screwdriver-set"}],u=e=>{l.push("/".concat(e)),t(""),n([]),i(!1)};return(0,a.useEffect)(()=>{let e=e=>{c.current&&!c.current.contains(e.target)&&i(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,r.jsxs)("div",{className:_().searchContainer,children:[(0,r.jsx)("button",{className:_().searchToggle,onClick:()=>{i(!s),s||setTimeout(()=>{c.current&&c.current.focus()},100)},"aria-label":"Toggle search",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("circle",{cx:"11",cy:"11",r:"8"}),(0,r.jsx)("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65"})]})}),(0,r.jsxs)("div",{className:"".concat(_().searchBar," ").concat(s?_().expanded:""),children:[(0,r.jsxs)("form",{onSubmit:t=>{t.preventDefault(),e.trim()&&(l.push("/products?search=".concat(encodeURIComponent(e))),i(!1))},ref:c,children:[(0,r.jsx)("input",{type:"text",placeholder:"Search for products...",value:e,onChange:e=>{let s=e.target.value;t(s),s.length>1?n(h.filter(e=>e.name.toLowerCase().includes(s.toLowerCase())).slice(0,5)):n([])},className:_().searchInput,"aria-label":"Search products"}),(0,r.jsx)("button",{type:"submit",className:_().searchButton,"aria-label":"Search",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("circle",{cx:"11",cy:"11",r:"8"}),(0,r.jsx)("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65"})]})})]}),o.length>0&&(0,r.jsx)("div",{className:_().suggestions,children:o.map(e=>(0,r.jsx)("div",{className:_().suggestionItem,onClick:()=>u(e.slug),children:e.name},e.id))})]})]})},f=()=>{let{data:e}=(0,i.useSession)(),t=(0,d.useRouter)(),[s,o]=(0,a.useState)(!1),[n,c]=(0,a.useState)(!1),h=(0,a.useRef)(null),[m,p]=(0,a.useState)(null),x=[{id:1,title:"iPhone Parts",url:"/products?category=iphone-parts",submenu:[{id:101,title:"By Model",items:[{id:1011,title:"iPhone 15 Series",url:"/products?category=iphone-parts&subcategory=iphone-parts/iphone-15"},{id:1012,title:"iPhone 14 Series",url:"/products?category=iphone-parts&subcategory=iphone-parts/iphone-14"},{id:1013,title:"iPhone 13 Series",url:"/products?category=iphone-parts&subcategory=iphone-parts/iphone-13"},{id:1014,title:"iPhone 12 Series",url:"/products?category=iphone-parts&subcategory=iphone-parts/iphone-12"},{id:1015,title:"iPhone 11 Series",url:"/products?category=iphone-parts&model=iphone-11"},{id:1016,title:"iPhone X Series",url:"/products?category=iphone-parts&model=iphone-x"},{id:1017,title:"iPhone 8 Series",url:"/products?category=iphone-parts&model=iphone-8"},{id:1018,title:"iPhone 7 Series",url:"/products?category=iphone-parts&model=iphone-7"}]},{id:102,title:"By Part Type",items:[{id:1021,title:"Screens & LCDs",url:"/products?category=iphone-parts&subcategory=iphone-parts/screens"},{id:1022,title:"Batteries",url:"/products?category=iphone-parts&subcategory=iphone-parts/batteries"},{id:1023,title:"Charging Ports",url:"/products?category=iphone-parts&subcategory=iphone-parts/charging-ports"},{id:1024,title:"Back Glass",url:"/products?category=iphone-parts&part=back-glass"},{id:1025,title:"Cameras",url:"/products?category=iphone-parts&part=camera"},{id:1026,title:"Speakers",url:"/products?category=iphone-parts&part=speaker"}]},{id:103,title:"Featured",items:[{id:1031,title:"New Arrivals",url:"/products?category=iphone-parts&sort=newest"},{id:1032,title:"Best Sellers",url:"/products?category=iphone-parts&sort=bestselling"},{id:1033,title:"Special Offers",url:"/products?category=iphone-parts&discount=true"}]}]},{id:2,title:"Samsung Parts",url:"/products?category=samsung-parts",submenu:[{id:201,title:"By Model",items:[{id:2011,title:"Galaxy S Series",url:"/products?category=samsung-parts&subcategory=samsung-parts/galaxy-s"},{id:2012,title:"Galaxy Note Series",url:"/products?category=samsung-parts&subcategory=samsung-parts/galaxy-note"},{id:2013,title:"Galaxy A Series",url:"/products?category=samsung-parts&subcategory=samsung-parts/galaxy-a"},{id:2014,title:"Galaxy Z Series",url:"/products?category=samsung-parts&model=galaxy-z"},{id:2015,title:"Galaxy Tab Series",url:"/products?category=samsung-parts&model=galaxy-tab"}]},{id:202,title:"By Part Type",items:[{id:2021,title:"Screens & LCDs",url:"/products?category=samsung-parts&subcategory=samsung-parts/screens"},{id:2022,title:"Batteries",url:"/products?category=samsung-parts&subcategory=samsung-parts/batteries"},{id:2023,title:"Charging Ports",url:"/products?category=samsung-parts&part=charging-port"},{id:2024,title:"Back Glass",url:"/products?category=samsung-parts&part=back-glass"},{id:2025,title:"Cameras",url:"/products?category=samsung-parts&part=camera"}]},{id:203,title:"Featured",items:[{id:2031,title:"New Arrivals",url:"/products?category=samsung-parts&sort=newest"},{id:2032,title:"Best Sellers",url:"/products?category=samsung-parts&sort=bestselling"},{id:2033,title:"Special Offers",url:"/products?category=samsung-parts&discount=true"}]}]},{id:3,title:"iPad Parts",url:"/products?category=ipad-parts",submenu:[{id:301,title:"By Model",items:[{id:3011,title:"iPad Pro",url:"/products?category=ipad-parts&subcategory=ipad-parts/ipad-pro"},{id:3012,title:"iPad Air",url:"/products?category=ipad-parts&subcategory=ipad-parts/ipad-air"},{id:3013,title:"iPad Mini",url:"/products?category=ipad-parts&subcategory=ipad-parts/ipad-mini"},{id:3014,title:"iPad Standard",url:"/products?category=ipad-parts&model=ipad-standard"}]},{id:302,title:"By Part Type",items:[{id:3021,title:"Screens & LCDs",url:"/products?category=ipad-parts&subcategory=ipad-parts/screens"},{id:3022,title:"Batteries",url:"/products?category=ipad-parts&part=battery"},{id:3023,title:"Charging Ports",url:"/products?category=ipad-parts&part=charging-port"},{id:3024,title:"Cameras",url:"/products?category=ipad-parts&part=camera"}]}]},{id:4,title:"MacBook Parts",url:"/products?category=macbook-parts",submenu:[{id:401,title:"By Model",items:[{id:4011,title:"MacBook Pro",url:"/products?category=macbook-parts&subcategory=macbook-parts/macbook-pro"},{id:4012,title:"MacBook Air",url:"/products?category=macbook-parts&subcategory=macbook-parts/macbook-air"}]},{id:402,title:"By Part Type",items:[{id:4021,title:"Screens",url:"/products?category=macbook-parts&subcategory=macbook-parts/screens"},{id:4022,title:"Keyboards",url:"/products?category=macbook-parts&subcategory=macbook-parts/keyboards"},{id:4023,title:"Batteries",url:"/products?category=macbook-parts&subcategory=macbook-parts/batteries"},{id:4024,title:"Trackpads",url:"/products?category=macbook-parts&part=trackpad"},{id:4025,title:"Logic Boards",url:"/products?category=macbook-parts&part=logic-board"}]}]},{id:5,title:"Repair Tools",url:"/products?category=repair-tools",submenu:[{id:501,title:"Tool Types",items:[{id:5011,title:"Tool Kits",url:"/products?category=repair-tools&subcategory=repair-tools/tool-kits"},{id:5012,title:"Screwdrivers",url:"/products?category=repair-tools&subcategory=repair-tools/screwdrivers"},{id:5013,title:"Heat Guns",url:"/products?category=repair-tools&subcategory=repair-tools/heat-guns"},{id:5014,title:"Soldering Equipment",url:"/products?category=repair-tools&subcategory=repair-tools/soldering"},{id:5015,title:"Adhesives & Tapes",url:"/products?category=repair-tools&subcategory=repair-tools/adhesives"}]},{id:502,title:"By Brand",items:[{id:5021,title:"iFixit",url:"/products?category=repair-tools&brand=ifixit"},{id:5022,title:"MDTS Tools",url:"/products?category=repair-tools&brand=mdts"},{id:5023,title:"JAKEMY",url:"/products?category=repair-tools&brand=jakemy"},{id:5024,title:"QIANLI",url:"/products?category=repair-tools&brand=qianli"}]},{id:503,title:"Featured",items:[{id:5031,title:"New Arrivals",url:"/products?category=repair-tools&sort=newest"},{id:5032,title:"Best Sellers",url:"/products?category=repair-tools&sort=bestselling"},{id:5033,title:"Special Offers",url:"/products?category=repair-tools&discount=true"}]}]}],_=()=>{o(!s)},f=e=>{p(e)};return(0,a.useEffect)(()=>{let e=e=>{h.current&&!h.current.contains(e.target)&&c(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,a.useEffect)(()=>{let e=()=>{o(!1)};return t.events.on("routeChangeStart",e),()=>{t.events.off("routeChangeStart",e)}},[t]),document.querySelector("header header"),(0,r.jsxs)("header",{className:"".concat(u().header," unified-header"),id:"unified-header",children:[(0,r.jsx)("div",{className:u().shippingCutoffContainer,children:(0,r.jsx)(g,{})}),(0,r.jsx)("div",{className:u().mainHeader,children:(0,r.jsxs)("div",{className:u().headerContainer,children:[(0,r.jsx)("div",{className:u().logo,children:(0,r.jsx)(l(),{href:"/",children:"MDTS"})}),(0,r.jsx)("div",{className:u().searchBarContainer,children:(0,r.jsx)(j,{})}),(0,r.jsx)("nav",{className:u().mainNav,children:(0,r.jsxs)("ul",{className:u().navList,children:[x.map(e=>(0,r.jsxs)("li",{className:"".concat(u().navItem," ").concat(u().megaMenuItem),onMouseEnter:()=>f(e.id),onMouseLeave:()=>f(null),children:[(0,r.jsxs)(l(),{href:e.url,className:t.asPath.includes(e.url)?u().active:"",children:[e.title,(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:u().chevronDown,children:(0,r.jsx)("polyline",{points:"6 9 12 15 18 9"})})]}),(0,r.jsx)("div",{className:"".concat(u().megaMenu," ").concat(m===e.id?u().active:""),children:(0,r.jsx)("div",{className:u().megaMenuContainer,children:e.submenu.map(e=>(0,r.jsxs)("div",{className:u().megaMenuColumn,children:[(0,r.jsx)("h3",{className:u().megaMenuTitle,children:e.title}),(0,r.jsx)("ul",{className:u().megaMenuList,children:e.items.map(e=>(0,r.jsx)("li",{className:u().megaMenuItem,children:(0,r.jsx)(l(),{href:e.url,children:e.title})},e.id))})]},e.id))})})]},e.id)),(0,r.jsx)("li",{className:u().navItem,children:(0,r.jsx)(l(),{href:"/lcd-buyback",className:"/lcd-buyback"===t.pathname?u().active:"",children:"LCD Buyback"})}),(0,r.jsx)("li",{className:u().navItem,children:(0,r.jsx)(l(),{href:"/gapp",className:"/gapp"===t.pathname?u().active:"",children:"Apple Parts Program"})})]})}),(0,r.jsxs)("div",{className:u().headerActions,children:[(0,r.jsxs)(l(),{href:"/cart",className:u().cartLink,children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("circle",{cx:"9",cy:"21",r:"1"}),(0,r.jsx)("circle",{cx:"20",cy:"21",r:"1"}),(0,r.jsx)("path",{d:"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"})]}),(0,r.jsx)("span",{children:"Cart"})]}),(0,r.jsxs)("div",{className:u().accountDropdown,ref:h,children:[(0,r.jsxs)("button",{className:u().accountButton,onClick:()=>{c(!n)},"aria-expanded":n,children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),(0,r.jsx)("circle",{cx:"12",cy:"7",r:"4"})]}),(0,r.jsx)("span",{children:"Account"})]}),n&&(0,r.jsx)("div",{className:u().dropdownMenu,children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:u().userInfo,children:(0,r.jsx)("span",{className:u().userName,children:e.user.name||e.user.email})}),(0,r.jsx)(l(),{href:"/user/profile",className:u().dropdownItem,children:"My Profile"}),(0,r.jsx)(l(),{href:"/user/orders",className:u().dropdownItem,children:"My Orders"}),(0,r.jsx)(l(),{href:"/wishlist",className:u().dropdownItem,children:"My Wishlist"}),(0,r.jsx)("div",{className:u().dropdownDivider}),(0,r.jsx)("button",{className:u().signOutButton,onClick:()=>(0,i.signOut)({callbackUrl:"/"}),children:"Sign Out"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l(),{href:"/auth/signin",className:u().dropdownItem,children:"Sign In"}),(0,r.jsx)(l(),{href:"/auth/register",className:u().dropdownItem,children:"Register"})]})})]}),(0,r.jsx)("button",{className:u().mobileMenuToggle,onClick:_,"aria-label":"Toggle mobile menu",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,r.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("line",{x1:"3",y1:"12",x2:"21",y2:"12"}),(0,r.jsx)("line",{x1:"3",y1:"6",x2:"21",y2:"6"}),(0,r.jsx)("line",{x1:"3",y1:"18",x2:"21",y2:"18"})]})})})]})]})}),(0,r.jsx)("div",{className:"".concat(u().mobileMenu," ").concat(s?u().open:""),children:(0,r.jsxs)("div",{className:u().mobileMenuContent,children:[(0,r.jsxs)("div",{className:u().mobileMenuHeader,children:[(0,r.jsx)("div",{className:u().mobileLogo,children:(0,r.jsx)(l(),{href:"/",children:"MDTS"})}),(0,r.jsx)("button",{className:u().mobileCloseButton,onClick:_,"aria-label":"Close menu",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,r.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),(0,r.jsx)("div",{className:u().mobileSearch,children:(0,r.jsx)(j,{})}),(0,r.jsxs)("nav",{className:u().mobileNav,children:[(0,r.jsxs)("div",{className:u().mobileNavSection,children:[(0,r.jsx)("h3",{className:u().mobileNavTitle,children:"Main Menu"}),(0,r.jsxs)("ul",{className:u().mobileNavList,children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/",children:"Home"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/lcd-buyback",children:"LCD Buyback"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/gapp",children:"Apple Parts Program"})})]})]}),x.map(e=>(0,r.jsxs)("div",{className:u().mobileNavSection,children:[(0,r.jsx)("h3",{className:u().mobileNavTitle,children:e.title}),(0,r.jsxs)("ul",{className:u().mobileNavList,children:[(0,r.jsx)("li",{children:(0,r.jsxs)(l(),{href:e.url,children:["All ",e.title]})}),e.submenu.map(e=>e.items.slice(0,3).map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:e.url,children:e.title})},e.id)))]})]},e.id)),(0,r.jsxs)("div",{className:u().mobileNavSection,children:[(0,r.jsx)("h3",{className:u().mobileNavTitle,children:"Account"}),(0,r.jsx)("ul",{className:u().mobileNavList,children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/user/profile",children:"My Profile"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/user/orders",children:"My Orders"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/wishlist",children:"My Wishlist"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/cart",children:"My Cart"})}),(0,r.jsx)("li",{children:(0,r.jsx)("button",{className:u().mobileSignOutButton,onClick:()=>(0,i.signOut)({callbackUrl:"/"}),children:"Sign Out"})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/auth/signin",children:"Sign In"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/auth/register",children:"Register"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/cart",children:"Cart"})})]})})]})]})]})})]})},v=s(77052),y=s.n(v),b=s(94207),w=s.n(b),N=e=>{let{isOpen:t,onClose:s}=e,[i,o]=(0,a.useState)({name:"",email:"",phone:"",subject:"",message:""}),[n,c]=(0,a.useState)(!1),[l,d]=(0,a.useState)(null),h=e=>{let{name:t,value:s}=e.target;o(e=>({...e,[t]:s}))},u=async e=>{e.preventDefault(),c(!0),d(null);try{let e=await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!e.ok)throw Error("Failed to send message");await e.json(),o({name:"",email:"",phone:"",subject:"",message:""}),d({success:!0,message:"Your message has been sent successfully! We will get back to you soon."}),setTimeout(()=>{s&&s()},3e3)}catch(e){console.error("Error sending contact form:",e),d({success:!1,message:"Failed to send your message. Please try again or contact us directly."})}finally{c(!1)}};return t?(0,r.jsx)("div",{className:w().overlay,onClick:s,children:(0,r.jsxs)("div",{className:w().contactFormContainer,onClick:e=>e.stopPropagation(),children:[(0,r.jsx)("button",{className:w().closeButton,onClick:s,children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,r.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})}),(0,r.jsxs)("div",{className:w().contactFormHeader,children:[(0,r.jsx)("h2",{children:"Contact Us"}),(0,r.jsx)("p",{children:"Fill out the form below and we'll get back to you as soon as possible."})]}),l&&(0,r.jsx)("div",{className:"".concat(w().submitResult," ").concat(l.success?w().success:w().error),children:l.message}),(0,r.jsxs)("form",{className:w().contactForm,onSubmit:u,children:[(0,r.jsxs)("div",{className:w().formGroup,children:[(0,r.jsx)("label",{htmlFor:"name",children:"Full Name *"}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",value:i.name,onChange:h,required:!0,placeholder:"Your full name"})]}),(0,r.jsxs)("div",{className:w().formRow,children:[(0,r.jsxs)("div",{className:w().formGroup,children:[(0,r.jsx)("label",{htmlFor:"email",children:"Email Address *"}),(0,r.jsx)("input",{type:"email",id:"email",name:"email",value:i.email,onChange:h,required:!0,placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:w().formGroup,children:[(0,r.jsx)("label",{htmlFor:"phone",children:"Phone Number"}),(0,r.jsx)("input",{type:"tel",id:"phone",name:"phone",value:i.phone,onChange:h,placeholder:"(*************"})]})]}),(0,r.jsxs)("div",{className:w().formGroup,children:[(0,r.jsx)("label",{htmlFor:"subject",children:"Subject *"}),(0,r.jsx)("input",{type:"text",id:"subject",name:"subject",value:i.subject,onChange:h,required:!0,placeholder:"What is your inquiry about?"})]}),(0,r.jsxs)("div",{className:w().formGroup,children:[(0,r.jsx)("label",{htmlFor:"message",children:"Message *"}),(0,r.jsx)("textarea",{id:"message",name:"message",value:i.message,onChange:h,required:!0,placeholder:"Please provide details about your inquiry...",rows:5})]}),(0,r.jsx)("div",{className:w().formActions,children:(0,r.jsx)("button",{type:"submit",className:w().submitButton,disabled:n,children:n?"Sending...":"Send Message"})})]}),(0,r.jsxs)("div",{className:w().contactInfo,children:[(0,r.jsxs)("div",{className:w().contactMethod,children:[(0,r.jsx)("div",{className:w().contactIcon,children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,r.jsx)("path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Phone"}),(0,r.jsx)("p",{children:"+****************"})]})]}),(0,r.jsxs)("div",{className:w().contactMethod,children:[(0,r.jsx)("div",{className:w().contactIcon,children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("path",{d:"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"}),(0,r.jsx)("polyline",{points:"22,6 12,13 2,6"})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Email"}),(0,r.jsx)("p",{children:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{className:w().contactMethod,children:[(0,r.jsx)("div",{className:w().contactIcon,children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),(0,r.jsx)("circle",{cx:"12",cy:"10",r:"3"})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Address"}),(0,r.jsx)("p",{children:"Vienna, VA 22182"})]})]}),(0,r.jsxs)("div",{className:w().contactMethod,children:[(0,r.jsx)("div",{className:w().contactIcon,children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,r.jsx)("polyline",{points:"12 6 12 12 16 14"})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Hours"}),(0,r.jsx)("p",{children:"Mon-Fri 9AM-10PM EST"})]})]})]})]})}):null},C=()=>{let[e,t]=(0,a.useState)(!1),[s,i]=(0,a.useState)("en"),[o,n]=(0,a.useState)("USD");return(0,a.useEffect)(()=>{let e=localStorage.getItem("preferredLanguage"),t=localStorage.getItem("preferredCurrency");e&&i(e),t&&n(t)},[]),(0,r.jsxs)("footer",{className:y().footerContainer,id:"footer-top",children:[(0,r.jsxs)("div",{"aria-label":"footer-main",className:y().footer,children:[(0,r.jsxs)("div",{className:y().fShippingMethodSection,children:[(0,r.jsxs)("div",{className:y().countryTypeSection,children:[(0,r.jsx)("div",{className:y().flagBox,children:(0,r.jsx)("img",{src:"/images/flags/us-flag.svg",alt:"US Flag",width:"24",height:"16"})}),(0,r.jsx)("div",{className:y().selectLangBox,"aria-label":"Select-language-block",children:(0,r.jsx)("select",{name:"language",className:y().selLang,value:s,onChange:e=>{let t=e.target.value;i(t),localStorage.setItem("preferredLanguage",t)},children:[{code:"en",name:"English"},{code:"es",name:"Espa\xf1ol"},{code:"fr",name:"Fran\xe7ais"},{code:"de",name:"Deutsch"},{code:"zh",name:"中文"},{code:"ar",name:"العربية"}].map(e=>(0,r.jsx)("option",{value:e.code,children:e.name},e.code))})}),(0,r.jsx)("div",{className:y().selectCurrencyBox,"aria-label":"Select-Currency-block",children:(0,r.jsx)("select",{name:"currency",className:y().selCurr,id:"selected_currency",value:o,onChange:e=>{let t=e.target.value;n(t),localStorage.setItem("preferredCurrency",t)},children:[{code:"USD",symbol:"$",name:"US Dollar"},{code:"EUR",symbol:"€",name:"Euro"},{code:"GBP",symbol:"\xa3",name:"British Pound"},{code:"CAD",symbol:"C$",name:"Canadian Dollar"},{code:"AUD",symbol:"A$",name:"Australian Dollar"},{code:"JPY",symbol:"\xa5",name:"Japanese Yen"}].map(e=>(0,r.jsx)("option",{value:e.code,children:e.name},e.code))})})]}),(0,r.jsxs)("div",{className:y().shippingMethodsType,children:[(0,r.jsx)("p",{"aria-label":"Shipping-methods",children:"Shipping Methods:"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{className:y().fedex,children:(0,r.jsx)("img",{src:"/images/shipping/fedex.svg",alt:"FedEx",width:"40",height:"24"})}),(0,r.jsx)("li",{className:y().ups,children:(0,r.jsx)("img",{src:"/images/shipping/ups.svg",alt:"UPS",width:"40",height:"24"})}),(0,r.jsx)("li",{className:y().frame,children:(0,r.jsx)("img",{src:"/images/shipping/usps.svg",alt:"USPS",width:"40",height:"24"})})]})]}),(0,r.jsxs)("div",{className:y().ftCerificateSection,children:[(0,r.jsx)("h5",{children:"Certifications"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:(0,r.jsx)("img",{width:"36",height:"45",src:"/images/certificates/r2-v3.svg",alt:"R2 Certificate"})}),(0,r.jsx)("li",{children:(0,r.jsx)("img",{width:"34",height:"48",src:"/images/certificates/certificate-001.svg",alt:"NSAI - Health & Safety"})}),(0,r.jsx)("li",{children:(0,r.jsx)("img",{width:"33",height:"48",src:"/images/certificates/certificate-002.svg",alt:"NSAI - Quality"})}),(0,r.jsx)("li",{children:(0,r.jsx)("img",{width:"34",height:"48",src:"/images/certificates/certificate-003.svg",alt:"NSAI - Environment"})})]})]})]}),(0,r.jsxs)("div",{className:y().newFooterMain,children:[(0,r.jsxs)("div",{className:"".concat(y().footerBoxes2," ").concat(y().firstCol),children:[(0,r.jsx)("h4",{className:y().showNav,children:"Shop"}),(0,r.jsxs)("ul",{className:"".concat(y().accordion," ").concat(y().footerLinks2),children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories/iphone-parts",children:"iPhone Parts"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories/samsung-parts",children:"Samsung Parts"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories/ipad-parts",children:"iPad Parts"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories/macbook-parts",children:"MacBook Parts"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories/repair-tools",children:"Repair Tools"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories/other-devices",children:"Other Devices"})})]})]}),(0,r.jsxs)("div",{className:"".concat(y().footerBoxes2," ").concat(y().secondCol),children:[(0,r.jsx)("h4",{className:y().showNav,children:"Information"}),(0,r.jsxs)("ul",{className:"".concat(y().accordion," ").concat(y().footerLinks2),children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/about",children:"About Us"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/blog",children:"Blog"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/quality-standards",children:"Quality Standards"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/return-policy",children:"Return Policy"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/terms",children:"Terms and Conditions"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/privacy",children:"Privacy Policy"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/trademark-disclaimer",children:"Trademark Disclaimer"})})]})]}),(0,r.jsxs)("div",{className:"".concat(y().footerBoxes2," ").concat(y().secondCol),children:[(0,r.jsx)("h4",{className:y().showNav,children:"Services"}),(0,r.jsxs)("ul",{className:"".concat(y().accordion," ").concat(y().footerLinks2),children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/lcd-buyback",children:"LCD Buyback"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/account",children:"My Account"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/device-grading",children:"Device Grading"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/shipping",children:"Shipping"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/trade-off",children:"Trade-Off"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/gapp",children:"Apple Parts Program"})})]})]}),(0,r.jsxs)("div",{className:"".concat(y().footerBoxes2," ").concat(y().fourthCol),children:[(0,r.jsx)("h4",{className:y().showNav,children:"Need Help?"}),(0,r.jsxs)("ul",{className:"".concat(y().accordion," ").concat(y().footerLinks2),children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/contact",children:"Contact Us"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/faq",children:"FAQ"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/help-center",children:"Help Center"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/payment-methods",children:"Payment Methods"})}),(0,r.jsx)("li",{className:y().contactUsMain,children:(0,r.jsx)("button",{onClick:()=>{t(!0)},className:y().contactUsBtn,children:"Contact us!"})})]})]}),(0,r.jsxs)("div",{className:"".concat(y().footerBoxes2," ").concat(y().fifthColumn," ").concat(y().shippingMethodsMobile),children:[(0,r.jsx)("h4",{className:y().showNav,children:"Shipping Methods:"}),(0,r.jsxs)("ul",{className:y().accordion,children:[(0,r.jsx)("li",{className:y().fedex,children:(0,r.jsx)("img",{src:"/images/shipping/fedex.svg",alt:"FedEx",width:"40",height:"24"})}),(0,r.jsx)("li",{className:y().ups,children:(0,r.jsx)("img",{src:"/images/shipping/ups.svg",alt:"UPS",width:"40",height:"24"})}),(0,r.jsx)("li",{className:y().frame,children:(0,r.jsx)("img",{src:"/images/shipping/usps.svg",alt:"USPS",width:"40",height:"24"})})]})]})]}),(0,r.jsx)("div",{className:y().footerBoxes2Mobile,children:(0,r.jsxs)("div",{className:y().fResponsivePart,children:[(0,r.jsxs)("div",{className:y().fLeftPart,children:[(0,r.jsx)("span",{children:"Warehouse"}),(0,r.jsxs)("div",{className:y().conDetailW,children:[(0,r.jsx)("div",{className:y().flagBox2,children:(0,r.jsx)("img",{src:"/images/flags/us-flag.svg",alt:"US Flag",width:"24",height:"16"})}),(0,r.jsx)("span",{className:y().usaTxt,children:"USA"})]})]}),(0,r.jsxs)("div",{className:y().fRightPart,children:[(0,r.jsx)("span",{className:y().contactTxt,children:"Contact"}),(0,r.jsx)("span",{className:y().contactNubTxt,children:"+****************"})]})]})})]}),(0,r.jsxs)("div",{"aria-label":"footer-main",className:"".concat(y().footer," ").concat(y().bootomFooter),children:[(0,r.jsxs)("div",{className:"".concat(y().leftCol," ").concat(y().withCerti),children:[(0,r.jsxs)("div",{className:y().footerTrademark,children:[(0,r.jsx)("div",{className:y().logoTrademark,children:(0,r.jsx)("img",{src:"/images/logo-dark.svg",alt:"MDTS Tech",width:"147",height:"35"})}),(0,r.jsxs)("span",{className:"".concat(y().follow2," ").concat(y().hideDeskOllow),children:[(0,r.jsx)("b",{children:"FOLLOW"})," MDTS TECH"]}),(0,r.jsxs)("ul",{className:y().socialMediaPart,children:[(0,r.jsx)("li",{className:y().facebook,children:(0,r.jsx)("a",{"aria-label":"facebook",href:"https://www.facebook.com/mdtstech",title:"facebook",target:"_blank",rel:"noopener noreferrer",children:"\xa0"})}),(0,r.jsx)("li",{className:y().twitter,children:(0,r.jsx)("a",{"aria-label":"twitter",href:"https://twitter.com/mdtstech",title:"twitter",target:"_blank",rel:"noopener noreferrer",children:"\xa0"})}),(0,r.jsx)("li",{className:y().linkedin,children:(0,r.jsx)("a",{"aria-label":"linkedin",href:"https://www.linkedin.com/in/fitzgerald-amaniampong-0a2962324/",title:"linkedin",target:"_blank",rel:"noopener noreferrer",children:"\xa0"})}),(0,r.jsx)("li",{className:y().instagram,children:(0,r.jsx)("a",{"aria-label":"instagram",href:"https://www.instagram.com/mdtstech.store/",title:"instagram",target:"_blank",rel:"noopener noreferrer",children:"\xa0"})}),(0,r.jsx)("li",{className:y().youtube,children:(0,r.jsx)("a",{"aria-label":"youtube",href:"https://youtube.com/channel/mdtstech",title:"youtube",target:"_blank",rel:"noopener noreferrer",children:"\xa0"})})]})]}),(0,r.jsxs)("div",{className:y().footerTrademark2,children:[(0,r.jsx)("p",{children:"All trademarks are properties of their respective holders. MDTS Tech does not own or make claim to those trademarks used on this website in which it is not the holder."}),(0,r.jsxs)("address",{children:["\xa9 ",new Date().getFullYear()," MIDAS TECHNICAL SOLUTIONS"]})]})]}),(0,r.jsx)("div",{className:y().rightCol,children:(0,r.jsx)("div",{className:"".concat(y().paymentMethodsPart," ").concat(y().paymentMethodsMobile),children:(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{className:y().amex,children:(0,r.jsx)("img",{src:"/images/payments/amex.svg",alt:"American Express",width:"40",height:"24"})}),(0,r.jsx)("li",{className:y().masterCard,children:(0,r.jsx)("img",{src:"/images/payments/mastercard.svg",alt:"Mastercard",width:"40",height:"24"})}),(0,r.jsx)("li",{className:y().venmo,children:(0,r.jsx)("img",{src:"/images/payments/venmo.svg",alt:"Venmo",width:"40",height:"24"})}),(0,r.jsx)("li",{className:y().paypal,children:(0,r.jsx)("img",{src:"/images/payments/paypal.svg",alt:"PayPal",width:"40",height:"24"})}),(0,r.jsx)("li",{className:y().paypalCredit,children:(0,r.jsx)("img",{src:"/images/payments/paypal-credit.svg",alt:"PayPal Credit",width:"40",height:"24"})}),(0,r.jsx)("li",{className:y().visa,children:(0,r.jsx)("img",{src:"/images/payments/visa.svg",alt:"Visa",width:"40",height:"24"})}),(0,r.jsx)("li",{className:y().discover,children:(0,r.jsx)("img",{src:"/images/payments/discover.svg",alt:"Discover",width:"40",height:"24"})}),(0,r.jsx)("li",{className:y().creditKey,children:(0,r.jsx)("img",{src:"/images/payments/credit-key.svg",alt:"Credit Key",width:"40",height:"24"})}),(0,r.jsx)("li",{className:y().wireTransfer,children:(0,r.jsx)("img",{src:"/images/payments/wire-transfer.svg",alt:"Wire Transfer",width:"40",height:"24"})})]})})})]}),(0,r.jsx)(N,{isOpen:e,onClose:()=>{t(!1)}})]})},S=s(23745),k=s.n(S);function M(){let[e,t]=(0,a.useState)(!1),s=(0,d.useRouter)();(0,a.useEffect)(()=>{if(!localStorage.getItem("hasSeenGradingPopup")){let e=setTimeout(()=>{t(!0)},5e3);return()=>clearTimeout(e)}},[]);let i=()=>{t(!1),localStorage.setItem("hasSeenGradingPopup","true")};return e?(0,r.jsx)("div",{className:k().popupOverlay,children:(0,r.jsxs)("div",{className:k().popupContainer,children:[(0,r.jsx)("button",{className:k().closeButton,onClick:i,children:"\xd7"}),(0,r.jsxs)("div",{className:k().popupHeader,children:[(0,r.jsx)("h2",{children:"Device Grading System"}),(0,r.jsx)("p",{children:"Learn about our standardized grading system for devices and parts"})]}),(0,r.jsxs)("div",{className:k().popupContent,children:[(0,r.jsxs)("div",{className:k().gradingCards,children:[(0,r.jsxs)("div",{className:k().gradingCard,children:[(0,r.jsx)("div",{className:k().gradeBadge,style:{backgroundColor:"#4CAF50"},children:"Grade A"}),(0,r.jsx)("h3",{children:"Excellent"}),(0,r.jsx)("p",{children:"Like new condition with no visible wear"})]}),(0,r.jsxs)("div",{className:k().gradingCard,children:[(0,r.jsx)("div",{className:k().gradeBadge,style:{backgroundColor:"#2196F3"},children:"Grade B"}),(0,r.jsx)("h3",{children:"Good"}),(0,r.jsx)("p",{children:"Minor wear but fully functional"})]}),(0,r.jsxs)("div",{className:k().gradingCard,children:[(0,r.jsx)("div",{className:k().gradeBadge,style:{backgroundColor:"#FFC107"},children:"Grade C"}),(0,r.jsx)("h3",{children:"Fair"}),(0,r.jsx)("p",{children:"Noticeable wear but working properly"})]}),(0,r.jsxs)("div",{className:k().gradingCard,children:[(0,r.jsx)("div",{className:k().gradeBadge,style:{backgroundColor:"#F44336"},children:"Grade D"}),(0,r.jsx)("h3",{children:"Poor"}),(0,r.jsx)("p",{children:"Significant wear with possible issues"})]})]}),(0,r.jsxs)("div",{className:k().popupInfo,children:[(0,r.jsx)("p",{children:"At MDTS, we use a standardized grading system to evaluate the condition of devices and parts. This ensures transparency and fair pricing for all our customers."}),(0,r.jsx)("p",{children:"Whether you're buying a refurbished device or selling through our LCD Buyback Program, understanding our grading system helps you make informed decisions."})]})]}),(0,r.jsxs)("div",{className:k().popupFooter,children:[(0,r.jsx)("button",{className:k().learnMoreButton,onClick:()=>{s.push("/device-grading"),i()},children:"Learn More"}),(0,r.jsx)(l(),{href:"/lcd-buyback",className:k().buybackButton,children:"LCD Buyback Program"})]})]})}):null}var B=s(46754),F=s.n(B);class I extends a.Component{static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,t){console.error("Error caught by ErrorBoundary:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?(0,r.jsx)("div",{className:F().errorContainer,children:(0,r.jsxs)("div",{className:F().errorContent,children:[(0,r.jsx)("h2",{className:F().errorTitle,children:"Something went wrong"}),(0,r.jsx)("p",{className:F().errorMessage,children:"We're sorry, but there was an error loading this component."}),this.props.showDetails&&this.state.error&&(0,r.jsxs)("div",{className:F().errorDetails,children:[(0,r.jsx)("p",{children:this.state.error.toString()}),(0,r.jsx)("pre",{className:F().errorStack,children:this.state.errorInfo&&this.state.errorInfo.componentStack})]}),(0,r.jsx)("button",{className:F().errorButton,onClick:()=>window.location.reload(),children:"Reload Page"})]})}):this.props.children}constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null}}}s(25675);var U=s(67174),P=s.n(U),T=()=>{let{data:e}=(0,i.useSession)(),[t,s]=(0,a.useState)(!1),[o,n]=(0,a.useState)([]),[c,l]=(0,a.useState)(""),[d,h]=(0,a.useState)(!1),[u,m]=(0,a.useState)(null),p=(0,a.useRef)(null);(0,a.useEffect)(()=>{let e=localStorage.getItem("chatbotConversationId"),t=localStorage.getItem("chatbotMessages");e&&t?(m(e),n(JSON.parse(t))):g()},[e]),(0,a.useEffect)(()=>{_()},[o]);let g=async()=>{try{var t;let s=await fetch("/api/chatbot/conversation",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:(null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.id)||"anonymous"})}),r=await s.json();if(r.success){m(r.conversationId),localStorage.setItem("chatbotConversationId",r.conversationId);let e={id:Date.now(),role:"assistant",content:"Hello! I'm your MDTS virtual assistant. How can I help you today? I can assist with product information, order tracking, returns, and more.",timestamp:new Date().toISOString()};n([e]),localStorage.setItem("chatbotMessages",JSON.stringify([e]))}}catch(t){console.error("Error initializing conversation:",t);let e={id:Date.now(),role:"assistant",content:"Hello! I'm your MDTS virtual assistant. How can I help you today?",timestamp:new Date().toISOString()};n([e]),localStorage.setItem("chatbotMessages",JSON.stringify([e]))}},x=async t=>{if(t.preventDefault(),!c.trim())return;let s=[...o,{id:Date.now(),role:"user",content:c,timestamp:new Date().toISOString()}];n(s),localStorage.setItem("chatbotMessages",JSON.stringify(s)),l(""),h(!0);try{var r;let t=await fetch("/api/chatbot/message",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversationId:u,message:c,userId:(null==e?void 0:null===(r=e.user)||void 0===r?void 0:r.id)||"anonymous"})}),a=await t.json();if(h(!1),a.success){let e={id:Date.now()+1,role:"assistant",content:a.reply,timestamp:new Date().toISOString()},t=[...s,e];n(t),localStorage.setItem("chatbotMessages",JSON.stringify(t))}else{let e={id:Date.now()+1,role:"assistant",content:"I'm sorry, I'm having trouble processing your request right now. Please try again later or contact our support <NAME_EMAIL>.",timestamp:new Date().toISOString()},t=[...s,e];n(t),localStorage.setItem("chatbotMessages",JSON.stringify(t))}}catch(t){console.error("Error sending message:",t),h(!1);let e=[...s,{id:Date.now()+1,role:"assistant",content:"I'm sorry, I'm having trouble connecting to our servers. Please check your internet connection and try again.",timestamp:new Date().toISOString()}];n(e),localStorage.setItem("chatbotMessages",JSON.stringify(e))}},_=()=>{var e;null===(e=p.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})},j=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),f=e=>{if(!e)return"";let t=e.replace(/(https?:\/\/[^\s]+)/g,e=>'<a href="'.concat(e,'" target="_blank" rel="noopener noreferrer" class="').concat(P().messageLink,'">').concat(e,"</Link>")).replace(RegExp("(\\d+\\.\\s.*?)(?=\\n\\d+\\.|\\n\\n|$)","gs"),"<li>$1</li>").replace(/(^|\n)- (.*?)(?=\n-|\n\n|$)/gm,"$1<li>$2</li>").replace(/\n(#{1,3})\s(.*?)\n/g,"\n<h4>$2</h4>\n").replace(/\n\n/g,"</p><p>");return t.startsWith("<p>")||(t="<p>".concat(t,"</p>")),t=(t=t.replace(/\n(?![<])/g,"<br>")).replace(/(<li>.*?<\/li>)+/g,"<ul>$&</ul>"),(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:t}})};return(0,r.jsxs)("div",{className:P().chatbotContainer,children:[(0,r.jsxs)("button",{className:"".concat(P().chatbotToggle," ").concat(t?P().open:""),onClick:()=>s(!t),"aria-label":t?"Close chat":"Open chat",children:[t?(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,r.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}):(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,r.jsx)("path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"})}),!t&&(0,r.jsx)("span",{className:P().chatbotLabel,children:"Chat with us"})]}),(0,r.jsxs)("div",{className:"".concat(P().chatbotDialog," ").concat(t?P().open:""),children:[(0,r.jsxs)("div",{className:P().chatbotHeader,children:[(0,r.jsxs)("div",{className:P().chatbotInfo,children:[(0,r.jsx)("div",{className:P().chatbotAvatar,children:(0,r.jsx)("img",{src:"/images/chatbot-avatar.svg",alt:"MDTS Assistant"})}),(0,r.jsxs)("div",{className:P().chatbotDetails,children:[(0,r.jsx)("h3",{children:"MDTS Assistant"}),(0,r.jsx)("span",{className:P().chatbotStatus,children:"Online"})]})]}),(0,r.jsxs)("div",{className:P().chatbotActions,children:[(0,r.jsx)("button",{className:P().clearButton,onClick:()=>{localStorage.removeItem("chatbotConversationId"),localStorage.removeItem("chatbotMessages"),g()},"aria-label":"Clear conversation",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("polyline",{points:"3 6 5 6 21 6"}),(0,r.jsx)("path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"})]})}),(0,r.jsx)("button",{className:P().closeButton,onClick:()=>s(!1),"aria-label":"Close chat",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,r.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]})]}),(0,r.jsxs)("div",{className:P().chatbotMessages,children:[o.map(e=>(0,r.jsxs)("div",{className:"".concat(P().message," ").concat("user"===e.role?P().userMessage:P().botMessage),children:[(0,r.jsx)("div",{className:P().messageContent,children:f(e.content)}),(0,r.jsx)("div",{className:P().messageTimestamp,children:j(e.timestamp)})]},e.id)),d&&(0,r.jsx)("div",{className:"".concat(P().message," ").concat(P().botMessage),children:(0,r.jsxs)("div",{className:P().typingIndicator,children:[(0,r.jsx)("span",{}),(0,r.jsx)("span",{}),(0,r.jsx)("span",{})]})}),(0,r.jsx)("div",{ref:p})]}),(0,r.jsxs)("form",{className:P().chatbotInput,onSubmit:x,children:[(0,r.jsx)("input",{type:"text",value:c,onChange:e=>l(e.target.value),placeholder:"Type your message here...","aria-label":"Type your message"}),(0,r.jsx)("button",{type:"submit",disabled:!c.trim()||d,"aria-label":"Send message",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("line",{x1:"22",y1:"2",x2:"11",y2:"13"}),(0,r.jsx)("polygon",{points:"22 2 15 22 11 13 2 9 22 2"})]})})]}),(0,r.jsxs)("div",{className:P().chatbotFooter,children:[(0,r.jsx)("p",{children:"Powered by MDTS AI Assistant"}),(0,r.jsx)("p",{className:P().disclaimer,children:"This is an AI assistant. For urgent matters, please contact our support team directly."})]})]})]})},D=s(33461),L=s.n(D),H=e=>{let{phoneNumber:t="+12403510511",message:s="Hello! I have a question about your products."}=e,[i,o]=(0,a.useState)(!1),n=t.replace(/\D/g,""),c="https://wa.me/".concat(n,"?text=").concat(encodeURIComponent(s));return(0,r.jsxs)("div",{className:L().whatsappContainer,children:[(0,r.jsx)("a",{href:c,target:"_blank",rel:"noopener noreferrer",className:L().whatsappButton,"aria-label":"Contact us on WhatsApp",onMouseEnter:()=>{o(!0)},onMouseLeave:()=>{o(!1)},children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",children:(0,r.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"})})}),i&&(0,r.jsx)("div",{className:L().whatsappTooltip,children:"Chat with us on WhatsApp"})]})},A=s(67874),E=s.n(A);function O(e){let{children:t,title:s,description:a}=e,{data:o}=(0,i.useSession)(),c=s?"".concat(s," | MDTS"):"MDTS",l=a||"MDTS offers high-quality mobile device parts and repair tools for professionals and DIY enthusiasts.";return(0,r.jsxs)("div",{className:E().layout,children:[(0,r.jsxs)(n(),{children:[(0,r.jsx)("title",{children:c}),(0,r.jsx)("meta",{name:"description",content:l}),(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0"}),(0,r.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,r.jsx)("meta",{property:"og:type",content:"website"}),(0,r.jsx)("meta",{property:"og:title",content:c}),(0,r.jsx)("meta",{property:"og:description",content:l}),(0,r.jsx)("meta",{property:"og:image",content:"/images/og-image.jpg"}),(0,r.jsx)("meta",{property:"twitter:card",content:"summary_large_image"}),(0,r.jsx)("meta",{property:"twitter:title",content:c}),(0,r.jsx)("meta",{property:"twitter:description",content:l}),(0,r.jsx)("meta",{property:"twitter:image",content:"/images/og-image.jpg"})]}),(0,r.jsx)(I,{children:(0,r.jsx)(f,{})}),(0,r.jsx)("main",{className:E().main,children:(0,r.jsx)(I,{children:t})}),(0,r.jsx)(I,{children:(0,r.jsx)(C,{})}),!o&&(0,r.jsx)(I,{children:(0,r.jsx)(M,{})}),(0,r.jsx)(I,{children:(0,r.jsx)(T,{})}),(0,r.jsx)(I,{children:(0,r.jsx)(H,{})})]})}},67174:function(e){e.exports={chatbotContainer:"Chatbot_chatbotContainer__LgJ79",chatbotToggle:"Chatbot_chatbotToggle__x_p_Z",open:"Chatbot_open__J2QkH",chatbotLabel:"Chatbot_chatbotLabel__vgE0h",chatbotDialog:"Chatbot_chatbotDialog__1lWzf",chatbotHeader:"Chatbot_chatbotHeader__M4nC9",chatbotInfo:"Chatbot_chatbotInfo__RD6yx",chatbotAvatar:"Chatbot_chatbotAvatar__wu6ec",chatbotDetails:"Chatbot_chatbotDetails__CU6KI",chatbotStatus:"Chatbot_chatbotStatus__kqm0q",chatbotActions:"Chatbot_chatbotActions__xrge0",clearButton:"Chatbot_clearButton__V_lS0",closeButton:"Chatbot_closeButton__xb3F4",chatbotMessages:"Chatbot_chatbotMessages__qDMZR",message:"Chatbot_message__Za7k2",messageAppear:"Chatbot_messageAppear__vZgFH",userMessage:"Chatbot_userMessage__F95Dk",botMessage:"Chatbot_botMessage__RThCL",messageContent:"Chatbot_messageContent__HbMsf",messageLink:"Chatbot_messageLink__7NKhJ",messageTimestamp:"Chatbot_messageTimestamp__TDjC4",typingIndicator:"Chatbot_typingIndicator__0g_o3",typing:"Chatbot_typing__Zidue",chatbotInput:"Chatbot_chatbotInput__2gkrC",chatbotFooter:"Chatbot_chatbotFooter__8xozd",disclaimer:"Chatbot_disclaimer__uI4MM"}},94207:function(e){e.exports={overlay:"ContactForm_overlay__69lgj",fadeIn:"ContactForm_fadeIn__rA5Nz",contactFormContainer:"ContactForm_contactFormContainer__PmeIE",slideUp:"ContactForm_slideUp__CdWzy",closeButton:"ContactForm_closeButton__YZC_1",contactFormHeader:"ContactForm_contactFormHeader__qhdrt",contactForm:"ContactForm_contactForm__HGyre",formGroup:"ContactForm_formGroup__wKzyj",formRow:"ContactForm_formRow__U6INC",formActions:"ContactForm_formActions__biUKu",submitButton:"ContactForm_submitButton__Wl6k0",submitResult:"ContactForm_submitResult__cKL99",success:"ContactForm_success__rER0q",error:"ContactForm_error__qDWds",contactInfo:"ContactForm_contactInfo__GONlQ",contactMethod:"ContactForm_contactMethod__UHVtv",contactIcon:"ContactForm_contactIcon__JQ8O_"}},46754:function(e){e.exports={errorContainer:"ErrorBoundary_errorContainer__XQ_4F",errorContent:"ErrorBoundary_errorContent__Jfru2",errorTitle:"ErrorBoundary_errorTitle__qsJX_",errorMessage:"ErrorBoundary_errorMessage__kVxeG",errorDetails:"ErrorBoundary_errorDetails__I_G7y",errorStack:"ErrorBoundary_errorStack__mWVwf",errorButton:"ErrorBoundary_errorButton__YkNRJ"}},67874:function(e){e.exports={layout:"Layout_layout__6J70X",main:"Layout_main__k2ohw"}},27936:function(e){e.exports={searchContainer:"SearchBar_searchContainer__xAbis",searchToggle:"SearchBar_searchToggle__AOFsS",searchBar:"SearchBar_searchBar___9vxM",expanded:"SearchBar_expanded__9_xYA",searchInput:"SearchBar_searchInput__uNsu6",searchButton:"SearchBar_searchButton__uY0x2",suggestions:"SearchBar_suggestions__I2RVH",suggestionItem:"SearchBar_suggestionItem__jRKj0"}},48240:function(e){e.exports={shippingCutoff:"ShippingCutoff_shippingCutoff__ZLFXv",cutoffMessage:"ShippingCutoff_cutoffMessage__hxXKO",cutoffTimer:"ShippingCutoff_cutoffTimer__m4Bzj",timerDisplay:"ShippingCutoff_timerDisplay__a057u",timerDigits:"ShippingCutoff_timerDigits__55Out",shippingMethod:"ShippingCutoff_shippingMethod__CzQHV",methodName:"ShippingCutoff_methodName__wL3cQ",freeShipping:"ShippingCutoff_freeShipping__EI_rM"}},77052:function(e){e.exports={footerContainer:"UnifiedFooter_footerContainer__Ecq4K",footer:"UnifiedFooter_footer__wHTXa",fShippingMethodSection:"UnifiedFooter_fShippingMethodSection__0LTWN",countryTypeSection:"UnifiedFooter_countryTypeSection__3_W72",flagBox:"UnifiedFooter_flagBox__NtyyZ",selectCurrencyBox:"UnifiedFooter_selectCurrencyBox__ejYaQ",selectLangBox:"UnifiedFooter_selectLangBox__Hq1E_",selCurr:"UnifiedFooter_selCurr__fjuw4",selLang:"UnifiedFooter_selLang__Sz3D6",shippingMethodsType:"UnifiedFooter_shippingMethodsType__iREqL",fedex:"UnifiedFooter_fedex__VVRGB",frame:"UnifiedFooter_frame__xPvfQ",ups:"UnifiedFooter_ups__suBiN",ftCerificateSection:"UnifiedFooter_ftCerificateSection__cxH36",newFooterMain:"UnifiedFooter_newFooterMain__1ElGw",footerBoxes2:"UnifiedFooter_footerBoxes2__i8PEK",showNav:"UnifiedFooter_showNav__t4Xzs",footerLinks2:"UnifiedFooter_footerLinks2__c1DKs",contactUsBtn:"UnifiedFooter_contactUsBtn__dyvE1",footerBoxes2Mobile:"UnifiedFooter_footerBoxes2Mobile__Cto4A",bootomFooter:"UnifiedFooter_bootomFooter__wBjrP",leftCol:"UnifiedFooter_leftCol__n0P60",footerTrademark:"UnifiedFooter_footerTrademark__baeDp",logoTrademark:"UnifiedFooter_logoTrademark__8wzP5",follow2:"UnifiedFooter_follow2__sta7A",socialMediaPart:"UnifiedFooter_socialMediaPart__I0Jnu",facebook:"UnifiedFooter_facebook__kIOuP",instagram:"UnifiedFooter_instagram__2nVop",linkedin:"UnifiedFooter_linkedin__wjy35",twitter:"UnifiedFooter_twitter__yuE1r",youtube:"UnifiedFooter_youtube__j31zn",footerTrademark2:"UnifiedFooter_footerTrademark2__mldin",rightCol:"UnifiedFooter_rightCol__3amtU",paymentMethodsPart:"UnifiedFooter_paymentMethodsPart__d4vhd",amex:"UnifiedFooter_amex__W_k8a",creditKey:"UnifiedFooter_creditKey__Itr8P",discover:"UnifiedFooter_discover__SFqxH",masterCard:"UnifiedFooter_masterCard__GN57U",paypal:"UnifiedFooter_paypal__vtfbf",paypalCredit:"UnifiedFooter_paypalCredit__gbiUx",venmo:"UnifiedFooter_venmo__kJZPY",visa:"UnifiedFooter_visa__5Oxqq",wireTransfer:"UnifiedFooter_wireTransfer__Qh7JD",fResponsivePart:"UnifiedFooter_fResponsivePart__cNrOc",fLeftPart:"UnifiedFooter_fLeftPart__8V41t",fRightPart:"UnifiedFooter_fRightPart__MOss3",conDetailW:"UnifiedFooter_conDetailW__QQymf",flagBox2:"UnifiedFooter_flagBox2__i1M2M",usaTxt:"UnifiedFooter_usaTxt__6Qlen",contactTxt:"UnifiedFooter_contactTxt__dWc5w",contactNubTxt:"UnifiedFooter_contactNubTxt__EsN8m",hideDeskOllow:"UnifiedFooter_hideDeskOllow__TwGr_",shippingMethodsMobile:"UnifiedFooter_shippingMethodsMobile__fIUEO"}},40282:function(e){e.exports={header:"UnifiedHeader_header__ZinHi",mainHeader:"UnifiedHeader_mainHeader__OBind",headerContainer:"UnifiedHeader_headerContainer__cHzsV",logo:"UnifiedHeader_logo___0nte",navItem:"UnifiedHeader_navItem__W3hzd",active:"UnifiedHeader_active__NfTfe",headerActions:"UnifiedHeader_headerActions__wetPJ",cartLink:"UnifiedHeader_cartLink__mUOy2",accountButton:"UnifiedHeader_accountButton__16EZ6",dropdownMenu:"UnifiedHeader_dropdownMenu__baOaX",userInfo:"UnifiedHeader_userInfo__BR9cP",dropdownItem:"UnifiedHeader_dropdownItem__EedwG",dropdownDivider:"UnifiedHeader_dropdownDivider__uqJ4f",signOutButton:"UnifiedHeader_signOutButton__hR_ej",mainNav:"UnifiedHeader_mainNav__aHNwJ",mobileMenuToggle:"UnifiedHeader_mobileMenuToggle__eC0Cd",navList:"UnifiedHeader_navList__1rGgB",actionLinks:"UnifiedHeader_actionLinks__jSrLu",accountDropdown:"UnifiedHeader_accountDropdown__6NTA8",chevron:"UnifiedHeader_chevron__GSbuW",open:"UnifiedHeader_open__8Z1FU",fadeIn:"UnifiedHeader_fadeIn__NZIMc",userName:"UnifiedHeader_userName__IRNW7",shippingCutoffContainer:"UnifiedHeader_shippingCutoffContainer__R_kRs",searchBarContainer:"UnifiedHeader_searchBarContainer__cBuGi",megaMenuItem:"UnifiedHeader_megaMenuItem__lggYf",chevronDown:"UnifiedHeader_chevronDown__h_mXj",megaMenu:"UnifiedHeader_megaMenu__Ucri1",megaMenuContainer:"UnifiedHeader_megaMenuContainer__wCpCo",megaMenuColumn:"UnifiedHeader_megaMenuColumn__KqSvx",megaMenuTitle:"UnifiedHeader_megaMenuTitle__aJ5D0",megaMenuList:"UnifiedHeader_megaMenuList__GnWuD",mobileNavTitle:"UnifiedHeader_mobileNavTitle__DOSfD",mobileNavList:"UnifiedHeader_mobileNavList__ILIob",submenuList:"UnifiedHeader_submenuList__rShdP",mobileMenu:"UnifiedHeader_mobileMenu__T6l2q",mobileMenuContent:"UnifiedHeader_mobileMenuContent__Pidvh",slideIn:"UnifiedHeader_slideIn__6JSV4",mobileMenuHeader:"UnifiedHeader_mobileMenuHeader__18WhE",mobileLogo:"UnifiedHeader_mobileLogo__Sa107",mobileCloseButton:"UnifiedHeader_mobileCloseButton__3TkN5",mobileNav:"UnifiedHeader_mobileNav__9pd4n",mobileSearch:"UnifiedHeader_mobileSearch__BngCe",mobileNavSection:"UnifiedHeader_mobileNavSection__aUi_L",mobileSignOutButton:"UnifiedHeader_mobileSignOutButton__9DxpQ"}},33461:function(e){e.exports={whatsappContainer:"WhatsApp_whatsappContainer__2PVtR",whatsappButton:"WhatsApp_whatsappButton__9IwRe",whatsappTooltip:"WhatsApp_whatsappTooltip__HnQv2",fadeIn:"WhatsApp_fadeIn__T6b8M"}},23745:function(e){e.exports={popupOverlay:"DeviceGradingPopup_popupOverlay__yWJx2",popupContainer:"DeviceGradingPopup_popupContainer__3w181",fadeIn:"DeviceGradingPopup_fadeIn__zWc3d",closeButton:"DeviceGradingPopup_closeButton__Zmxsp",popupHeader:"DeviceGradingPopup_popupHeader__zCPhR",popupContent:"DeviceGradingPopup_popupContent__e4Xhk",gradingCards:"DeviceGradingPopup_gradingCards__sCgtj",gradingCard:"DeviceGradingPopup_gradingCard__BZ0vQ",gradeBadge:"DeviceGradingPopup_gradeBadge__1rRJz",popupInfo:"DeviceGradingPopup_popupInfo__UT9kA",popupFooter:"DeviceGradingPopup_popupFooter__tdXD0",buybackButton:"DeviceGradingPopup_buybackButton__VztH0",learnMoreButton:"DeviceGradingPopup_learnMoreButton___ZgPS"}}}]);