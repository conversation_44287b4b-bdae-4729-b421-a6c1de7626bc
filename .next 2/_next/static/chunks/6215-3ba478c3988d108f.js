(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6215],{76215:function(e,i,t){"use strict";t.d(i,{Z:function(){return u}});var r=t(85893),s=t(67294),n=t(11163),l=t(20323),a=t.n(l),c=t(24287),o=t.n(c),d=e=>{let{min:i=0,max:t=1e3,step:n=10,initialMin:l=0,initialMax:a=1e3,onChange:c}=e,[d,u]=(0,s.useState)(l),[h,p]=(0,s.useState)(a),x=(0,s.useRef)(null),g=(0,s.useRef)(null),m=(0,s.useRef)(null);return(0,s.useEffect)(()=>{if(m.current){let e=(d-i)/(t-i)*100,r=(h-i)/(t-i)*100;m.current.style.left="".concat(e,"%"),m.current.style.width="".concat(r-e,"%")}},[d,h,i,t]),(0,r.jsxs)("div",{className:o().priceRangeSlider,children:[(0,r.jsxs)("div",{className:o().sliderContainer,children:[(0,r.jsx)("div",{className:o().sliderTrack}),(0,r.jsx)("div",{ref:m,className:o().sliderRange}),(0,r.jsx)("input",{type:"range",min:i,max:t,step:n,value:d,onChange:e=>{let i=Math.min(+e.target.value,h-n);u(i),x.current.value=i,c&&c([i,h])},className:"".concat(o().sliderThumb," ").concat(o().sliderThumbLeft)}),(0,r.jsx)("input",{type:"range",min:i,max:t,step:n,value:h,onChange:e=>{let i=Math.max(+e.target.value,d+n);p(i),g.current.value=i,c&&c([d,i])},className:"".concat(o().sliderThumb," ").concat(o().sliderThumbRight)})]}),(0,r.jsxs)("div",{className:o().priceInputs,children:[(0,r.jsxs)("div",{className:o().priceInput,children:[(0,r.jsx)("label",{htmlFor:"min-price",children:"Min"}),(0,r.jsxs)("div",{className:o().inputWithPrefix,children:[(0,r.jsx)("span",{children:"$"}),(0,r.jsx)("input",{ref:x,type:"number",id:"min-price",min:i,max:t,value:d,onChange:e=>{let i=Math.min(+e.target.value,h-n);u(i),c&&c([i,h])}})]})]}),(0,r.jsxs)("div",{className:o().priceInput,children:[(0,r.jsx)("label",{htmlFor:"max-price",children:"Max"}),(0,r.jsxs)("div",{className:o().inputWithPrefix,children:[(0,r.jsx)("span",{children:"$"}),(0,r.jsx)("input",{ref:g,type:"number",id:"max-price",min:i,max:t,value:h,onChange:e=>{let i=Math.max(+e.target.value,d+n);p(i),c&&c([d,i])}})]})]})]})]})},u=e=>{let{categories:i,brands:t,onFilterChange:l}=e,c=(0,n.useRouter)(),{query:o}=c,[u,h]=(0,s.useState)({category:o.category||"",brand:o.brand||"",priceRange:[0,500],inStock:"true"===o.inStock,sortBy:o.sortBy||"featured"}),[p,x]=(0,s.useState)(o.minPrice?parseInt(o.minPrice):0),[g,m]=(0,s.useState)(o.maxPrice?parseInt(o.maxPrice):500),[y,j]=(0,s.useState)(!1);(0,s.useEffect)(()=>{h({category:o.category||"",brand:o.brand||"",priceRange:[o.minPrice?parseInt(o.minPrice):0,o.maxPrice?parseInt(o.maxPrice):500],inStock:"true"===o.inStock,sortBy:o.sortBy||"featured"}),x(o.minPrice?parseInt(o.minPrice):0),m(o.maxPrice?parseInt(o.maxPrice):500)},[o]);let _=(e,i)=>{let t={...u,[e]:i};h(t),l&&l(t);let r={...o};"category"===e&&i?r.category=i:"category"===e&&delete r.category,"brand"===e&&i?r.brand=i:"brand"===e&&delete r.brand,"priceRange"===e&&(r.minPrice=i[0].toString(),r.maxPrice=i[1].toString()),"inStock"===e&&(i?r.inStock="true":delete r.inStock),"sortBy"===e&&"featured"!==i?r.sortBy=i:"sortBy"===e&&delete r.sortBy,c.push({pathname:c.pathname,query:r},void 0,{shallow:!0})},v=()=>{j(!y)};return(0,r.jsxs)("div",{className:a().productFilters,children:[(0,r.jsxs)("div",{className:a().mobileFilterToggle,children:[(0,r.jsxs)("button",{onClick:v,children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("line",{x1:"4",y1:"21",x2:"4",y2:"14"}),(0,r.jsx)("line",{x1:"4",y1:"10",x2:"4",y2:"3"}),(0,r.jsx)("line",{x1:"12",y1:"21",x2:"12",y2:"12"}),(0,r.jsx)("line",{x1:"12",y1:"8",x2:"12",y2:"3"}),(0,r.jsx)("line",{x1:"20",y1:"21",x2:"20",y2:"16"}),(0,r.jsx)("line",{x1:"20",y1:"12",x2:"20",y2:"3"}),(0,r.jsx)("line",{x1:"1",y1:"14",x2:"7",y2:"14"}),(0,r.jsx)("line",{x1:"9",y1:"8",x2:"15",y2:"8"}),(0,r.jsx)("line",{x1:"17",y1:"16",x2:"23",y2:"16"})]}),"Filters"]}),(0,r.jsxs)("div",{className:a().sortByMobile,children:[(0,r.jsx)("label",{htmlFor:"sort-by-mobile",style:{marginRight:"8px",fontSize:"0.875rem",color:"#4b5563"},children:"Sort By:"}),(0,r.jsxs)("select",{id:"sort-by-mobile",value:u.sortBy,onChange:e=>_("sortBy",e.target.value),children:[(0,r.jsx)("option",{value:"featured",children:"Featured"}),(0,r.jsx)("option",{value:"price-asc",children:"Price: Low to High"}),(0,r.jsx)("option",{value:"price-desc",children:"Price: High to Low"}),(0,r.jsx)("option",{value:"newest",children:"Newest First"}),(0,r.jsx)("option",{value:"oldest",children:"Oldest First"}),(0,r.jsx)("option",{value:"name-asc",children:"Name: A to Z"}),(0,r.jsx)("option",{value:"name-desc",children:"Name: Z to A"}),(0,r.jsx)("option",{value:"bestselling",children:"Best Selling"}),(0,r.jsx)("option",{value:"rating",children:"Highest Rated"}),(0,r.jsx)("option",{value:"discount",children:"Biggest Discount"})]})]})]}),(0,r.jsxs)("div",{className:a().sortByDesktop,style:{display:"none"},children:[(0,r.jsx)("label",{htmlFor:"sort-by",children:"Sort By:"}),(0,r.jsxs)("select",{id:"sort-by",value:u.sortBy,onChange:e=>_("sortBy",e.target.value),children:[(0,r.jsx)("option",{value:"featured",children:"Featured"}),(0,r.jsx)("option",{value:"price-asc",children:"Price: Low to High"}),(0,r.jsx)("option",{value:"price-desc",children:"Price: High to Low"}),(0,r.jsx)("option",{value:"newest",children:"Newest First"}),(0,r.jsx)("option",{value:"oldest",children:"Oldest First"}),(0,r.jsx)("option",{value:"name-asc",children:"Name: A to Z"}),(0,r.jsx)("option",{value:"name-desc",children:"Name: Z to A"}),(0,r.jsx)("option",{value:"bestselling",children:"Best Selling"}),(0,r.jsx)("option",{value:"rating",children:"Highest Rated"}),(0,r.jsx)("option",{value:"discount",children:"Biggest Discount"})]})]}),(0,r.jsxs)("div",{className:"".concat(a().filterSidebar," ").concat(y?a().open:""),children:[(0,r.jsxs)("div",{className:a().filterHeader,children:[(0,r.jsx)("h3",{children:"Filters"}),(0,r.jsx)("button",{className:a().closeButton,onClick:v,children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,r.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),(0,r.jsxs)("div",{className:a().filterSection,children:[(0,r.jsx)("h4",{children:"Category"}),(0,r.jsxs)("div",{className:a().filterOptions,children:[(0,r.jsxs)("div",{className:a().filterOption,children:[(0,r.jsx)("input",{type:"radio",id:"category-all",name:"category",checked:""===u.category,onChange:()=>_("category","")}),(0,r.jsx)("label",{htmlFor:"category-all",children:"All Categories"})]}),i&&i.map(e=>(0,r.jsxs)("div",{className:a().categoryGroup,children:[(0,r.jsxs)("div",{className:a().filterOption,children:[(0,r.jsx)("input",{type:"radio",id:"category-".concat(e.id),name:"category",checked:u.category===e.slug,onChange:()=>_("category",e.slug)}),(0,r.jsx)("label",{htmlFor:"category-".concat(e.id),children:e.name})]}),u.category===e.slug&&e.subcategories&&(0,r.jsx)("div",{className:a().subcategories,children:e.subcategories.map(e=>(0,r.jsxs)("div",{className:a().filterOption,children:[(0,r.jsx)("input",{type:"radio",id:"subcategory-".concat(e.id),name:"subcategory",checked:o.subcategory===e.slug,onChange:()=>{let i={...o,subcategory:e.slug};c.push({pathname:c.pathname,query:i},void 0,{shallow:!0})}}),(0,r.jsx)("label",{htmlFor:"subcategory-".concat(e.id),children:e.name})]},e.id))})]},e.id))]})]}),(0,r.jsxs)("div",{className:a().filterSection,children:[(0,r.jsx)("h4",{children:"Brand"}),(0,r.jsxs)("div",{className:a().filterOptions,children:[(0,r.jsxs)("div",{className:a().filterOption,children:[(0,r.jsx)("input",{type:"radio",id:"brand-all",name:"brand",checked:""===u.brand,onChange:()=>_("brand","")}),(0,r.jsx)("label",{htmlFor:"brand-all",children:"All Brands"})]}),t&&t.map(e=>(0,r.jsxs)("div",{className:a().filterOption,children:[(0,r.jsx)("input",{type:"radio",id:"brand-".concat(e.id),name:"brand",checked:u.brand===e.slug,onChange:()=>_("brand",e.slug)}),(0,r.jsx)("label",{htmlFor:"brand-".concat(e.id),children:e.name})]},e.id))]})]}),(0,r.jsxs)("div",{className:a().filterSection,children:[(0,r.jsx)("h4",{children:"Price Range"}),(0,r.jsx)(d,{min:0,max:1e3,step:10,initialMin:p,initialMax:g,onChange:e=>{let[i,t]=e;x(i),m(t),_("priceRange",[i,t])}})]}),(0,r.jsxs)("div",{className:a().filterSection,children:[(0,r.jsx)("h4",{children:"Availability"}),(0,r.jsx)("div",{className:a().filterOptions,children:(0,r.jsxs)("div",{className:a().filterOption,children:[(0,r.jsx)("input",{type:"checkbox",id:"in-stock",checked:u.inStock,onChange:e=>_("inStock",e.target.checked)}),(0,r.jsx)("label",{htmlFor:"in-stock",children:"In Stock Only"})]})})]}),(0,r.jsx)("button",{className:a().resetButton,onClick:()=>{h({category:"",brand:"",priceRange:[0,500],inStock:!1,sortBy:"featured"}),x(0),m(500),c.push({pathname:c.pathname},void 0,{shallow:!0}),l&&l({category:"",brand:"",priceRange:[0,500],inStock:!1,sortBy:"featured"})},children:"Reset Filters"})]})]})}},24287:function(e){e.exports={priceRangeSlider:"PriceRangeSlider_priceRangeSlider__aWdzh",sliderContainer:"PriceRangeSlider_sliderContainer__zJs2p",sliderTrack:"PriceRangeSlider_sliderTrack__jbIDN",sliderRange:"PriceRangeSlider_sliderRange__c4qpY",sliderThumb:"PriceRangeSlider_sliderThumb__OLGvW",priceInputs:"PriceRangeSlider_priceInputs__DgqA4",priceInput:"PriceRangeSlider_priceInput__CkmPO",inputWithPrefix:"PriceRangeSlider_inputWithPrefix__NNqxw"}},20323:function(e){e.exports={productFilters:"ProductFilters_productFilters__GQ08N",mobileFilterToggle:"ProductFilters_mobileFilterToggle__ySVG5",sortByMobile:"ProductFilters_sortByMobile__7milb",filterSidebar:"ProductFilters_filterSidebar__9QGCn",open:"ProductFilters_open__etQTd",filterHeader:"ProductFilters_filterHeader__9DcUF",closeButton:"ProductFilters_closeButton__6n2mE",filterSection:"ProductFilters_filterSection__Ada3n",filterOptions:"ProductFilters_filterOptions__6IV5Q",filterOption:"ProductFilters_filterOption__NqfIe",categoryGroup:"ProductFilters_categoryGroup__c8Fqz",subcategories:"ProductFilters_subcategories__kfXBZ",priceRange:"ProductFilters_priceRange__vl_Ch",priceInputs:"ProductFilters_priceInputs__TcNht",priceInput:"ProductFilters_priceInput__VJay5",inputWithPrefix:"ProductFilters_inputWithPrefix__hj8Sz",applyButton:"ProductFilters_applyButton__qH_lg",resetButton:"ProductFilters_resetButton__h3aKN",sortByDesktop:"ProductFilters_sortByDesktop__xCkgr"}},11163:function(e,i,t){e.exports=t(43079)}}]);