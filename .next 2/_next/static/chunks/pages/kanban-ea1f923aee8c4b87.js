(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8310],{96185:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/kanban",function(){return t(98427)}])},98427:function(e,n,t){"use strict";t.r(n);var r=t(85893),s=t(67294),a=t(33299),o=t(11163),i=t(56029),l=t(15948);let d=(0,t(48967).eI)("https://your-supabase-project-url.supabase.co","your-supabase-anon-key"),c={tasks:{"task-1":{id:"task-1",content:"Take out the garbage",priority:"medium"},"task-2":{id:"task-2",content:"Watch my favorite show",priority:"low"},"task-3":{id:"task-3",content:"Charge my phone",priority:"high"},"task-4":{id:"task-4",content:"Cook dinner",priority:"medium"}},columns:{"column-1":{id:"column-1",title:"To do",taskIds:["task-1","task-2","task-3","task-4"]},"column-2":{id:"column-2",title:"In progress",taskIds:[]},"column-3":{id:"column-3",title:"Done",taskIds:[]}},columnOrder:["column-1","column-2","column-3"]};n.default=function(){let{data:e,status:n}=(0,a.useSession)(),t=(0,o.useRouter)(),[u,m]=(0,s.useState)(c),[p,h]=(0,s.useState)(!0),[f,b]=(0,s.useState)(""),[x,k]=(0,s.useState)("medium"),[g,y]=(0,s.useState)(null);(0,s.useEffect)(()=>{"unauthenticated"===n&&t.push("/auth/signin?callbackUrl=/kanban")},[n,t]),(0,s.useEffect)(()=>{var n;async function t(){var n;if(null==e?void 0:null===(n=e.user)||void 0===n?void 0:n.id)try{let{data:n,error:t}=await d.from("kanban_columns").select("*").eq("user_id",e.user.id).order("position");if(t)throw t;let{data:r,error:s}=await d.from("kanban_tasks").select("*").eq("user_id",e.user.id);if(s)throw s;if(n.length>0&&r){let e={};r.forEach(n=>{e[n.id]={id:n.id,content:n.content,priority:n.priority}});let t={},s=[];n.forEach(e=>{t[e.id]={id:e.id,title:e.title,taskIds:e.task_ids||[]},s.push(e.id)}),m({tasks:e,columns:t,columnOrder:s})}}catch(e){console.error("Error fetching board data:",e),m(c)}finally{h(!1)}}(null==e?void 0:null===(n=e.user)||void 0===n?void 0:n.id)?t():h(!1)},[e]);let j=async e=>{let{destination:n,source:t,draggableId:r,type:s}=e;if(!n||n.droppableId===t.droppableId&&n.index===t.index)return;if("column"===s){let e=Array.from(u.columnOrder);e.splice(t.index,1),e.splice(n.index,0,r),m({...u,columnOrder:e});try{for(let n=0;n<e.length;n++)await d.from("kanban_columns").update({position:n}).eq("id",e[n])}catch(e){console.error("Error updating column positions:",e)}return}let a=u.columns[t.droppableId],o=u.columns[n.droppableId];if(a===o){let e=Array.from(a.taskIds);e.splice(t.index,1),e.splice(n.index,0,r);let s={...a,taskIds:e};m({...u,columns:{...u.columns,[s.id]:s}});try{await d.from("kanban_columns").update({task_ids:e}).eq("id",a.id)}catch(e){console.error("Error updating task order:",e)}return}let i=Array.from(a.taskIds);i.splice(t.index,1);let l={...a,taskIds:i},c=Array.from(o.taskIds);c.splice(n.index,0,r);let p={...o,taskIds:c};m({...u,columns:{...u.columns,[l.id]:l,[p.id]:p}});try{await d.from("kanban_columns").update({task_ids:i}).eq("id",a.id),await d.from("kanban_columns").update({task_ids:c}).eq("id",o.id)}catch(e){console.error("Error updating task columns:",e)}},w=async n=>{if(!f.trim())return;let t="task-".concat(Date.now()),r=u.columns[n],s=Array.from(r.taskIds);s.push(t);let a={...r,taskIds:s};m({...u,tasks:{...u.tasks,[t]:{id:t,content:f,priority:x}},columns:{...u.columns,[n]:a}}),b(""),k("medium"),y(null);try{await d.from("kanban_tasks").insert([{id:t,content:f,priority:x,user_id:e.user.id}]),await d.from("kanban_columns").update({task_ids:s}).eq("id",n)}catch(e){console.error("Error adding new task:",e)}},v=async(e,n)=>{let t=u.columns[n],r=t.taskIds.filter(n=>n!==e),s={...t,taskIds:r},a={...u.tasks};delete a[e],m({...u,tasks:a,columns:{...u.columns,[n]:s}});try{await d.from("kanban_tasks").delete().eq("id",e),await d.from("kanban_columns").update({task_ids:r}).eq("id",n)}catch(e){console.error("Error deleting task:",e)}},_=async()=>{let n="column-".concat(Date.now()),t=Array.from(u.columnOrder);t.push(n),m({...u,columns:{...u.columns,[n]:{id:n,title:"New Column",taskIds:[]}},columnOrder:t});try{await d.from("kanban_columns").insert([{id:n,title:"New Column",task_ids:[],position:t.length-1,user_id:e.user.id}])}catch(e){console.error("Error adding new column:",e)}},N=async(e,n)=>{if(!n.trim())return;let t={...u.columns[e],title:n};m({...u,columns:{...u.columns,[e]:t}});try{await d.from("kanban_columns").update({title:n}).eq("id",e)}catch(e){console.error("Error updating column title:",e)}},I=async e=>{let n=u.columns[e].taskIds,t={...u.columns};delete t[e];let r={...u.tasks};n.forEach(e=>{delete r[e]}),m({tasks:r,columns:t,columnOrder:u.columnOrder.filter(n=>n!==e)});try{for(let t of(await d.from("kanban_columns").delete().eq("id",e),n))await d.from("kanban_tasks").delete().eq("id",t)}catch(e){console.error("Error deleting column:",e)}};return"loading"===n||p?(0,r.jsx)(i.Z,{title:"Kanban Board | MDTS",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"flex justify-center items-center h-64",children:[(0,r.jsx)("div",{className:"loading-spinner"}),(0,r.jsx)("p",{className:"ml-4",children:"Loading board..."})]})})}):"unauthenticated"===n?(0,r.jsx)(i.Z,{title:"Kanban Board | MDTS",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("p",{children:"Please sign in to access the Kanban board."})})})}):(0,r.jsx)(i.Z,{title:"Kanban Board | MDTS",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Project Management Board"}),(0,r.jsx)("button",{onClick:_,className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"Add Column"})]}),(0,r.jsx)(l.Z5,{onDragEnd:j,children:(0,r.jsx)(l.bK,{droppableId:"all-columns",direction:"horizontal",type:"column",children:e=>(0,r.jsxs)("div",{...e.droppableProps,ref:e.innerRef,className:"flex overflow-x-auto pb-4 space-x-4",style:{minHeight:"500px"},children:[u.columnOrder.map((e,n)=>{let t=u.columns[e],s=t.taskIds.map(e=>u.tasks[e]);return(0,r.jsx)(l._l,{draggableId:t.id,index:n,children:e=>(0,r.jsxs)("div",{...e.draggableProps,ref:e.innerRef,className:"bg-gray-100 rounded-lg w-80 flex-shrink-0",children:[(0,r.jsxs)("div",{...e.dragHandleProps,className:"p-4 font-semibold bg-gray-200 rounded-t-lg flex justify-between items-center",children:[(0,r.jsx)("input",{type:"text",value:t.title,onChange:e=>N(t.id,e.target.value),className:"bg-transparent border-b border-transparent hover:border-gray-400 focus:border-blue-500 focus:outline-none px-1"}),(0,r.jsx)("button",{onClick:()=>I(t.id),className:"text-red-500 hover:text-red-700 focus:outline-none",children:"\xd7"})]}),(0,r.jsx)(l.bK,{droppableId:t.id,type:"task",children:(e,n)=>(0,r.jsxs)("div",{...e.droppableProps,ref:e.innerRef,className:"p-4 min-h-[400px] ".concat(n.isDraggingOver?"bg-blue-50":""),children:[s.map((e,n)=>(0,r.jsx)(l._l,{draggableId:e.id,index:n,children:(n,s)=>(0,r.jsx)("div",{...n.draggableProps,...n.dragHandleProps,ref:n.innerRef,className:"p-3 mb-3 rounded shadow-sm ".concat(s.isDragging?"bg-blue-100":"bg-white"," ").concat("high"===e.priority?"border-l-4 border-red-500":"medium"===e.priority?"border-l-4 border-yellow-500":"border-l-4 border-green-500"),children:(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("p",{children:e.content}),(0,r.jsx)("button",{onClick:()=>v(e.id,t.id),className:"text-red-500 hover:text-red-700 focus:outline-none ml-2",children:"\xd7"})]})})},e.id)),e.placeholder,g===t.id?(0,r.jsxs)("div",{className:"mt-4 p-3 bg-white rounded shadow-sm",children:[(0,r.jsx)("textarea",{value:f,onChange:e=>b(e.target.value),placeholder:"Enter task description...",className:"w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 mb-2",rows:"3"}),(0,r.jsxs)("div",{className:"flex mb-2",children:[(0,r.jsx)("label",{className:"mr-2",children:"Priority:"}),(0,r.jsxs)("select",{value:x,onChange:e=>k(e.target.value),className:"border rounded p-1 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"low",children:"Low"}),(0,r.jsx)("option",{value:"medium",children:"Medium"}),(0,r.jsx)("option",{value:"high",children:"High"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)("button",{onClick:()=>y(null),className:"px-3 py-1 text-gray-600 hover:text-gray-800 focus:outline-none",children:"Cancel"}),(0,r.jsx)("button",{onClick:()=>w(t.id),className:"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"Add"})]})]}):(0,r.jsx)("button",{onClick:()=>y(t.id),className:"w-full mt-2 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded text-center focus:outline-none",children:"+ Add Task"})]})})]})},t.id)}),e.placeholder]})})})]})})}}},function(e){e.O(0,[630,1664,5675,8764,8967,8941,6029,2888,9774,179],function(){return e(e.s=96185)}),_N_E=e.O()}]);