(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7345],{83308:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/products",function(){return a(90124)}])},90124:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return p}});var r=a(85893);a(25675);var t=a(67294),n=a(41664),i=a.n(n),o=a(11163);a(9008);var c=a(56029),d=a(76215),u=a(72885),l=a.n(u);function p(){let e=(0,o.useRouter)(),{query:s}=e,[a,n]=(0,t.useState)([]),[u,p]=(0,t.useState)(!0),[g,h]=(0,t.useState)(null),[m,_]=(0,t.useState)(1),[P]=(0,t.useState)(12),[j,x]=(0,t.useState)(0),[k,w]=(0,t.useState)(1),[v,b]=(0,t.useState)([{id:1,name:"iPhone Parts",slug:"iphone-parts",subcategories:[{id:101,name:"iPhone 15 Series",slug:"iphone-parts/iphone-15"},{id:102,name:"iPhone 14 Series",slug:"iphone-parts/iphone-14"},{id:103,name:"iPhone 13 Series",slug:"iphone-parts/iphone-13"},{id:104,name:"iPhone 12 Series",slug:"iphone-parts/iphone-12"},{id:105,name:"Screens & LCDs",slug:"iphone-parts/screens"},{id:106,name:"Batteries",slug:"iphone-parts/batteries"},{id:107,name:"Charging Ports",slug:"iphone-parts/charging-ports"}]},{id:2,name:"Samsung Parts",slug:"samsung-parts",subcategories:[{id:201,name:"Galaxy S Series",slug:"samsung-parts/galaxy-s"},{id:202,name:"Galaxy Note Series",slug:"samsung-parts/galaxy-note"},{id:203,name:"Galaxy A Series",slug:"samsung-parts/galaxy-a"},{id:204,name:"Screens & LCDs",slug:"samsung-parts/screens"},{id:205,name:"Batteries",slug:"samsung-parts/batteries"}]},{id:3,name:"iPad Parts",slug:"ipad-parts",subcategories:[{id:301,name:"iPad Pro",slug:"ipad-parts/ipad-pro"},{id:302,name:"iPad Air",slug:"ipad-parts/ipad-air"},{id:303,name:"iPad Mini",slug:"ipad-parts/ipad-mini"},{id:304,name:"Screens & LCDs",slug:"ipad-parts/screens"}]},{id:4,name:"MacBook Parts",slug:"macbook-parts",subcategories:[{id:401,name:"MacBook Pro",slug:"macbook-parts/macbook-pro"},{id:402,name:"MacBook Air",slug:"macbook-parts/macbook-air"},{id:403,name:"Screens",slug:"macbook-parts/screens"},{id:404,name:"Keyboards",slug:"macbook-parts/keyboards"},{id:405,name:"Batteries",slug:"macbook-parts/batteries"}]},{id:5,name:"Repair Tools",slug:"repair-tools",subcategories:[{id:501,name:"Tool Kits",slug:"repair-tools/tool-kits"},{id:502,name:"Screwdrivers",slug:"repair-tools/screwdrivers"},{id:503,name:"Heat Guns",slug:"repair-tools/heat-guns"},{id:504,name:"Soldering Equipment",slug:"repair-tools/soldering"},{id:505,name:"Adhesives & Tapes",slug:"repair-tools/adhesives"}]}]),[y,f]=(0,t.useState)([{id:1,name:"Apple",slug:"apple"},{id:2,name:"Samsung",slug:"samsung"},{id:3,name:"LG",slug:"lg"},{id:4,name:"Huawei",slug:"huawei"},{id:5,name:"Xiaomi",slug:"xiaomi"}]),N=(0,t.useMemo)(()=>["iphone-parts.png","iphone-screen.jpg","iphone-battery.jpg","iphone-camera.jpg","iphone-charging-port.jpg","iphone-back-glass.jpg","samsung-parts.png","samsung-screen.jpg","samsung-battery.jpg","samsung-camera.jpg","samsung-charging-port.jpg","ipad-parts.png","ipad-screen.jpg","ipad-battery.jpg","ipad-camera.jpg","macbook-parts.png","macbook-screen.jpg","macbook-keyboard.jpg","macbook-battery.jpg","macbook-trackpad.jpg","repair-tools.png","screwdriver-set.jpg","heat-gun.jpg","opening-tools.jpg","soldering-kit.jpg","microscope.jpg","apple-logo.png","warranty.png","certified.png"],[]),C=e=>{var s,a;let r=(null===(s=e.name)||void 0===s?void 0:s.toLowerCase())||"",t=(null===(a=e.category_name)||void 0===a?void 0:a.toLowerCase())||"";if(t.includes("iphone")){if(r.includes("screen")||r.includes("display")||r.includes("lcd"))return"iphone-screen.jpg";if(r.includes("battery"))return"iphone-battery.jpg";if(r.includes("camera"))return"iphone-camera.jpg";if(r.includes("charging")||r.includes("port")||r.includes("connector"))return"iphone-charging-port.jpg";else if(r.includes("back")||r.includes("glass")||r.includes("housing"))return"iphone-back-glass.jpg";else return"iphone-parts.png"}if(t.includes("samsung"))return r.includes("screen")||r.includes("display")||r.includes("lcd")?"samsung-screen.jpg":r.includes("battery")?"samsung-battery.jpg":r.includes("camera")?"samsung-camera.jpg":r.includes("charging")||r.includes("port")||r.includes("connector")?"samsung-charging-port.jpg":"samsung-parts.png";if(t.includes("ipad"))return r.includes("screen")||r.includes("display")||r.includes("lcd")?"ipad-screen.jpg":r.includes("battery")?"ipad-battery.jpg":r.includes("camera")?"ipad-camera.jpg":"ipad-parts.png";if(t.includes("macbook"))return r.includes("screen")||r.includes("display")||r.includes("lcd")?"macbook-screen.jpg":r.includes("keyboard")?"macbook-keyboard.jpg":r.includes("battery")?"macbook-battery.jpg":r.includes("trackpad")?"macbook-trackpad.jpg":"macbook-parts.png";if(t.includes("tool")){if(r.includes("screwdriver")||r.includes("driver"))return"screwdriver-set.jpg";if(r.includes("heat")||r.includes("gun"))return"heat-gun.jpg";if(r.includes("opening")||r.includes("pry"))return"opening-tools.jpg";if(r.includes("solder"))return"soldering-kit.jpg";else if(r.includes("microscope")||r.includes("magnifier"))return"microscope.jpg";else return"repair-tools.png"}{let e=Math.floor(Math.random()*N.length);return N[e]}};return(0,t.useEffect)(()=>{s.page&&_(parseInt(s.page))},[s.page]),(0,t.useEffect)(()=>{(async()=>{try{p(!0);let e="page=".concat(m,"&limit=").concat(P);s.category&&(e+="&category=".concat(s.category)),s.brand&&(e+="&brand=".concat(s.brand)),s.minPrice&&s.maxPrice&&(e+="&minPrice=".concat(s.minPrice,"&maxPrice=").concat(s.maxPrice)),"true"===s.inStock&&(e+="&inStock=true"),s.sortBy&&(e+="&sortBy=".concat(s.sortBy));let a=await fetch("/api/products?".concat(e));if(!a.ok)throw Error("Failed to fetch products");let r=await a.json();if(r.success)n(r.products),x(r.total||r.products.length),w(r.totalPages||Math.ceil(r.total/P)||1);else throw Error(r.message||"Failed to fetch products")}catch(e){console.error("Error fetching products:",e),h(e.message)}finally{p(!1)}})()},[m,P,s]),(0,r.jsx)(c.Z,{title:"Products | MDTS - Midas Technical Solutions",description:"Browse our extensive collection of repair parts and tools for all major device brands.",children:(0,r.jsxs)("div",{className:"container ".concat(l().productsContainer),children:[(0,r.jsxs)("div",{className:l().productsHeader,children:[(0,r.jsx)("h1",{children:"Products"}),(0,r.jsx)("p",{children:"Browse our extensive collection of repair parts and tools"})]}),(0,r.jsxs)("div",{className:l().productsLayout,children:[(0,r.jsx)(d.Z,{categories:v,brands:y,onFilterChange:e=>{}}),(0,r.jsx)("div",{className:l().productsContent,children:u?(0,r.jsxs)("div",{className:"loading-container",children:[(0,r.jsx)("div",{className:"loading-spinner"}),(0,r.jsx)("p",{children:"Loading products..."})]}):g?(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsxs)("p",{children:["Error: ",g]}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"btn btn-primary",children:"Try Again"})]}):0===a.length?(0,r.jsxs)("div",{className:l().emptyProducts,children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("circle",{cx:"9",cy:"21",r:"1"}),(0,r.jsx)("circle",{cx:"20",cy:"21",r:"1"}),(0,r.jsx)("path",{d:"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"})]}),(0,r.jsx)("h3",{children:"No products found"}),(0,r.jsx)("p",{children:"Try adjusting your filters or browse all products"}),(0,r.jsx)("button",{className:l().resetButton,onClick:()=>e.push("/products"),children:"Reset Filters"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:l().resultsInfo,children:(0,r.jsxs)("p",{children:["Showing ",a.length," of ",j," products"]})}),(0,r.jsx)("div",{className:l().productsGrid,children:a.map(e=>(0,r.jsxs)("div",{className:l().productCard,children:[(0,r.jsxs)("div",{className:l().productImageContainer,children:[(0,r.jsx)("img",{src:e.image_url||"/images/gapp/".concat(C(e)),alt:e.name,className:l().productImage}),e.discount_percentage>0&&(0,r.jsxs)("span",{className:l().discountBadge,children:[e.discount_percentage,"% OFF"]}),(0,r.jsxs)("div",{className:l().productActions,children:[(0,r.jsx)("button",{className:l().wishlistButton,title:"Add to Wishlist",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,r.jsx)("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})})}),(0,r.jsx)("button",{className:l().quickViewButton,title:"Quick View",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),(0,r.jsx)("circle",{cx:"12",cy:"12",r:"3"})]})})]})]}),(0,r.jsxs)("div",{className:l().productContent,children:[(0,r.jsx)("div",{className:l().productCategory,children:e.category_name}),(0,r.jsx)("h3",{className:l().productName,children:(0,r.jsx)(i(),{href:"/products/".concat(e.slug),children:e.name})}),(0,r.jsx)("div",{className:l().productPrice,children:e.discount_percentage>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{className:l().originalPrice,children:["$",(e.price/(1-e.discount_percentage/100)).toFixed(2)]}),(0,r.jsxs)("span",{className:l().currentPrice,children:["$",e.price.toFixed(2)]})]}):(0,r.jsxs)("span",{className:l().currentPrice,children:["$",e.price.toFixed(2)]})}),(0,r.jsxs)("div",{className:l().productButtons,children:[(0,r.jsx)(i(),{href:"/products/".concat(e.slug),className:l().viewDetailsButton,children:"View Details"}),(0,r.jsx)("button",{className:l().addToCartButton,children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("circle",{cx:"9",cy:"21",r:"1"}),(0,r.jsx)("circle",{cx:"20",cy:"21",r:"1"}),(0,r.jsx)("path",{d:"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"})]})})]})]})]},e.id))}),(0,r.jsxs)("div",{className:l().pagination,children:[(0,r.jsxs)("button",{onClick:()=>{e.push({pathname:e.pathname,query:{...s,page:Math.max(m-1,1)}},void 0,{shallow:!0})},disabled:1===m,className:"".concat(l().paginationButton," ").concat(1===m?l().disabled:""),children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,r.jsx)("polyline",{points:"15 18 9 12 15 6"})}),"Previous"]}),(0,r.jsx)("div",{className:l().paginationPages,children:[...Array(k)].map((a,t)=>(0,r.jsx)("button",{onClick:()=>{e.push({pathname:e.pathname,query:{...s,page:t+1}},void 0,{shallow:!0})},className:"".concat(l().pageNumber," ").concat(m===t+1?l().activePage:""),children:t+1},t))}),(0,r.jsxs)("button",{onClick:()=>{e.push({pathname:e.pathname,query:{...s,page:m+1}},void 0,{shallow:!0})},disabled:m>=k,className:"".concat(l().paginationButton," ").concat(m>=k?l().disabled:""),children:["Next",(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,r.jsx)("polyline",{points:"9 18 15 12 9 6"})})]})]})]})})]})]})})}},72885:function(e){e.exports={header:"ProductsPage_header__0_qnD",headerContainer:"ProductsPage_headerContainer__qvOxP",logo:"ProductsPage_logo__MtNzK",searchBar:"ProductsPage_searchBar__t7oRJ",navigation:"ProductsPage_navigation__uH1WO",signInLink:"ProductsPage_signInLink__5pgPk",productsContainer:"ProductsPage_productsContainer__svYMq",productsHeader:"ProductsPage_productsHeader__Yi_iT",enhancedSearchContainer:"ProductsPage_enhancedSearchContainer__SjrN6",productsLayout:"ProductsPage_productsLayout__NRNcW",productsContent:"ProductsPage_productsContent__TbfJr",resultsInfo:"ProductsPage_resultsInfo__dY2V4",productsGrid:"ProductsPage_productsGrid__6469f",productCard:"ProductsPage_productCard__uvvwR",productImageContainer:"ProductsPage_productImageContainer__aTle7",productImage:"ProductsPage_productImage__AC5vw",discountBadge:"ProductsPage_discountBadge__A6mku",productActions:"ProductsPage_productActions__BCST1",quickViewButton:"ProductsPage_quickViewButton__4pBo9",wishlistButton:"ProductsPage_wishlistButton__FThVi",productContent:"ProductsPage_productContent__i8b6I",productCategory:"ProductsPage_productCategory__ACJwm",productName:"ProductsPage_productName__f3hpv",productPrice:"ProductsPage_productPrice__xiPe6",originalPrice:"ProductsPage_originalPrice__1_apf",currentPrice:"ProductsPage_currentPrice__LmtSa",productButtons:"ProductsPage_productButtons__8oSdX",viewDetailsButton:"ProductsPage_viewDetailsButton__g83gW",addToCartButton:"ProductsPage_addToCartButton__vUa7W",emptyProducts:"ProductsPage_emptyProducts__Ov28Q",resetButton:"ProductsPage_resetButton__YdSZP",pagination:"ProductsPage_pagination___dj7k",paginationButton:"ProductsPage_paginationButton__A37dH",disabled:"ProductsPage_disabled__BZBVf",paginationPages:"ProductsPage_paginationPages__HWQJe",pageNumber:"ProductsPage_pageNumber__YyvCr",activePage:"ProductsPage_activePage__E2fDy"}}},function(e){e.O(0,[1664,5675,6029,6215,2888,9774,179],function(){return e(e.s=83308)}),_N_E=e.O()}]);