(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4536],{5288:function(e,r,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/trademark-disclaimer",function(){return t(8130)}])},8130:function(e,r,t){"use strict";t.r(r);var i=t(85893);t(67294);var o=t(41664),a=t.n(o);t(9008);var n=t(56029);r.default=function(){return(0,i.jsx)(n.Z,{title:"Trademark Disclaimer - Midas Technical Solutions",description:"Read our trademark disclaimer regarding the use of third-party trademarks and brand names on the MDTS website.",children:(0,i.jsxs)("div",{className:"container",style:{padding:"40px 20px",maxWidth:"800px",margin:"0 auto"},children:[(0,i.jsx)("h1",{style:{fontSize:"2rem",marginBottom:"1.5rem"},children:"Trademark Disclaimer"}),(0,i.jsxs)("section",{style:{marginBottom:"2rem"},children:[(0,i.jsx)("p",{style:{marginBottom:"1rem",lineHeight:"1.6"},children:'The trademarks, service marks, and logos (collectively the "Trademarks") used and displayed on this website are registered and unregistered Trademarks of Midas Technical Solutions and others.'}),(0,i.jsx)("p",{style:{marginBottom:"1rem",lineHeight:"1.6"},children:"Nothing on this website should be construed as granting, by implication, estoppel, or otherwise, any license or right to use any Trademark displayed on this website without the written permission of Midas Technical Solutions or the third party that owns the Trademark."}),(0,i.jsx)("p",{style:{marginBottom:"1rem",lineHeight:"1.6"},children:"Midas Technical Solutions aggressively enforces its intellectual property rights to the fullest extent of the law. The name Midas Technical Solutions, MDTS, the Midas Technical Solutions logo, and all related names, logos, product and service names, designs, and slogans are trademarks of Midas Technical Solutions or its affiliates. You may not use such marks without the prior written permission of Midas Technical Solutions."})]}),(0,i.jsxs)("section",{style:{marginBottom:"2rem"},children:[(0,i.jsx)("h2",{style:{fontSize:"1.5rem",marginBottom:"1rem"},children:"Third-Party Trademarks"}),(0,i.jsx)("p",{style:{marginBottom:"1rem",lineHeight:"1.6"},children:"All other trademarks, registered trademarks, product names and company names or logos mentioned on the website are the property of their respective owners. Reference to any products, services, processes, or other information by trade name, trademark, manufacturer, supplier, or otherwise does not constitute or imply endorsement, sponsorship, or recommendation by Midas Technical Solutions."}),(0,i.jsx)("p",{style:{marginBottom:"1rem",lineHeight:"1.6"},children:"Specifically, Midas Technical Solutions is not affiliated with, endorsed by, or sponsored by Apple Inc., Samsung Electronics Co., Ltd., or any other manufacturer whose products or parts are sold on our website. All manufacturer names, logos, product names, and trademarks are the property of their respective owners."}),(0,i.jsx)("p",{style:{marginBottom:"1rem",lineHeight:"1.6"},children:"The following is a non-exhaustive list of third-party trademarks that may appear on our website:"}),(0,i.jsxs)("ul",{style:{marginBottom:"1rem",paddingLeft:"1.5rem",lineHeight:"1.6"},children:[(0,i.jsx)("li",{children:"Apple\xae, iPhone\xae, iPad\xae, MacBook\xae, and other Apple product names are trademarks of Apple Inc., registered in the U.S. and other countries."}),(0,i.jsx)("li",{children:"Samsung\xae, Galaxy\xae, and other Samsung product names are trademarks of Samsung Electronics Co., Ltd."}),(0,i.jsx)("li",{children:"Google\xae, Pixel\xae, and other Google product names are trademarks of Google LLC."}),(0,i.jsx)("li",{children:"Other product and company names mentioned herein may be trademarks of their respective owners."})]})]}),(0,i.jsxs)("section",{style:{marginBottom:"2rem"},children:[(0,i.jsx)("h2",{style:{fontSize:"1.5rem",marginBottom:"1rem"},children:"Compatibility Statement"}),(0,i.jsx)("p",{style:{marginBottom:"1rem",lineHeight:"1.6"},children:'When we state that our products are "compatible" with a specific device or model, we are indicating that our products are designed to work with that device or model. This does not imply that our products are manufactured by, endorsed by, or affiliated with the original equipment manufacturer.'}),(0,i.jsx)("p",{style:{marginBottom:"1rem",lineHeight:"1.6"},children:'Unless explicitly stated otherwise, the parts and accessories sold on our website are aftermarket products designed to be compatible with the referenced devices. They are not original equipment manufacturer (OEM) parts unless specifically labeled as "OEM" or "Genuine."'})]}),(0,i.jsxs)("section",{style:{marginBottom:"2rem"},children:[(0,i.jsx)("h2",{style:{fontSize:"1.5rem",marginBottom:"1rem"},children:"Apple Parts Program"}),(0,i.jsx)("p",{style:{marginBottom:"1rem",lineHeight:"1.6"},children:'Midas Technical Solutions is a participant in the Apple Independent Repair Provider Program. Through this program, we are authorized to offer genuine Apple parts for certain repairs. When we refer to "Genuine Apple Parts" or "GAPP" (Genuine Apple Parts Program), we are referring to parts obtained through this official program.'}),(0,i.jsxs)("p",{style:{lineHeight:"1.6"},children:["For more information about our participation in the Apple Independent Repair Provider Program, please visit our ",(0,i.jsx)(a(),{href:"/gapp",style:{color:"#0066cc",textDecoration:"none"},children:"Apple Parts Program page"}),"."]})]}),(0,i.jsxs)("section",{style:{backgroundColor:"#f0f7ff",padding:"1.5rem",borderRadius:"8px",marginBottom:"2rem"},children:[(0,i.jsx)("h2",{style:{fontSize:"1.5rem",marginBottom:"1rem"},children:"Contact Information"}),(0,i.jsx)("p",{style:{lineHeight:"1.6"},children:"If you have any questions about our trademark usage or would like to report potential trademark infringement, please contact us at:"}),(0,i.jsxs)("p",{style:{marginTop:"1rem",lineHeight:"1.6"},children:["Midas Technical Solutions",(0,i.jsx)("br",{}),"Vienna, VA 22182",(0,i.jsx)("br",{}),"Email: <EMAIL>",(0,i.jsx)("br",{}),"Phone: +****************"]})]})]})})}},11163:function(e,r,t){e.exports=t(43079)}},function(e){e.O(0,[1664,5675,6029,2888,9774,179],function(){return e(e.s=5288)}),_N_E=e.O()}]);