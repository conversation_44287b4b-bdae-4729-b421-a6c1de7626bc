(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1811],{7837:function(e,i,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/device-grading",function(){return a(95372)}])},95372:function(e,i,a){"use strict";a.r(i),a.d(i,{__N_SSP:function(){return j},default:function(){return m}});var r=a(85893),s=a(67294),d=a(9008),l=a.n(d),n=a(41664),c=a.n(n),t=a(11163),g=a(33299),o=a(56029),h=a(27180),x=a.n(h),j=!0;function m(){let{data:e,status:i}=(0,g.useSession)(),a=(0,t.useRouter)(),[d,n]=(0,s.useState)("lcd");(0,s.useEffect)(()=>{"loading"!==i&&"unauthenticated"===i&&a.replace("/device-grading-public")},[i,a]);let h=e=>{n(e)};return"loading"===i?(0,r.jsx)(o.Z,{children:(0,r.jsx)("div",{className:"container",children:(0,r.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"50vh",flexDirection:"column"},children:[(0,r.jsx)("div",{style:{border:"4px solid rgba(0, 0, 0, 0.1)",borderRadius:"50%",borderTop:"4px solid #0066cc",width:"40px",height:"40px",animation:"spin 1s linear infinite",marginBottom:"1rem"}}),(0,r.jsx)("p",{children:"Loading..."})]})})}):"unauthenticated"===i?null:(0,r.jsxs)(o.Z,{children:[(0,r.jsxs)(l(),{children:[(0,r.jsx)("title",{children:"Device Grading System | MDTS - Midas Technical Solutions"}),(0,r.jsx)("meta",{name:"description",content:"Learn about our device grading system for LCD buyback and trade-ins at MDTS - Midas Technical Solutions."})]}),(0,r.jsx)("main",{className:"main-content",children:(0,r.jsxs)("div",{className:"container",children:[(0,r.jsxs)("div",{className:x().gradingHeader,children:[(0,r.jsx)("h1",{children:"Device Grading System"}),(0,r.jsx)("p",{children:"At MDTS, we use a standardized grading system to evaluate the condition of devices and parts. This ensures transparency and fair pricing for all our customers."})]}),(0,r.jsxs)("div",{className:x().gradingTabs,children:[(0,r.jsx)("button",{className:"".concat(x().gradingTab," ").concat("lcd"===d?x().active:""),onClick:()=>h("lcd"),children:"LCD Screens"}),(0,r.jsx)("button",{className:"".concat(x().gradingTab," ").concat("devices"===d?x().active:""),onClick:()=>h("devices"),children:"Complete Devices"}),(0,r.jsx)("button",{className:"".concat(x().gradingTab," ").concat("batteries"===d?x().active:""),onClick:()=>h("batteries"),children:"Batteries"})]}),(0,r.jsxs)("div",{className:x().gradingContent,children:["lcd"===d&&(0,r.jsxs)("div",{className:x().gradingSection,children:[(0,r.jsx)("h2",{children:"LCD Screen Grading"}),(0,r.jsxs)("div",{className:x().gradingCards,children:[(0,r.jsxs)("div",{className:x().gradingCard,children:[(0,r.jsx)("div",{className:x().gradeBadge,style:{backgroundColor:"#4CAF50"},children:"Grade A"}),(0,r.jsx)("h3",{children:"Grade A (Excellent)"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"100% functional with no issues"}),(0,r.jsx)("li",{children:"No visible scratches or marks"}),(0,r.jsx)("li",{children:"Perfect touch functionality"}),(0,r.jsx)("li",{children:"No discoloration or burn-in"}),(0,r.jsx)("li",{children:"Original OEM quality"})]}),(0,r.jsxs)("div",{className:x().gradeImages,children:[(0,r.jsx)("img",{src:"/images/grading/lcd-a1.jpg",alt:"Grade A LCD Example 1"}),(0,r.jsx)("img",{src:"/images/grading/lcd-a2.jpg",alt:"Grade A LCD Example 2"})]})]}),(0,r.jsxs)("div",{className:x().gradingCard,children:[(0,r.jsx)("div",{className:x().gradeBadge,style:{backgroundColor:"#2196F3"},children:"Grade B"}),(0,r.jsx)("h3",{children:"Grade B (Good)"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"100% functional"}),(0,r.jsx)("li",{children:"Minor scratches (not visible when screen is on)"}),(0,r.jsx)("li",{children:"Full touch functionality"}),(0,r.jsx)("li",{children:"Slight discoloration may be present"}),(0,r.jsx)("li",{children:"May be aftermarket high-quality"})]}),(0,r.jsxs)("div",{className:x().gradeImages,children:[(0,r.jsx)("img",{src:"/images/grading/lcd-b1.jpg",alt:"Grade B LCD Example 1"}),(0,r.jsx)("img",{src:"/images/grading/lcd-b2.jpg",alt:"Grade B LCD Example 2"})]})]}),(0,r.jsxs)("div",{className:x().gradingCard,children:[(0,r.jsx)("div",{className:x().gradeBadge,style:{backgroundColor:"#FFC107"},children:"Grade C"}),(0,r.jsx)("h3",{children:"Grade C (Fair)"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"Fully functional"}),(0,r.jsx)("li",{children:"Visible scratches when screen is on"}),(0,r.jsx)("li",{children:"Touch functionality works"}),(0,r.jsx)("li",{children:"May have noticeable discoloration"}),(0,r.jsx)("li",{children:"Typically aftermarket"})]}),(0,r.jsxs)("div",{className:x().gradeImages,children:[(0,r.jsx)("img",{src:"/images/grading/lcd-c1.jpg",alt:"Grade C LCD Example 1"}),(0,r.jsx)("img",{src:"/images/grading/lcd-c2.jpg",alt:"Grade C LCD Example 2"})]})]}),(0,r.jsxs)("div",{className:x().gradingCard,children:[(0,r.jsx)("div",{className:x().gradeBadge,style:{backgroundColor:"#F44336"},children:"Grade D"}),(0,r.jsx)("h3",{children:"Grade D (Poor)"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"Functional but with issues"}),(0,r.jsx)("li",{children:"Heavy scratches or cracks"}),(0,r.jsx)("li",{children:"Touch may have dead spots"}),(0,r.jsx)("li",{children:"Significant discoloration or burn-in"}),(0,r.jsx)("li",{children:"Low-quality aftermarket"})]}),(0,r.jsxs)("div",{className:x().gradeImages,children:[(0,r.jsx)("img",{src:"/images/grading/lcd-d1.jpg",alt:"Grade D LCD Example 1"}),(0,r.jsx)("img",{src:"/images/grading/lcd-d2.jpg",alt:"Grade D LCD Example 2"})]})]})]}),(0,r.jsxs)("div",{className:x().gradingNote,children:[(0,r.jsx)("h3",{children:"LCD Buyback Program"}),(0,r.jsxs)("p",{children:["We buy back LCD screens in all conditions. The grade of your LCD will determine the buyback value. Submit your LCD screens through our ",(0,r.jsx)(c(),{href:"/lcd-buyback",children:"LCD Buyback Program"})," to receive a quote."]})]})]}),"devices"===d&&(0,r.jsxs)("div",{className:x().gradingSection,children:[(0,r.jsx)("h2",{children:"Complete Device Grading"}),(0,r.jsxs)("div",{className:x().gradingCards,children:[(0,r.jsxs)("div",{className:x().gradingCard,children:[(0,r.jsx)("div",{className:x().gradeBadge,style:{backgroundColor:"#4CAF50"},children:"Grade A"}),(0,r.jsx)("h3",{children:"Grade A (Excellent)"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"Like new condition"}),(0,r.jsx)("li",{children:"No visible scratches or marks"}),(0,r.jsx)("li",{children:"100% functional with no issues"}),(0,r.jsx)("li",{children:"Battery health above 90%"}),(0,r.jsx)("li",{children:"All components original and working"})]}),(0,r.jsxs)("div",{className:x().gradeImages,children:[(0,r.jsx)("img",{src:"/images/grading/device-a1.jpg",alt:"Grade A Device Example 1"}),(0,r.jsx)("img",{src:"/images/grading/device-a2.jpg",alt:"Grade A Device Example 2"})]})]}),(0,r.jsxs)("div",{className:x().gradingCard,children:[(0,r.jsx)("div",{className:x().gradeBadge,style:{backgroundColor:"#2196F3"},children:"Grade B"}),(0,r.jsx)("h3",{children:"Grade B (Good)"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"Good condition with minor wear"}),(0,r.jsx)("li",{children:"Light scratches on body or screen"}),(0,r.jsx)("li",{children:"Fully functional"}),(0,r.jsx)("li",{children:"Battery health 80-90%"}),(0,r.jsx)("li",{children:"All components working properly"})]}),(0,r.jsxs)("div",{className:x().gradeImages,children:[(0,r.jsx)("img",{src:"/images/grading/device-b1.jpg",alt:"Grade B Device Example 1"}),(0,r.jsx)("img",{src:"/images/grading/device-b2.jpg",alt:"Grade B Device Example 2"})]})]}),(0,r.jsxs)("div",{className:x().gradingCard,children:[(0,r.jsx)("div",{className:x().gradeBadge,style:{backgroundColor:"#FFC107"},children:"Grade C"}),(0,r.jsx)("h3",{children:"Grade C (Fair)"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"Noticeable wear and tear"}),(0,r.jsx)("li",{children:"Visible scratches or minor dents"}),(0,r.jsx)("li",{children:"Functional with minor issues"}),(0,r.jsx)("li",{children:"Battery health 70-80%"}),(0,r.jsx)("li",{children:"May have non-original components"})]}),(0,r.jsxs)("div",{className:x().gradeImages,children:[(0,r.jsx)("img",{src:"/images/grading/device-c1.jpg",alt:"Grade C Device Example 1"}),(0,r.jsx)("img",{src:"/images/grading/device-c2.jpg",alt:"Grade C Device Example 2"})]})]}),(0,r.jsxs)("div",{className:x().gradingCard,children:[(0,r.jsx)("div",{className:x().gradeBadge,style:{backgroundColor:"#F44336"},children:"Grade D"}),(0,r.jsx)("h3",{children:"Grade D (Poor)"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"Heavy wear and tear"}),(0,r.jsx)("li",{children:"Significant scratches, dents, or cracks"}),(0,r.jsx)("li",{children:"Functional but with multiple issues"}),(0,r.jsx)("li",{children:"Battery health below 70%"}),(0,r.jsx)("li",{children:"Multiple non-original components"})]}),(0,r.jsxs)("div",{className:x().gradeImages,children:[(0,r.jsx)("img",{src:"/images/grading/device-d1.jpg",alt:"Grade D Device Example 1"}),(0,r.jsx)("img",{src:"/images/grading/device-d2.jpg",alt:"Grade D Device Example 2"})]})]})]})]}),"batteries"===d&&(0,r.jsxs)("div",{className:x().gradingSection,children:[(0,r.jsx)("h2",{children:"Battery Grading"}),(0,r.jsxs)("div",{className:x().gradingCards,children:[(0,r.jsxs)("div",{className:x().gradingCard,children:[(0,r.jsx)("div",{className:x().gradeBadge,style:{backgroundColor:"#4CAF50"},children:"Grade A"}),(0,r.jsx)("h3",{children:"Grade A (Excellent)"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"90-100% capacity"}),(0,r.jsx)("li",{children:"No swelling or damage"}),(0,r.jsx)("li",{children:"Original OEM battery"}),(0,r.jsx)("li",{children:"Less than 100 charge cycles"}),(0,r.jsx)("li",{children:"Holds charge perfectly"})]})]}),(0,r.jsxs)("div",{className:x().gradingCard,children:[(0,r.jsx)("div",{className:x().gradeBadge,style:{backgroundColor:"#2196F3"},children:"Grade B"}),(0,r.jsx)("h3",{children:"Grade B (Good)"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"80-90% capacity"}),(0,r.jsx)("li",{children:"No visible damage"}),(0,r.jsx)("li",{children:"OEM or high-quality replacement"}),(0,r.jsx)("li",{children:"100-300 charge cycles"}),(0,r.jsx)("li",{children:"Holds charge well"})]})]}),(0,r.jsxs)("div",{className:x().gradingCard,children:[(0,r.jsx)("div",{className:x().gradeBadge,style:{backgroundColor:"#FFC107"},children:"Grade C"}),(0,r.jsx)("h3",{children:"Grade C (Fair)"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"70-80% capacity"}),(0,r.jsx)("li",{children:"Minor wear but no damage"}),(0,r.jsx)("li",{children:"May be aftermarket"}),(0,r.jsx)("li",{children:"300-500 charge cycles"}),(0,r.jsx)("li",{children:"Holds charge but drains faster"})]})]}),(0,r.jsxs)("div",{className:x().gradingCard,children:[(0,r.jsx)("div",{className:x().gradeBadge,style:{backgroundColor:"#F44336"},children:"Grade D"}),(0,r.jsx)("h3",{children:"Grade D (Poor)"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"Below 70% capacity"}),(0,r.jsx)("li",{children:"May show signs of swelling"}),(0,r.jsx)("li",{children:"Typically aftermarket"}),(0,r.jsx)("li",{children:"Over 500 charge cycles"}),(0,r.jsx)("li",{children:"Poor charge retention"})]})]})]}),(0,r.jsxs)("div",{className:x().gradingNote,children:[(0,r.jsx)("h3",{children:"Battery Safety"}),(0,r.jsx)("p",{children:"We do not accept batteries with visible damage, leakage, or severe swelling as they pose safety risks. All batteries are tested for capacity and performance before grading."})]})]})]}),(0,r.jsxs)("div",{className:x().gradingCTA,children:[(0,r.jsx)("h2",{children:"Ready to Sell or Trade In?"}),(0,r.jsx)("p",{children:"Whether you're looking to sell your old devices or trade them in for an upgrade, our fair grading system ensures you get the best value for your electronics."}),(0,r.jsxs)("div",{className:x().ctaButtons,children:[(0,r.jsx)(c(),{href:"/lcd-buyback",className:x().primaryButton,children:"LCD Buyback Program"}),(0,r.jsx)(c(),{href:"/device-system?instock=1",className:x().secondaryButton,children:"View In-Stock Devices"})]})]}),(0,r.jsxs)("div",{className:x().gradingNote,style:{marginTop:"3rem"},children:[(0,r.jsx)("h3",{children:"Exclusive Access for Registered Users"}),(0,r.jsxs)("p",{children:["As a registered user, you have exclusive access to our complete inventory of graded devices. Visit our ",(0,r.jsx)(c(),{href:"/device-system",children:"Device Inventory System"})," to browse all available devices, filter by grade, and place orders directly."]})]})]})})]})}},27180:function(e){e.exports={gradingHeader:"DeviceGrading_gradingHeader__G6YUn",gradingTabs:"DeviceGrading_gradingTabs__8Kgt3",gradingTab:"DeviceGrading_gradingTab__UfuWD",active:"DeviceGrading_active__KHhO0",gradingContent:"DeviceGrading_gradingContent__XgfDa",gradingSection:"DeviceGrading_gradingSection__OTFGM",gradingCards:"DeviceGrading_gradingCards__yJk97",gradingCard:"DeviceGrading_gradingCard__tmuHL",gradeBadge:"DeviceGrading_gradeBadge__nMlQY",gradeImages:"DeviceGrading_gradeImages__Jvaa9",gradingNote:"DeviceGrading_gradingNote__ssLAD",gradingCTA:"DeviceGrading_gradingCTA__zweZb",ctaButtons:"DeviceGrading_ctaButtons__8sLdl",primaryButton:"DeviceGrading_primaryButton__lx_P4",secondaryButton:"DeviceGrading_secondaryButton__q_SMd"}},11163:function(e,i,a){e.exports=a(43079)}},function(e){e.O(0,[1664,5675,6029,2888,9774,179],function(){return e(e.s=7837)}),_N_E=e.O()}]);