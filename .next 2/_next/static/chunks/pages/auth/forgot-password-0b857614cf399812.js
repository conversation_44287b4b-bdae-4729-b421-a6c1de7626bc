(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4076],{99104:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/auth/forgot-password",function(){return a(49278)}])},49278:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return o}});var n=a(85893),r=a(67294),t=a(41664),i=a.n(t);function o(){let[e,s]=(0,r.useState)(""),[a,t]=(0,r.useState)(""),[o,l]=(0,r.useState)(""),[c,d]=(0,r.useState)(!1),u=async s=>{s.preventDefault(),d(!0),l(""),t("");try{let s=await fetch("/api/auth/forgot-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})}),a=await s.json();s.ok?t("Password reset instructions have been sent to your email."):l(a.message||"An error occurred. Please try again.")}catch(e){l("An error occurred. Please try again.")}finally{d(!1)}};return(0,n.jsx)("div",{className:"container",children:(0,n.jsxs)("div",{className:"auth-form",children:[(0,n.jsx)("h1",{children:"Forgot Password"}),o&&(0,n.jsx)("div",{className:"error-message",children:(0,n.jsx)("p",{children:o})}),a&&(0,n.jsx)("div",{className:"success-message",children:(0,n.jsx)("p",{children:a})}),(0,n.jsx)("p",{className:"auth-description",children:"Enter your email address and we'll send you instructions to reset your password."}),(0,n.jsxs)("form",{onSubmit:u,children:[(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsx)("label",{htmlFor:"email",children:"Email"}),(0,n.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>s(e.target.value),required:!0})]}),(0,n.jsx)("button",{type:"submit",className:"btn btn-primary",disabled:c,children:c?"Sending...":"Reset Password"})]}),(0,n.jsx)("div",{className:"auth-links",children:(0,n.jsx)("p",{className:"auth-link",children:(0,n.jsx)(i(),{href:"/auth/signin",children:"Back to Sign In"})})})]})})}}},function(e){e.O(0,[1664,2888,9774,179],function(){return e(e.s=99104)}),_N_E=e.O()}]);