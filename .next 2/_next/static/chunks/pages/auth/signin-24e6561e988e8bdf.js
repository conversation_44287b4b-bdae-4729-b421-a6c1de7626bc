(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[65],{12413:function(e,r,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/auth/signin",function(){return t(50696)}])},50696:function(e,r,t){"use strict";t.r(r),t.d(r,{__N_SSP:function(){return m},default:function(){return j}});var n=t(85893),s=t(67294),o=t(33299),i=t(11163),a=t(41664),c=t.n(a);t(9008);var l=t(4298),u=t.n(l),d=t(82928),h=t.n(d),_=t(34155),g=e=>{let{onVerify:r,onError:t,action:o}=e,i=(0,s.useRef)(null),a=(0,s.useRef)(null);return(0,s.useEffect)(()=>{let e=()=>{if(window.turnstile&&i.current){if(a.current&&window.turnstile){try{window.turnstile.remove(a.current)}catch(e){console.warn("Failed to remove Turnstile widget:",e)}a.current=null}try{a.current=window.turnstile.render(i.current,{sitekey:_.env.NEXT_PUBLIC_CLOUDFLARE_TURNSTILE_SITE_KEY||"1x00000000000000000000AA",theme:"light",action:o||"generic_action",callback:e=>{r&&r(e)},"error-callback":e=>{console.error("Turnstile error:",e),t&&t(e)}})}catch(e){console.error("Failed to render Turnstile widget:",e),t&&t("Failed to render security widget")}}};return window.turnstile?e():window.onloadTurnstileCallback=e,()=>{if(a.current&&window.turnstile){try{window.turnstile.remove(a.current)}catch(e){console.warn("Failed to remove Turnstile widget during cleanup:",e)}a.current=null}}},[r,t,o]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(u(),{src:"https://challenges.cloudflare.com/turnstile/v0/api.js?onload=onloadTurnstileCallback",async:!0,defer:!0,strategy:"afterInteractive"}),(0,n.jsx)("div",{ref:i,className:h().turnstileContainer})]})},f=()=>(0,n.jsxs)("div",{className:h().securityBadge,children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,n.jsx)("path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"})}),(0,n.jsxs)("span",{children:["Protected by Cloudflare | ",(0,n.jsx)("a",{href:"https://www.cloudflare.com/trust-hub/privacy-and-data-protection/",target:"_blank",rel:"noopener noreferrer",children:"Privacy Policy"})]})]}),w=e=>{let{type:r="info",children:t}=e;return(0,n.jsxs)("div",{className:"".concat(h().securityMessage," ").concat(h()[r]),children:[(()=>{switch(r){case"info":return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,n.jsx)("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),(0,n.jsx)("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"})]});case"warning":return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),(0,n.jsx)("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),(0,n.jsx)("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})]});case"error":return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,n.jsx)("line",{x1:"15",y1:"9",x2:"9",y2:"15"}),(0,n.jsx)("line",{x1:"9",y1:"9",x2:"15",y2:"15"})]});case"success":return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}),(0,n.jsx)("polyline",{points:"22 4 12 14.01 9 11.01"})]});default:return null}})(),(0,n.jsx)("p",{children:t})]})};t(42474);var x=t(59235),p=t.n(x),y=t(56029),m=!0;function j(e){let{csrfToken:r}=e,[t,a]=(0,s.useState)(""),[l,u]=(0,s.useState)(""),[d,h]=(0,s.useState)(""),[_,x]=(0,s.useState)(!1),[m,j]=(0,s.useState)(!0),[v,S]=(0,s.useState)(""),[k,P]=(0,s.useState)(null),C=(0,i.useRouter)(),{callbackUrl:N,error:A}=C.query;(0,s.useEffect)(()=>{(async function(){await (0,o.getSession)()?C.replace(N||"/"):j(!1)})(),A&&("CredentialsSignin"===A?h("Invalid email or password"):h(A))},[C,N,A]);let b=async e=>{if(e.preventDefault(),x(!0),h(""),P(null),!v){h("Please complete the security check"),x(!1);return}try{{let e=await fetch("/api/security/verify-turnstile",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:v})});if(!(await e.json()).success){h("Security check failed. Please try again."),x(!1);return}}let e=await (0,o.signIn)("credentials",{redirect:!1,email:t,password:l,callbackUrl:N||"/"});e.error?(h("Invalid email or password"),P({type:"warning",message:"Multiple failed login attempts may result in temporary account lockout."}),x(!1)):e.url&&e.url.includes("requiresTwoFactor=true")?C.push("/auth/2fa".concat(N?"?callbackUrl=".concat(encodeURIComponent(N)):"")):N&&N.includes("/account")?setTimeout(()=>{C.push("/account")},500):C.push(N||"/")}catch(e){console.error("Sign in error:",e),h("An error occurred. Please try again."),x(!1)}};return m?(0,n.jsx)(y.Z,{title:"Sign In - Midas Technical Solutions",description:"Sign in to your Midas Technical Solutions account.",children:(0,n.jsx)("div",{className:p().mainContent,children:(0,n.jsx)("div",{className:p().authForm,children:(0,n.jsxs)("div",{className:"loading-spinner",children:[(0,n.jsx)("div",{className:"spinner"}),(0,n.jsx)("p",{children:"Loading..."})]})})})}):(0,n.jsx)(y.Z,{title:"Sign In - Midas Technical Solutions",description:"Sign in to your Midas Technical Solutions account.",children:(0,n.jsx)("div",{className:p().mainContent,children:(0,n.jsxs)("div",{className:p().authForm,children:[(0,n.jsx)("h1",{children:"Sign In"}),d&&(0,n.jsx)("div",{className:"error-message",children:(0,n.jsx)("p",{children:d})}),k&&(0,n.jsx)(w,{type:k.type,children:k.message}),(0,n.jsxs)("form",{onSubmit:b,children:[(0,n.jsx)("input",{name:"csrfToken",type:"hidden",defaultValue:r}),(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsx)("label",{htmlFor:"email",children:"Email"}),(0,n.jsx)("input",{id:"email",type:"email",value:t,onChange:e=>a(e.target.value),required:!0})]}),(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsxs)("div",{className:"password-header",children:[(0,n.jsx)("label",{htmlFor:"password",children:"Password"}),(0,n.jsx)(c(),{href:"/auth/forgot-password",className:"forgot-password-link",children:"Forgot password?"})]}),(0,n.jsx)("input",{id:"password",type:"password",value:l,onChange:e=>u(e.target.value),required:!0})]}),(0,n.jsx)(g,{onVerify:e=>S(e),onError:()=>S(""),action:"signin"}),(0,n.jsx)("button",{type:"submit",className:"btn btn-primary",disabled:_||!v,children:_?"Signing in...":"Sign In"})]}),(0,n.jsx)(f,{}),(0,n.jsx)("div",{className:"auth-separator",children:(0,n.jsx)("span",{children:"OR"})}),(0,n.jsx)("button",{onClick:()=>{(0,o.signIn)("google",{callbackUrl:N||"/"})},className:"btn btn-google",type:"button",children:"Sign in with Google"}),(0,n.jsxs)("p",{className:"auth-link",children:["Don't have an account? ",(0,n.jsx)(c(),{href:"/auth/register",children:"Register"})]})]})})})}},82928:function(e){e.exports={turnstileContainer:"Security_turnstileContainer__kzfRQ",securityBadge:"Security_securityBadge__ughJR",securityMessage:"Security_securityMessage__SV9Dv",info:"Security_info__4n6Wd",warning:"Security_warning__rRWIf",error:"Security_error__fn177",success:"Security_success__CTj8u",securityBanner:"Security_securityBanner__HMk8D"}},59235:function(e){e.exports={header:"AuthPages_header__9rsly",headerContainer:"AuthPages_headerContainer__iFVZY",logo:"AuthPages_logo__TQKnf",searchBar:"AuthPages_searchBar__rSUIB",navigation:"AuthPages_navigation__f0G3I",signInLink:"AuthPages_signInLink__S67jn",mainContent:"AuthPages_mainContent__s1GZc",authForm:"AuthPages_authForm__vB_1b",footer:"AuthPages_footer__AV5vM",footerContainer:"AuthPages_footerContainer__uWCPy",footerTop:"AuthPages_footerTop__e2_PI",footerNewsletter:"AuthPages_footerNewsletter__z_Dwx",footerForm:"AuthPages_footerForm__4lLaX",footerServices:"AuthPages_footerServices__O9IEZ",footerService:"AuthPages_footerService__p6LrV",footerServiceIcon:"AuthPages_footerServiceIcon__tcXzK",footerServiceName:"AuthPages_footerServiceName__EyykS",footerServiceDescription:"AuthPages_footerServiceDescription__z7RXD",footerMiddle:"AuthPages_footerMiddle__IFHjm",footerColumn:"AuthPages_footerColumn__tj16E",footerLinks:"AuthPages_footerLinks__jRZHn",footerBottom:"AuthPages_footerBottom__pK1fh",footerCopyright:"AuthPages_footerCopyright__xiE_l",footerPaymentMethods:"AuthPages_footerPaymentMethods__2sHlK",footerPaymentIcon:"AuthPages_footerPaymentIcon__HJ7Rj"}}},function(e){e.O(0,[3714,1664,5675,8764,9956,6029,2888,9774,179],function(){return e(e.s=12413)}),_N_E=e.O()}]);