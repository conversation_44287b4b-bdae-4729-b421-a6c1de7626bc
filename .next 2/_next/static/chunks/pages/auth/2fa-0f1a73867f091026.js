(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7313],{66473:function(e,t,o){(window.__NEXT_P=window.__NEXT_P||[]).push(["/auth/2fa",function(){return o(40250)}])},40250:function(e,t,o){"use strict";o.r(t),o.d(t,{default:function(){return f}});var r=o(85893),i=o(67294),n=o(11163),a=o(33299),s=o(9008),c=o.n(s),u=o(91435),d=o.n(u),l=e=>{let{userId:t,email:o,methods:s,callbackUrl:c}=e,[u,l]=(0,i.useState)((null==s?void 0:s.preferred_method)||"email"),[_,h]=(0,i.useState)(["","","","","",""]),[f,p]=(0,i.useState)(""),[m,w]=(0,i.useState)(!1),[g,A]=(0,i.useState)(""),[v,T]=(0,i.useState)(0),[F,y]=(0,i.useState)(!1),[C,j]=(0,i.useState)(null),k=(0,i.useRef)([]),x=(0,n.useRouter)();(0,i.useEffect)(()=>{"email"===u||"sms"===u?S(u):"duo"===u&&b()},[u]),(0,i.useEffect)(()=>{if(v>0){let e=setTimeout(()=>T(v-1),1e3);return()=>clearTimeout(e)}},[v]);let b=async()=>{try{w(!0),A("");let e=await fetch("/api/auth/2fa/duo-init",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t})});if(e.ok){let t=await e.json();j(t),window.Duo&&window.Duo.init({host:t.host,sig_request:t.sigRequest,iframe:"duo-iframe"})}else{let t=await e.json();A(t.message||"Failed to initialize DUO authentication")}}catch(e){A("An error occurred while initializing DUO authentication"),console.error(e)}finally{w(!1)}},S=async e=>{try{w(!0),A("");let o=await fetch("/api/auth/2fa/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t,method:e})});if(o.ok)T(60);else{let t=await o.json();A(t.message||"Failed to send ".concat(e," verification code"))}}catch(t){A("An error occurred while sending ".concat(e," verification code")),console.error(t)}finally{w(!1)}},N=(e,t)=>{if(t&&!/^\d+$/.test(t))return;let o=[..._];o[e]=t,h(o),t&&e<5&&k.current[e+1].focus()},P=(e,t)=>{"Backspace"===t.key&&!_[e]&&e>0&&k.current[e-1].focus()},B=async()=>{try{w(!0),A("");let e=_.join("");if(6!==e.length){A("Please enter a 6-digit verification code"),w(!1);return}let o=await fetch("/api/auth/2fa/verify-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t,method:u,code:e})});if(o.ok){let e=await (0,a.signIn)("2fa-completion",{redirect:!1,userId:t,twoFactorVerified:!0});e.error?A(e.error):x.push(c||"/")}else{let e=await o.json();A(e.message||"Invalid verification code")}}catch(e){A("An error occurred while verifying the code"),console.error(e)}finally{w(!1)}},I=async()=>{try{if(w(!0),A(""),!f||f.length<8){A("Please enter a valid backup code"),w(!1);return}let e=await fetch("/api/auth/2fa/verify-backup-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t,code:f})});if(e.ok){let e=await (0,a.signIn)("2fa-completion",{redirect:!1,userId:t,twoFactorVerified:!0});e.error?A(e.error):x.push(c||"/")}else{let t=await e.json();A(t.message||"Invalid backup code")}}catch(e){A("An error occurred while verifying the backup code"),console.error(e)}finally{w(!1)}};return(0,r.jsxs)("div",{className:d().verificationContainer,children:[(0,r.jsx)("h2",{children:"Two-Factor Authentication"}),(0,r.jsx)("p",{className:d().verificationDescription,children:"For added security, please verify your identity with a second factor."}),g&&(0,r.jsx)("div",{className:d().error,children:g}),F?(0,r.jsxs)("div",{className:d().backupCodeForm,children:[(0,r.jsx)("p",{children:"Enter one of your backup codes:"}),(0,r.jsx)("input",{type:"text",className:d().backupCodeInput,value:f,onChange:e=>p(e.target.value.toUpperCase()),placeholder:"Enter backup code",maxLength:10}),(0,r.jsx)("button",{className:d().verifyButton,onClick:I,disabled:m||!f,children:m?"Verifying...":"Verify Backup Code"}),(0,r.jsx)("div",{className:d().resendContainer,children:(0,r.jsx)("span",{className:d().resendLink,onClick:()=>y(!1),children:"Back to verification methods"})})]}):"duo"===u?(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{children:"Complete the DUO authentication process:"}),(0,r.jsx)("div",{className:d().duoContainer,children:(0,r.jsx)("iframe",{id:"duo-iframe",width:"100%",height:"330",frameBorder:"0",title:"DUO Authentication","data-host":null==C?void 0:C.host,"data-sig-request":null==C?void 0:C.sigRequest})})]}):(0,r.jsxs)("div",{className:d().verificationForm,children:[(0,r.jsxs)("p",{children:["Enter the 6-digit verification code sent to your ","email"===u?"email":"phone",":"]}),(0,r.jsx)("div",{className:d().codeInput,onPaste:e=>{e.preventDefault();let t=e.clipboardData.getData("text");/^\d{6}$/.test(t)&&(h(t.split("")),k.current[5].focus())},children:_.map((e,t)=>(0,r.jsx)("input",{type:"text",maxLength:1,value:e,onChange:e=>N(t,e.target.value),onKeyDown:e=>P(t,e),ref:e=>k.current[t]=e,autoFocus:0===t},t))}),(0,r.jsx)("button",{className:d().verifyButton,onClick:B,disabled:m||_.some(e=>!e),children:m?"Verifying...":"Verify Code"}),(0,r.jsx)("div",{className:d().resendContainer,children:v>0?(0,r.jsxs)("span",{children:["Resend code in ",(0,r.jsxs)("span",{className:d().countdown,children:[v,"s"]})]}):(0,r.jsx)("span",{className:d().resendLink,onClick:()=>S(u),children:"Resend verification code"})})]}),!F&&(0,r.jsxs)("div",{className:d().alternativeMethod,children:[(0,r.jsx)("h3",{children:"Verification Methods"}),(0,r.jsxs)("div",{children:[(null==s?void 0:s.email_enabled)&&(0,r.jsx)("button",{className:d().methodButton,onClick:()=>l("email"),disabled:"email"===u,children:"Email"}),(null==s?void 0:s.sms_enabled)&&(0,r.jsx)("button",{className:d().methodButton,onClick:()=>l("sms"),disabled:"sms"===u,children:"SMS"}),(null==s?void 0:s.duo_enabled)&&(0,r.jsx)("button",{className:d().methodButton,onClick:()=>l("duo"),disabled:"duo"===u,children:"DUO Security"})]}),(0,r.jsx)("span",{className:d().backupCodeLink,onClick:()=>y(!0),children:"Use a backup code"})]})]})},_=o(59235),h=o.n(_);function f(){let[e,t]=(0,i.useState)(!0),[o,s]=(0,i.useState)(""),[u,d]=(0,i.useState)(null),[_,f]=(0,i.useState)(""),[p,m]=(0,i.useState)(null),w=(0,n.useRouter)(),{callbackUrl:g}=w.query;return((0,i.useEffect)(()=>{(async function(){try{let e=await (0,a.getSession)();if(!e){w.replace("/auth/signin");return}if(!e.requiresTwoFactor){w.replace(g||"/");return}d(e.user.id),f(e.user.email),m(e.twoFactorMethods),t(!1)}catch(e){console.error("Error checking session:",e),s("An error occurred while checking your session"),t(!1)}})()},[w,g]),e)?(0,r.jsxs)("div",{className:h().container,children:[(0,r.jsx)(c(),{children:(0,r.jsx)("title",{children:"Two-Factor Authentication | MDTS Tech"})}),(0,r.jsx)("div",{className:h().formContainer,children:(0,r.jsxs)("div",{className:h().loading,children:[(0,r.jsx)("div",{className:h().spinner}),(0,r.jsx)("p",{children:"Loading..."})]})})]}):o?(0,r.jsxs)("div",{className:h().container,children:[(0,r.jsx)(c(),{children:(0,r.jsx)("title",{children:"Two-Factor Authentication | MDTS Tech"})}),(0,r.jsx)("div",{className:h().formContainer,children:(0,r.jsxs)("div",{className:h().error,children:[(0,r.jsx)("p",{children:o}),(0,r.jsx)("button",{className:h().button,onClick:()=>w.push("/auth/signin"),children:"Back to Sign In"})]})})]}):(0,r.jsxs)("div",{className:h().container,children:[(0,r.jsxs)(c(),{children:[(0,r.jsx)("title",{children:"Two-Factor Authentication | MDTS Tech"}),(null==p?void 0:p.duo_enabled)&&(0,r.jsx)("script",{src:"https://api-XXXX.duosecurity.com/frame/hosted/Duo-Web-v2.min.js"})]}),(0,r.jsx)("div",{className:h().formContainer,children:(0,r.jsx)(l,{userId:u,email:_,methods:p,callbackUrl:g})})]})}},91435:function(e){e.exports={twoFactorSetup:"TwoFactorAuth_twoFactorSetup__HyIsT",description:"TwoFactorAuth_description__VX2X1",error:"TwoFactorAuth_error__6om0q",success:"TwoFactorAuth_success__cEzBw",toggleContainer:"TwoFactorAuth_toggleContainer__ETt4Z",toggle:"TwoFactorAuth_toggle__hU6AN",toggleSwitch:"TwoFactorAuth_toggleSwitch___3OIO",slider:"TwoFactorAuth_slider__a02gE",methodsContainer:"TwoFactorAuth_methodsContainer__rYE69",method:"TwoFactorAuth_method__8B8c7",methodDescription:"TwoFactorAuth_methodDescription__V0UFV",testButton:"TwoFactorAuth_testButton__0BKy3",addPhoneLink:"TwoFactorAuth_addPhoneLink__ECIyQ",duoLink:"TwoFactorAuth_duoLink__Kh5_z",preferredMethod:"TwoFactorAuth_preferredMethod__vRlJf",radioGroup:"TwoFactorAuth_radioGroup__qCwKi",radio:"TwoFactorAuth_radio__0jQbC",backupCodes:"TwoFactorAuth_backupCodes__sg8lg",generateButton:"TwoFactorAuth_generateButton__wb6uy",codesContainer:"TwoFactorAuth_codesContainer__USZ60",warning:"TwoFactorAuth_warning___0wlx",codes:"TwoFactorAuth_codes__VHDTn",code:"TwoFactorAuth_code__uC_b2",closeButton:"TwoFactorAuth_closeButton__SGbty",printButton:"TwoFactorAuth_printButton__FU79P",loading:"TwoFactorAuth_loading__932Lk",spinner:"TwoFactorAuth_spinner__mo5NC",spin:"TwoFactorAuth_spin__Ud7VS",verificationContainer:"TwoFactorAuth_verificationContainer__tHKVG",verificationDescription:"TwoFactorAuth_verificationDescription__tQEXg",verificationForm:"TwoFactorAuth_verificationForm__mt4Tf",codeInput:"TwoFactorAuth_codeInput__Tt4JK",verifyButton:"TwoFactorAuth_verifyButton__wEAXB",resendContainer:"TwoFactorAuth_resendContainer__fs3LM",resendLink:"TwoFactorAuth_resendLink__PBmkh",countdown:"TwoFactorAuth_countdown__mL3Kb",alternativeMethod:"TwoFactorAuth_alternativeMethod__unCIl",methodButton:"TwoFactorAuth_methodButton__7g0wL",backupCodeLink:"TwoFactorAuth_backupCodeLink__hzy_k",backupCodeForm:"TwoFactorAuth_backupCodeForm__XbWxp",backupCodeInput:"TwoFactorAuth_backupCodeInput__iTQsW",duoContainer:"TwoFactorAuth_duoContainer__ezP8M",printHide:"TwoFactorAuth_printHide__856IU"}},59235:function(e){e.exports={header:"AuthPages_header__9rsly",headerContainer:"AuthPages_headerContainer__iFVZY",logo:"AuthPages_logo__TQKnf",searchBar:"AuthPages_searchBar__rSUIB",navigation:"AuthPages_navigation__f0G3I",signInLink:"AuthPages_signInLink__S67jn",mainContent:"AuthPages_mainContent__s1GZc",authForm:"AuthPages_authForm__vB_1b",footer:"AuthPages_footer__AV5vM",footerContainer:"AuthPages_footerContainer__uWCPy",footerTop:"AuthPages_footerTop__e2_PI",footerNewsletter:"AuthPages_footerNewsletter__z_Dwx",footerForm:"AuthPages_footerForm__4lLaX",footerServices:"AuthPages_footerServices__O9IEZ",footerService:"AuthPages_footerService__p6LrV",footerServiceIcon:"AuthPages_footerServiceIcon__tcXzK",footerServiceName:"AuthPages_footerServiceName__EyykS",footerServiceDescription:"AuthPages_footerServiceDescription__z7RXD",footerMiddle:"AuthPages_footerMiddle__IFHjm",footerColumn:"AuthPages_footerColumn__tj16E",footerLinks:"AuthPages_footerLinks__jRZHn",footerBottom:"AuthPages_footerBottom__pK1fh",footerCopyright:"AuthPages_footerCopyright__xiE_l",footerPaymentMethods:"AuthPages_footerPaymentMethods__2sHlK",footerPaymentIcon:"AuthPages_footerPaymentIcon__HJ7Rj"}},11163:function(e,t,o){e.exports=o(43079)}},function(e){e.O(0,[2888,9774,179],function(){return e(e.s=66473)}),_N_E=e.O()}]);