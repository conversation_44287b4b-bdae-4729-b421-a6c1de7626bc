(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3035],{26742:function(s,e,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/auth/reset-password",function(){return r(73791)}])},73791:function(s,e,r){"use strict";r.r(e),r.d(e,{default:function(){return l}});var a=r(85893),n=r(67294),t=r(11163),i=r(41664),o=r.n(i);function l(){let[s,e]=(0,n.useState)(""),[r,i]=(0,n.useState)(""),[l,d]=(0,n.useState)(""),[c,u]=(0,n.useState)(""),[h,m]=(0,n.useState)(!1),p=(0,t.useRouter)(),{token:w}=p.query,f=async e=>{if(e.preventDefault(),!w){u("Invalid or missing reset token");return}if(s!==r){u("Passwords do not match");return}if(s.length<8){u("Password must be at least 8 characters long");return}m(!0),u(""),d("");try{let e=await fetch("/api/auth/reset-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:w,password:s})}),r=await e.json();e.ok?(d("Your password has been reset successfully. You can now sign in with your new password."),setTimeout(()=>{p.push("/auth/signin")},3e3)):u(r.message||"An error occurred. Please try again.")}catch(s){u("An error occurred. Please try again.")}finally{m(!1)}};return(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"auth-form",children:[(0,a.jsx)("h1",{children:"Reset Password"}),c&&(0,a.jsx)("div",{className:"error-message",children:(0,a.jsx)("p",{children:c})}),l&&(0,a.jsx)("div",{className:"success-message",children:(0,a.jsx)("p",{children:l})}),w?(0,a.jsxs)("form",{onSubmit:f,children:[(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"password",children:"New Password"}),(0,a.jsx)("input",{id:"password",type:"password",value:s,onChange:s=>e(s.target.value),required:!0,minLength:8}),(0,a.jsx)("small",{children:"Password must be at least 8 characters long"})]}),(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,a.jsx)("input",{id:"confirmPassword",type:"password",value:r,onChange:s=>i(s.target.value),required:!0})]}),(0,a.jsx)("button",{type:"submit",className:"btn btn-primary",disabled:h,children:h?"Resetting...":"Reset Password"})]}):(0,a.jsxs)("div",{className:"error-message",children:[(0,a.jsx)("p",{children:"Invalid or missing reset token. Please request a new password reset link."}),(0,a.jsx)("p",{children:(0,a.jsx)(o(),{href:"/auth/forgot-password",children:"Request new password reset"})})]}),(0,a.jsx)("div",{className:"auth-links",children:(0,a.jsx)("p",{className:"auth-link",children:(0,a.jsx)(o(),{href:"/auth/signin",children:"Back to Sign In"})})})]})})}},11163:function(s,e,r){s.exports=r(43079)}},function(s){s.O(0,[1664,2888,9774,179],function(){return s(s.s=26742)}),_N_E=s.O()}]);