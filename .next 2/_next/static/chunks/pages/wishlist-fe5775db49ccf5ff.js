(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4737],{96712:function(s,i,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/wishlist",function(){return t(73077)}])},73077:function(s,i,t){"use strict";t.r(i),t.d(i,{default:function(){return m}});var e=t(85893),r=t(67294),l=t(9008),a=t.n(l),n=t(41664),o=t.n(n),c=t(11163),d=t(33299),h=t(56029),u=t(72354),_=t.n(u);function m(){let{data:s,status:i}=(0,d.useSession)();(0,c.useRouter)();let[t,l]=(0,r.useState)([]),[n,u]=(0,r.useState)(!0),[m,w]=(0,r.useState)(null);(0,r.useEffect)(()=>{(async()=>{try{if(u(!0),w(null),"unauthenticated"===i){let s=localStorage.getItem("wishlistItems"),i=s?JSON.parse(s):[];l(i),u(!1);return}l([{id:1,product_id:1,name:"iPhone 13 Pro LCD Screen",slug:"iphone-13-pro-lcd-screen",category:"iPhone Parts",price:89.99,discount_percentage:10,image_url:"/images/products/iphone-screen.jpg",added_at:"2023-05-15T10:30:00Z"},{id:2,product_id:2,name:"Samsung Galaxy S22 Battery",slug:"samsung-galaxy-s22-battery",category:"Samsung Parts",price:39.99,discount_percentage:0,image_url:"/images/products/samsung-battery.jpg",added_at:"2023-05-16T14:45:00Z"},{id:3,product_id:3,name:"Professional Repair Tool Kit",slug:"professional-repair-tool-kit",category:"Repair Tools",price:129.99,discount_percentage:15,image_url:"/images/products/repair-tools.jpg",added_at:"2023-05-18T09:15:00Z"},{id:4,product_id:5,name:'iPad Pro 12.9" LCD Assembly',slug:"ipad-pro-12-9-lcd-assembly",category:"iPad Parts",price:199.99,discount_percentage:5,image_url:"/images/products/ipad-screen.jpg",added_at:"2023-05-20T16:20:00Z"}])}catch(s){console.error("Error fetching wishlist items:",s),w("Failed to load wishlist items. Please try again later.")}finally{u(!1)}})()},[i]);let g=s=>{if(l(t.filter(i=>i.id!==s)),"unauthenticated"===i){let i=t.filter(i=>i.id!==s);localStorage.setItem("wishlistItems",JSON.stringify(i))}},p=async s=>{try{let i=await fetch("/api/cart",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:s.product_id,quantity:1})});if(!i.ok)throw Error("Failed to add item to cart");let t=await i.json();if(t.success)alert("".concat(s.name," added to cart!"));else throw Error(t.message||"Failed to add item to cart")}catch(s){console.error("Error adding item to cart:",s),alert("Failed to add item to cart. Please try again.")}};return(0,e.jsxs)(h.Z,{children:[(0,e.jsxs)(a(),{children:[(0,e.jsx)("title",{children:"My Wishlist | MDTS - Midas Technical Solutions"}),(0,e.jsx)("meta",{name:"description",content:"View and manage your saved items at MDTS - Midas Technical Solutions."})]}),(0,e.jsx)("main",{className:"main-content",children:(0,e.jsx)("div",{className:"container",children:(0,e.jsxs)("div",{className:_().wishlistContainer,children:[(0,e.jsxs)("div",{className:_().wishlistHeader,children:[(0,e.jsxs)("div",{children:[(0,e.jsx)("h1",{className:_().wishlistTitle,children:"My Wishlist"}),(0,e.jsx)("p",{className:_().wishlistSubtitle,children:"Items you've saved for later"})]}),t.length>0&&(0,e.jsx)("button",{onClick:()=>{window.confirm("Are you sure you want to clear your wishlist?")&&(l([]),"unauthenticated"===i&&localStorage.setItem("wishlistItems",JSON.stringify([])))},className:_().clearWishlistButton,children:"Clear Wishlist"})]}),n?(0,e.jsxs)("div",{className:_().loadingContainer,children:[(0,e.jsx)("div",{className:_().loadingSpinner}),(0,e.jsx)("p",{children:"Loading your wishlist..."})]}):m?(0,e.jsxs)("div",{className:_().errorMessage,children:[(0,e.jsx)("p",{children:m}),(0,e.jsx)("button",{onClick:()=>window.location.reload(),className:_().retryButton,children:"Try Again"})]}):0===t.length?(0,e.jsxs)("div",{className:_().emptyWishlist,children:[(0,e.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,e.jsx)("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})}),(0,e.jsx)("h3",{children:"Your wishlist is empty"}),(0,e.jsx)("p",{children:"Save items you're interested in for later."}),(0,e.jsx)(o(),{href:"/products",className:_().browseProductsButton,children:"Browse Products"})]}):(0,e.jsx)("div",{className:_().wishlistGrid,children:t.map(s=>(0,e.jsxs)("div",{className:_().wishlistCard,children:[(0,e.jsxs)("div",{className:_().wishlistImageContainer,children:[(0,e.jsx)("img",{src:s.image_url||"/images/placeholder.svg",alt:s.name,className:_().wishlistImage}),(0,e.jsx)("button",{className:_().removeWishlistItem,onClick:()=>g(s.id),title:"Remove from wishlist",children:(0,e.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,e.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,e.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),(0,e.jsxs)("div",{className:_().wishlistContent,children:[(0,e.jsx)("div",{className:_().wishlistCategory,children:s.category}),(0,e.jsx)("h3",{className:_().wishlistName,children:(0,e.jsx)(o(),{href:"/products/".concat(s.slug),children:s.name})}),(0,e.jsx)("div",{className:_().wishlistPrice,children:s.discount_percentage>0?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)("span",{className:_().wishlistOriginalPrice,children:["$",(s.price/(1-s.discount_percentage/100)).toFixed(2)]}),(0,e.jsxs)("span",{className:_().wishlistCurrentPrice,children:["$",s.price.toFixed(2)]})]}):(0,e.jsxs)("span",{className:_().wishlistCurrentPrice,children:["$",s.price.toFixed(2)]})}),(0,e.jsxs)("div",{className:_().wishlistButtons,children:[(0,e.jsx)(o(),{href:"/products/".concat(s.slug),className:_().viewDetailsButton,children:"View Details"}),(0,e.jsx)("button",{className:_().addToCartButton,onClick:()=>p(s),children:"Add to Cart"})]})]})]},s.id))})]})})})]})}},72354:function(s){s.exports={wishlistContainer:"Wishlist_wishlistContainer__mmr3Z",wishlistHeader:"Wishlist_wishlistHeader__hdKuY",wishlistTitle:"Wishlist_wishlistTitle__erS6e",wishlistSubtitle:"Wishlist_wishlistSubtitle__mPpHy",clearWishlistButton:"Wishlist_clearWishlistButton__oWks8",loadingContainer:"Wishlist_loadingContainer__ECOeL",loadingSpinner:"Wishlist_loadingSpinner__ZGTqL",spin:"Wishlist_spin__oEgyN",errorMessage:"Wishlist_errorMessage__iQEv7",retryButton:"Wishlist_retryButton__IhDOj",emptyWishlist:"Wishlist_emptyWishlist___d5eQ",browseProductsButton:"Wishlist_browseProductsButton__qx3lY",wishlistGrid:"Wishlist_wishlistGrid__N3qre",wishlistCard:"Wishlist_wishlistCard__sENLi",wishlistImageContainer:"Wishlist_wishlistImageContainer__m4s3Q",wishlistImage:"Wishlist_wishlistImage__SzmMz",removeWishlistItem:"Wishlist_removeWishlistItem__GIloR",wishlistContent:"Wishlist_wishlistContent__JK8gU",wishlistCategory:"Wishlist_wishlistCategory__O6mD5",wishlistName:"Wishlist_wishlistName__WWz6h",wishlistPrice:"Wishlist_wishlistPrice__VV9Az",wishlistOriginalPrice:"Wishlist_wishlistOriginalPrice__N4gEc",wishlistCurrentPrice:"Wishlist_wishlistCurrentPrice__hUdyf",wishlistButtons:"Wishlist_wishlistButtons__tzWZA",viewDetailsButton:"Wishlist_viewDetailsButton__KMel_",addToCartButton:"Wishlist_addToCartButton__9fFju"}},11163:function(s,i,t){s.exports=t(43079)}},function(s){s.O(0,[1664,5675,6029,2888,9774,179],function(){return s(s.s=96712)}),_N_E=s.O()}]);