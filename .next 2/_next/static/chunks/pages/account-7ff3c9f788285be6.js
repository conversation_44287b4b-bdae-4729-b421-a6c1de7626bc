(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7966],{84865:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/account",function(){return s(87385)}])},18670:function(e,t,s){"use strict";var n=s(85893),a=s(67294),i=s(70297),r=s.n(i);t.Z=e=>{let{url:t,title:s,description:i,image:o,platforms:c=["facebook","twitter","pinterest","linkedin","email","copy"]}=e,[l,d]=(0,a.useState)(!1),[h,u]=(0,a.useState)(!1),m=encodeURIComponent(t),p=encodeURIComponent(s),_=encodeURIComponent(i||""),x=o?encodeURIComponent(o):"",v={facebook:"https://www.facebook.com/sharer/sharer.php?u=".concat(m),twitter:"https://twitter.com/intent/tweet?url=".concat(m,"&text=").concat(p),pinterest:"https://pinterest.com/pin/create/button/?url=".concat(m,"&media=").concat(x,"&description=").concat(p),linkedin:"https://www.linkedin.com/sharing/share-offsite/?url=".concat(m),email:"mailto:?subject=".concat(p,"&body=").concat(_,"%0A%0A").concat(m)},g={facebook:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,n.jsx)("path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"})}),twitter:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,n.jsx)("path",{d:"M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"})}),pinterest:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,n.jsx)("path",{d:"M12 0a12 12 0 0 0-4.37 23.17c-.1-.94-.2-2.43.04-3.47l1.42-6.02s-.36-.72-.36-1.78c0-1.67.97-2.92 2.17-2.92 1.02 0 1.51.77 1.51 1.7 0 1.03-.66 2.57-1 4-.28 1.2.6 2.17 1.78 2.17 2.13 0 3.77-2.25 3.77-5.5 0-2.87-2.06-4.88-5-4.88-3.4 0-5.39 2.56-5.39 5.2 0 1.03.4 2.13.9 2.73.1.12.11.22.08.34l-.34 1.36c-.05.22-.18.27-.4.16-1.5-.7-2.43-2.89-2.43-4.65 0-3.78 2.75-7.26 7.92-7.26 4.17 0 7.4 2.97 7.4 6.93 0 4.14-2.6 7.46-6.2 7.46-1.21 0-2.35-.63-2.74-1.37l-.75 2.85c-.27 1.04-1 2.35-1.49 3.15.94.29 1.92.44 2.96.44a12 12 0 0 0 12-12A12 12 0 0 0 12 0z"})}),linkedin:(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:[(0,n.jsx)("path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"}),(0,n.jsx)("rect",{x:"2",y:"9",width:"4",height:"12"}),(0,n.jsx)("circle",{cx:"4",cy:"4",r:"2"})]}),email:(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"}),(0,n.jsx)("polyline",{points:"22,6 12,13 2,6"})]}),copy:(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),(0,n.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})]})},j={facebook:"Facebook",twitter:"Twitter",pinterest:"Pinterest",linkedin:"LinkedIn",email:"Email",copy:"Copy Link"},y=e=>{"copy"===e?navigator.clipboard.writeText(t).then(()=>{d(!0),u(!0),setTimeout(()=>{u(!1),setTimeout(()=>d(!1),300)},2e3)}):window.open(v[e],"_blank","width=600,height=400")};return(0,n.jsxs)("div",{className:r().socialShare,children:[(0,n.jsx)("div",{className:r().shareButtons,children:c.map(e=>(0,n.jsxs)("button",{className:"".concat(r().shareButton," ").concat(r()[e]),onClick:()=>y(e),"aria-label":"Share on ".concat(j[e]),title:j[e],children:[g[e],(0,n.jsx)("span",{className:r().buttonText,children:j[e]})]},e))}),l&&(0,n.jsx)("div",{className:"".concat(r().tooltip," ").concat(h?r().show:""),children:"Link copied to clipboard!"})]})}},87385:function(e,t,s){"use strict";s.r(t),s.d(t,{__N_SSP:function(){return T},default:function(){return L}});var n=s(85893),a=s(67294),i=s(33299),r=s(11163),o=s(9008),c=s.n(o),l=s(41664),d=s.n(l),h=s(44729),u=s.n(h),m=s(44339),p=s.n(m),_=e=>{var t,s,a;let{activeTab:r,onTabChange:o}=e,{data:c}=(0,i.useSession)(),l=(null==c?void 0:null===(t=c.user)||void 0===t?void 0:t.email)==="<EMAIL>"||(null==c?void 0:null===(s=c.user)||void 0===s?void 0:s.email)==="<EMAIL>"||(null==c?void 0:null===(a=c.user)||void 0===a?void 0:a.isAdmin)===!0;return(0,n.jsxs)("div",{className:"".concat(u().sidebar," ").concat(p().accountSidebar),children:[(0,n.jsxs)("nav",{className:"".concat(u().sidebarNav," ").concat(p().accountSidebarNav),children:[(0,n.jsxs)("div",{className:"".concat(u().navItem," ").concat("overview"===r?u().active:""),onClick:()=>o("overview"),children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("rect",{x:"3",y:"3",width:"7",height:"7"}),(0,n.jsx)("rect",{x:"14",y:"3",width:"7",height:"7"}),(0,n.jsx)("rect",{x:"14",y:"14",width:"7",height:"7"}),(0,n.jsx)("rect",{x:"3",y:"14",width:"7",height:"7"})]}),"Dashboard"]}),(0,n.jsxs)("div",{className:"".concat(u().navItem," ").concat("orders"===r?u().active:""),onClick:()=>o("orders"),children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"}),(0,n.jsx)("line",{x1:"3",y1:"6",x2:"21",y2:"6"}),(0,n.jsx)("path",{d:"M16 10a4 4 0 0 1-8 0"})]}),"Orders"]}),(0,n.jsxs)("div",{className:"".concat(u().navItem," ").concat("addresses"===r?u().active:""),onClick:()=>o("addresses"),children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),(0,n.jsx)("circle",{cx:"12",cy:"10",r:"3"})]}),"Addresses"]}),(0,n.jsxs)("div",{className:"".concat(u().navItem," ").concat("wishlist"===r?u().active:""),onClick:()=>o("wishlist"),children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,n.jsx)("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})}),"Wishlist"]}),(0,n.jsxs)("div",{className:"".concat(u().navItem," ").concat("preferences"===r?u().active:""),onClick:()=>o("preferences"),children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"3"}),(0,n.jsx)("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"})]}),"Preferences"]}),l&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:u().sidebarDivider,children:(0,n.jsx)("span",{children:"Admin"})}),(0,n.jsxs)("div",{className:"".concat(u().navItem," ").concat("integrations"===r?u().active:""),onClick:()=>o("integrations"),children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"}),(0,n.jsx)("path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"})]}),"Integrations"]}),(0,n.jsxs)("div",{className:"".concat(u().navItem," ").concat("inventory"===r?u().active:""),onClick:()=>o("inventory"),children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("polyline",{points:"21 8 21 21 3 21 3 8"}),(0,n.jsx)("rect",{x:"1",y:"3",width:"22",height:"5"}),(0,n.jsx)("line",{x1:"10",y1:"12",x2:"14",y2:"12"})]}),"Inventory"]})]})]}),(0,n.jsxs)("button",{className:u().logoutButton,onClick:()=>(0,i.signOut)({callbackUrl:"/"}),children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}),(0,n.jsx)("polyline",{points:"16 17 21 12 16 7"}),(0,n.jsx)("line",{x1:"21",y1:"12",x2:"9",y2:"12"})]}),"Sign Out"]})]})},x=e=>{let{session:t}=e,s=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{children:"Account Overview"}),(0,n.jsxs)("div",{className:u().overviewGrid,children:[(0,n.jsxs)("div",{className:u().overviewCard,children:[(0,n.jsx)("h3",{children:"Orders"}),(0,n.jsx)("div",{className:u().value,children:5}),(0,n.jsx)("p",{children:"View your order history"}),(0,n.jsx)(d(),{href:"/account?tab=orders",children:"View Orders"})]}),(0,n.jsxs)("div",{className:u().overviewCard,children:[(0,n.jsx)("h3",{children:"Wishlist"}),(0,n.jsx)("div",{className:u().value,children:8}),(0,n.jsx)("p",{children:"Items saved for later"}),(0,n.jsx)(d(),{href:"/account?tab=wishlist",children:"View Wishlist"})]}),(0,n.jsxs)("div",{className:u().overviewCard,children:[(0,n.jsx)("h3",{children:"Addresses"}),(0,n.jsx)("div",{className:u().value,children:2}),(0,n.jsx)("p",{children:"Saved shipping addresses"}),(0,n.jsx)(d(),{href:"/account?tab=addresses",children:"Manage Addresses"})]})]}),(0,n.jsxs)("div",{className:u().recentActivity,children:[(0,n.jsx)("h3",{children:"Recent Activity"}),[{id:1,type:"order",title:"Order Placed",description:"Order #12345 for $199.99",date:"2023-06-15T14:30:00Z"},{id:2,type:"wishlist",title:"Item Added to Wishlist",description:"iPhone 13 Pro OLED Screen",date:"2023-06-10T09:15:00Z"},{id:3,type:"address",title:"Address Updated",description:"Shipping address updated",date:"2023-06-05T16:45:00Z"}].map(e=>(0,n.jsxs)("div",{className:u().activityItem,children:[(0,n.jsxs)("div",{className:u().activityIcon,children:["order"===e.type&&(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"}),(0,n.jsx)("line",{x1:"3",y1:"6",x2:"21",y2:"6"}),(0,n.jsx)("path",{d:"M16 10a4 4 0 0 1-8 0"})]}),"wishlist"===e.type&&(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,n.jsx)("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})}),"address"===e.type&&(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),(0,n.jsx)("circle",{cx:"12",cy:"10",r:"3"})]})]}),(0,n.jsxs)("div",{className:u().activityContent,children:[(0,n.jsx)("h4",{children:e.title}),(0,n.jsx)("p",{children:e.description}),(0,n.jsx)("div",{className:u().activityDate,children:s(e.date)})]})]},e.id))]})]})};s(25675);var v=()=>{let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(!0),[r,o]=(0,a.useState)(null);(0,a.useEffect)(()=>{(async()=>{try{i(!0),await new Promise(e=>setTimeout(e,500)),t([{id:"ORD-12345",date:"2023-06-15T14:30:00Z",status:"delivered",total:199.99,items:[{id:1,name:"iPhone 13 Pro OLED Screen",price:129.99,quantity:1,image:"/images/iphone-screen.svg"},{id:2,name:"Professional Repair Tool Kit",price:69.99,quantity:1,image:"/images/repair-tools.svg"}]},{id:"ORD-12344",date:"2023-05-22T10:15:00Z",status:"shipped",total:89.99,items:[{id:3,name:"Samsung Galaxy S22 Battery",price:39.99,quantity:1,image:"/images/samsung-battery.svg"},{id:4,name:"Precision Screwdriver Set",price:49.99,quantity:1,image:"/images/screwdriver-set.svg"}]},{id:"ORD-12343",date:"2023-04-10T09:45:00Z",status:"delivered",total:129.99,items:[{id:5,name:"iPad Mini 5 Digitizer",price:79.99,quantity:1,image:"/images/ipad-mini.svg"},{id:6,name:"Heat Gun for Repairs",price:49.99,quantity:1,image:"/images/heat-gun.svg"}]}])}catch(e){console.error("Error fetching orders:",e),o("Failed to load orders. Please try again later.")}finally{i(!1)}})()},[]);let c=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),l=e=>{switch(e){case"pending":return u().statusPending;case"processing":return u().statusProcessing;case"shipped":return u().statusShipped;case"delivered":return u().statusDelivered;case"cancelled":return u().statusCancelled;default:return""}};return s?(0,n.jsxs)("div",{className:"loading-container",children:[(0,n.jsx)("div",{className:"loading-spinner"}),(0,n.jsx)("p",{children:"Loading your orders..."})]}):r?(0,n.jsxs)("div",{className:"error-message",children:[(0,n.jsx)("p",{children:r}),(0,n.jsx)("button",{onClick:()=>window.location.reload(),className:"btn btn-primary",children:"Try Again"})]}):0===e.length?(0,n.jsxs)("div",{className:u().emptyOrders,children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"}),(0,n.jsx)("line",{x1:"3",y1:"6",x2:"21",y2:"6"}),(0,n.jsx)("path",{d:"M16 10a4 4 0 0 1-8 0"})]}),(0,n.jsx)("h3",{children:"No orders yet"}),(0,n.jsx)("p",{children:"You haven't placed any orders yet."}),(0,n.jsx)(d(),{href:"/products",className:"btn btn-primary",children:"Start Shopping"})]}):(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{children:"Order History"}),(0,n.jsx)("p",{children:"View and track your orders"}),(0,n.jsx)("div",{className:u().ordersList,children:e.map(e=>(0,n.jsxs)("div",{className:u().orderCard,children:[(0,n.jsxs)("div",{className:u().orderHeader,children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:u().orderNumber,children:e.id}),(0,n.jsx)("div",{className:u().orderDate,children:c(e.date)})]}),(0,n.jsx)("div",{className:"".concat(u().orderStatus," ").concat(l(e.status)),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,n.jsx)("div",{className:u().orderItems,children:e.items.map(e=>(0,n.jsxs)("div",{className:u().orderItem,children:[(0,n.jsx)("img",{src:e.image||"/images/placeholder.svg",alt:e.name,className:u().orderItemImage}),(0,n.jsxs)("div",{className:u().orderItemDetails,children:[(0,n.jsx)("div",{className:u().orderItemName,children:e.name}),(0,n.jsxs)("div",{className:u().orderItemPrice,children:["$",e.price.toFixed(2)]}),(0,n.jsxs)("div",{className:u().orderItemQuantity,children:["Quantity: ",e.quantity]})]})]},e.id))}),(0,n.jsxs)("div",{className:u().orderFooter,children:[(0,n.jsxs)("div",{className:u().orderTotal,children:["Total: $",e.total.toFixed(2)]}),(0,n.jsxs)("div",{className:u().orderActions,children:[(0,n.jsx)(d(),{href:"/orders/".concat(e.id),className:"".concat(u().orderButton," ").concat(u().viewOrderButton),children:"View Order Details"}),("shipped"===e.status||"processing"===e.status)&&(0,n.jsx)(d(),{href:"/orders/track/".concat(e.id),className:"".concat(u().orderButton," ").concat(u().trackOrderButton),children:"Track Order"})]})]})]},e.id))})]})};let g=e=>{let{address:t,onSave:s,onCancel:i}=e,[r,o]=(0,a.useState)({type:(null==t?void 0:t.type)||"Home",isDefault:(null==t?void 0:t.isDefault)||!1,firstName:(null==t?void 0:t.firstName)||"",lastName:(null==t?void 0:t.lastName)||"",address1:(null==t?void 0:t.address1)||"",address2:(null==t?void 0:t.address2)||"",city:(null==t?void 0:t.city)||"",state:(null==t?void 0:t.state)||"",postalCode:(null==t?void 0:t.postalCode)||"",country:(null==t?void 0:t.country)||"United States",phone:(null==t?void 0:t.phone)||""}),c=e=>{let{name:t,value:s,type:n,checked:a}=e.target;o({...r,[t]:"checkbox"===n?a:s})};return(0,n.jsxs)("div",{className:u().addressForm,children:[(0,n.jsx)("h3",{children:t?"Edit Address":"Add New Address"}),(0,n.jsxs)("form",{onSubmit:e=>{e.preventDefault(),s(r)},children:[(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"addressType",children:"Address Type"}),(0,n.jsxs)("select",{id:"addressType",name:"type",value:r.type,onChange:c,required:!0,children:[(0,n.jsx)("option",{value:"Home",children:"Home"}),(0,n.jsx)("option",{value:"Work",children:"Work"}),(0,n.jsx)("option",{value:"Other",children:"Other"})]})]}),(0,n.jsxs)("div",{className:u().checkboxGroup,children:[(0,n.jsx)("input",{type:"checkbox",id:"isDefault",name:"isDefault",checked:r.isDefault,onChange:c}),(0,n.jsx)("label",{htmlFor:"isDefault",children:"Set as default address"})]}),(0,n.jsxs)("div",{className:u().formRow,children:[(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"firstName",children:"First Name"}),(0,n.jsx)("input",{type:"text",id:"firstName",name:"firstName",value:r.firstName,onChange:c,required:!0})]}),(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"lastName",children:"Last Name"}),(0,n.jsx)("input",{type:"text",id:"lastName",name:"lastName",value:r.lastName,onChange:c,required:!0})]})]}),(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"address1",children:"Address Line 1"}),(0,n.jsx)("input",{type:"text",id:"address1",name:"address1",value:r.address1,onChange:c,required:!0})]}),(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"address2",children:"Address Line 2 (Optional)"}),(0,n.jsx)("input",{type:"text",id:"address2",name:"address2",value:r.address2,onChange:c})]}),(0,n.jsxs)("div",{className:u().formRow,children:[(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"city",children:"City"}),(0,n.jsx)("input",{type:"text",id:"city",name:"city",value:r.city,onChange:c,required:!0})]}),(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"state",children:"State/Province"}),(0,n.jsx)("input",{type:"text",id:"state",name:"state",value:r.state,onChange:c,required:!0})]}),(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"postalCode",children:"Postal Code"}),(0,n.jsx)("input",{type:"text",id:"postalCode",name:"postalCode",value:r.postalCode,onChange:c,required:!0})]})]}),(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"country",children:"Country"}),(0,n.jsxs)("select",{id:"country",name:"country",value:r.country,onChange:c,required:!0,children:[(0,n.jsx)("option",{value:"United States",children:"United States"}),(0,n.jsx)("option",{value:"Canada",children:"Canada"}),(0,n.jsx)("option",{value:"United Kingdom",children:"United Kingdom"})]})]}),(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"phone",children:"Phone Number"}),(0,n.jsx)("input",{type:"tel",id:"phone",name:"phone",value:r.phone,onChange:c,required:!0})]}),(0,n.jsxs)("div",{className:u().formActions,children:[(0,n.jsx)("button",{type:"submit",className:u().saveButton,children:"Save Address"}),(0,n.jsx)("button",{type:"button",className:u().cancelButton,onClick:i,children:"Cancel"})]})]})]})};var j=()=>{let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(!0),[r,o]=(0,a.useState)(null),[c,l]=(0,a.useState)(!1),[d,h]=(0,a.useState)(null);(0,a.useEffect)(()=>{(async()=>{try{i(!0),await new Promise(e=>setTimeout(e,500)),t([{id:1,type:"Home",isDefault:!0,firstName:"John",lastName:"Doe",address1:"123 Main St",address2:"Apt 4B",city:"Vienna",state:"VA",postalCode:"22182",country:"United States",phone:"+****************"},{id:2,type:"Work",isDefault:!1,firstName:"John",lastName:"Doe",address1:"456 Business Ave",address2:"Suite 200",city:"Vienna",state:"VA",postalCode:"22182",country:"United States",phone:"+****************"}])}catch(e){console.error("Error fetching addresses:",e),o("Failed to load addresses. Please try again later.")}finally{i(!1)}})()},[]);let m=e=>{h(e),l(!0)},p=s=>{t(e.filter(e=>e.id!==s))},_=s=>{t(e.map(e=>({...e,isDefault:e.id===s})))};return s?(0,n.jsxs)("div",{className:"loading-container",children:[(0,n.jsx)("div",{className:"loading-spinner"}),(0,n.jsx)("p",{children:"Loading your addresses..."})]}):r?(0,n.jsxs)("div",{className:"error-message",children:[(0,n.jsx)("p",{children:r}),(0,n.jsx)("button",{onClick:()=>window.location.reload(),className:"btn btn-primary",children:"Try Again"})]}):(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{children:"Saved Addresses"}),(0,n.jsx)("p",{children:"Manage your shipping and billing addresses"}),c?(0,n.jsx)(g,{address:d,onSave:s=>{d?t(e.map(e=>e.id===d.id?{...s,id:e.id}:e)):t([...e,{...s,id:Date.now()}]),l(!1),h(null)},onCancel:()=>{l(!1),h(null)}}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:u().addressGrid,children:e.map(e=>(0,n.jsxs)("div",{className:"".concat(u().addressCard," ").concat(e.isDefault?u().default:""),children:[e.isDefault&&(0,n.jsx)("div",{className:u().defaultBadge,children:"Default"}),(0,n.jsx)("div",{className:u().addressType,children:e.type}),(0,n.jsxs)("div",{className:u().addressDetails,children:[(0,n.jsxs)("p",{children:[e.firstName," ",e.lastName]}),(0,n.jsx)("p",{children:e.address1}),e.address2&&(0,n.jsx)("p",{children:e.address2}),(0,n.jsxs)("p",{children:[e.city,", ",e.state," ",e.postalCode]}),(0,n.jsx)("p",{children:e.country}),(0,n.jsx)("p",{children:e.phone})]}),(0,n.jsxs)("div",{className:u().addressActions,children:[(0,n.jsx)("button",{className:u().editAddressButton,onClick:()=>m(e),children:"Edit"}),!e.isDefault&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("button",{className:u().editAddressButton,onClick:()=>_(e.id),children:"Set as Default"}),(0,n.jsx)("button",{className:u().deleteAddressButton,onClick:()=>p(e.id),children:"Delete"})]})]})]},e.id))}),(0,n.jsxs)("button",{className:u().addAddressButton,onClick:()=>{h(null),l(!0)},children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),(0,n.jsx)("line",{x1:"5",y1:"12",x2:"19",y2:"12"})]}),"Add New Address"]})]})]})};let y=(0,a.createContext)(),w=()=>(0,a.useContext)(y);var k=()=>{let[e,t]=(0,a.useState)(100),[s,i]=(0,a.useState)("USD"),[r,o]=(0,a.useState)("EUR"),[c,l]=(0,a.useState)(0),[d,h]=(0,a.useState)(0),[m,p]=(0,a.useState)(!1),[_,x]=(0,a.useState)(""),v={USD:1,EUR:.92,GBP:.79,CAD:1.37,AUD:1.52,JPY:149.82,CNY:7.24,INR:83.12,BRL:5.05,RUB:91.75,KRW:1345.78,CHF:.9,SGD:1.35,NZD:1.64,MXN:16.82,HKD:7.82,SEK:10.42,NOK:10.71,DKK:6.87,PLN:3.94,ZAR:18.45,AED:3.67,SAR:3.75,BTC:16e-6,ETH:31e-5};(0,a.useEffect)(()=>{g(),x(new Date().toLocaleString())},[s,r]);let g=()=>{p(!0);try{let t=v[r]/v[s];h(t),l((e*t).toFixed(2))}catch(e){console.error("Error converting currency:",e)}finally{p(!1)}};return(0,n.jsxs)("div",{className:u().currencyConverter,children:[(0,n.jsx)("h3",{children:"Currency Converter"}),(0,n.jsx)("p",{children:"Convert between different currencies using real-time exchange rates"}),(0,n.jsxs)("form",{onSubmit:e=>{e.preventDefault(),g(),x(new Date().toLocaleString())},className:u().converterForm,children:[(0,n.jsxs)("div",{className:u().converterInputs,children:[(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"amount",children:"Amount"}),(0,n.jsx)("input",{type:"number",id:"amount",value:e,onChange:e=>{t(e.target.value)},min:"0.01",step:"0.01",required:!0})]}),(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"fromCurrency",children:"From"}),(0,n.jsx)("select",{id:"fromCurrency",value:s,onChange:e=>{i(e.target.value)},children:Object.keys(v).map(e=>(0,n.jsx)("option",{value:e,children:e},"from-".concat(e)))})]}),(0,n.jsx)("div",{className:u().switchCurrencies,children:(0,n.jsx)("button",{type:"button",onClick:()=>{i(r),o(s)},"aria-label":"Switch currencies",children:"⇄"})}),(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"toCurrency",children:"To"}),(0,n.jsx)("select",{id:"toCurrency",value:r,onChange:e=>{o(e.target.value)},children:Object.keys(v).map(e=>(0,n.jsx)("option",{value:e,children:e},"to-".concat(e)))})]})]}),(0,n.jsx)("button",{type:"submit",className:u().convertButton,disabled:m,children:m?"Converting...":"Convert"})]}),(0,n.jsxs)("div",{className:u().conversionResult,children:[(0,n.jsxs)("div",{className:u().resultAmount,children:[(0,n.jsxs)("span",{children:[e," ",s," = "]}),(0,n.jsxs)("span",{className:u().convertedValue,children:[c," ",r]})]}),(0,n.jsx)("div",{className:u().exchangeRate,children:(0,n.jsxs)("span",{children:["1 ",s," = ",d.toFixed(6)," ",r]})}),(0,n.jsx)("div",{className:u().lastUpdated,children:(0,n.jsxs)("small",{children:["Last updated: ",_]})})]}),(0,n.jsx)("div",{className:u().disclaimer,children:(0,n.jsx)("p",{children:(0,n.jsx)("small",{children:"Disclaimer: Exchange rates are for informational purposes only and may vary at checkout. Actual conversion is performed at the time of transaction."})})})]})},f=()=>{let{theme:e,changeTheme:t}=w(),[s,i]=(0,a.useState)({emailNotifications:{orderUpdates:!0,promotions:!0,newProducts:!1,blog:!1},displayPreferences:{theme:"light",currency:"USD",language:"en"},privacySettings:{shareDataForAnalytics:!0,allowPersonalization:!0}}),[r,o]=(0,a.useState)(!1),[c,l]=(0,a.useState)(!1),[d,h]=(0,a.useState)(null);(0,a.useEffect)(()=>{i(t=>({...t,displayPreferences:{...t.displayPreferences,theme:e}}))},[e]);let m=e=>{let{name:t,checked:n}=e.target;i({...s,emailNotifications:{...s.emailNotifications,[t]:n}})},p=e=>{let{name:n,value:a}=e.target;"theme"===n&&t(a),i({...s,displayPreferences:{...s.displayPreferences,[n]:a}})},_=e=>{let{name:t,checked:n}=e.target;i({...s,privacySettings:{...s.privacySettings,[t]:n}})},x=e=>{localStorage.setItem("userPreferences",JSON.stringify(e))};(0,a.useEffect)(()=>{let e=localStorage.getItem("userPreferences");if(e)try{let t=JSON.parse(e);i(t)}catch(e){console.error("Error parsing saved preferences:",e)}},[]);let v=async e=>{e.preventDefault();try{o(!0),h(null),x(s),await new Promise(e=>setTimeout(e,800)),window.notificationCenter&&(s.emailNotifications.orderUpdates&&window.notificationCenter.addNotification("Order Updates Enabled","You will now receive notifications about your order status and shipping updates.","success"),s.emailNotifications.promotions&&window.notificationCenter.addNotification("Promotions Enabled","You will now receive notifications about special offers and discounts.","info"),s.emailNotifications.newProducts&&window.notificationCenter.addNotification("New Product Announcements Enabled","You will now receive notifications when new products are added to our store.","info"),s.emailNotifications.blog&&window.notificationCenter.addNotification("Blog Updates Enabled","You will now receive notifications about new blog posts and repair guides.","info")),l(!0),setTimeout(()=>{l(!1)},3e3)}catch(e){console.error("Error saving preferences:",e),h("Failed to save preferences. Please try again.")}finally{o(!1)}};return(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{children:"Account Preferences"}),(0,n.jsx)("p",{children:"Customize your account settings and preferences"}),(0,n.jsxs)("form",{className:u().preferencesForm,onSubmit:v,children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{children:"Email Notifications"}),(0,n.jsxs)("div",{className:u().checkboxGroup,children:[(0,n.jsx)("input",{type:"checkbox",id:"orderUpdates",name:"orderUpdates",checked:s.emailNotifications.orderUpdates,onChange:m}),(0,n.jsx)("label",{htmlFor:"orderUpdates",children:"Order updates and shipping notifications"})]}),(0,n.jsxs)("div",{className:u().checkboxGroup,children:[(0,n.jsx)("input",{type:"checkbox",id:"promotions",name:"promotions",checked:s.emailNotifications.promotions,onChange:m}),(0,n.jsx)("label",{htmlFor:"promotions",children:"Promotions and discounts"})]}),(0,n.jsxs)("div",{className:u().checkboxGroup,children:[(0,n.jsx)("input",{type:"checkbox",id:"newProducts",name:"newProducts",checked:s.emailNotifications.newProducts,onChange:m}),(0,n.jsx)("label",{htmlFor:"newProducts",children:"New product announcements"})]}),(0,n.jsxs)("div",{className:u().checkboxGroup,children:[(0,n.jsx)("input",{type:"checkbox",id:"blog",name:"blog",checked:s.emailNotifications.blog,onChange:m}),(0,n.jsx)("label",{htmlFor:"blog",children:"Blog posts and repair guides"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{children:"Display Preferences"}),(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"theme",children:"Theme"}),(0,n.jsxs)("select",{id:"theme",name:"theme",value:s.displayPreferences.theme,onChange:p,children:[(0,n.jsx)("option",{value:"light",children:"Light"}),(0,n.jsx)("option",{value:"dark",children:"Dark"}),(0,n.jsx)("option",{value:"system",children:"System Default"})]}),(0,n.jsx)("p",{className:u().helperText,children:"light"===s.displayPreferences.theme?"Using light theme":"dark"===s.displayPreferences.theme?"Using dark theme":"Using your system preferences"})]}),(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"currency",children:"Currency"}),(0,n.jsxs)("select",{id:"currency",name:"currency",value:s.displayPreferences.currency,onChange:p,children:[(0,n.jsx)("option",{value:"USD",children:"USD - US Dollar"}),(0,n.jsx)("option",{value:"EUR",children:"EUR - Euro"}),(0,n.jsx)("option",{value:"GBP",children:"GBP - British Pound"}),(0,n.jsx)("option",{value:"CAD",children:"CAD - Canadian Dollar"}),(0,n.jsx)("option",{value:"AUD",children:"AUD - Australian Dollar"}),(0,n.jsx)("option",{value:"JPY",children:"JPY - Japanese Yen"}),(0,n.jsx)("option",{value:"CNY",children:"CNY - Chinese Yuan"}),(0,n.jsx)("option",{value:"INR",children:"INR - Indian Rupee"}),(0,n.jsx)("option",{value:"BRL",children:"BRL - Brazilian Real"}),(0,n.jsx)("option",{value:"RUB",children:"RUB - Russian Ruble"}),(0,n.jsx)("option",{value:"KRW",children:"KRW - South Korean Won"}),(0,n.jsx)("option",{value:"CHF",children:"CHF - Swiss Franc"}),(0,n.jsx)("option",{value:"SGD",children:"SGD - Singapore Dollar"}),(0,n.jsx)("option",{value:"NZD",children:"NZD - New Zealand Dollar"}),(0,n.jsx)("option",{value:"MXN",children:"MXN - Mexican Peso"}),(0,n.jsx)("option",{value:"HKD",children:"HKD - Hong Kong Dollar"}),(0,n.jsx)("option",{value:"SEK",children:"SEK - Swedish Krona"}),(0,n.jsx)("option",{value:"NOK",children:"NOK - Norwegian Krone"}),(0,n.jsx)("option",{value:"DKK",children:"DKK - Danish Krone"}),(0,n.jsx)("option",{value:"PLN",children:"PLN - Polish Złoty"}),(0,n.jsx)("option",{value:"ZAR",children:"ZAR - South African Rand"}),(0,n.jsx)("option",{value:"AED",children:"AED - United Arab Emirates Dirham"}),(0,n.jsx)("option",{value:"SAR",children:"SAR - Saudi Riyal"}),(0,n.jsx)("option",{value:"BTC",children:"BTC - Bitcoin"}),(0,n.jsx)("option",{value:"ETH",children:"ETH - Ethereum"})]}),(0,n.jsx)("p",{className:u().helperText,children:"Currency conversion is applied at checkout based on current exchange rates"})]}),(0,n.jsxs)("div",{className:u().formGroup,children:[(0,n.jsx)("label",{htmlFor:"language",children:"Language"}),(0,n.jsxs)("select",{id:"language",name:"language",value:s.displayPreferences.language,onChange:p,children:[(0,n.jsx)("option",{value:"en",children:"English"}),(0,n.jsx)("option",{value:"es",children:"Espa\xf1ol"}),(0,n.jsx)("option",{value:"fr",children:"Fran\xe7ais"}),(0,n.jsx)("option",{value:"de",children:"Deutsch"}),(0,n.jsx)("option",{value:"it",children:"Italiano"}),(0,n.jsx)("option",{value:"pt",children:"Portugu\xeas"}),(0,n.jsx)("option",{value:"ru",children:"Русский"}),(0,n.jsx)("option",{value:"zh",children:"中文"}),(0,n.jsx)("option",{value:"ja",children:"日本語"}),(0,n.jsx)("option",{value:"ko",children:"한국어"}),(0,n.jsx)("option",{value:"ar",children:"العربية"})]})]}),(0,n.jsx)("div",{className:u().divider}),(0,n.jsx)(k,{})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{children:"Privacy Settings"}),(0,n.jsxs)("div",{className:u().checkboxGroup,children:[(0,n.jsx)("input",{type:"checkbox",id:"shareDataForAnalytics",name:"shareDataForAnalytics",checked:s.privacySettings.shareDataForAnalytics,onChange:_}),(0,n.jsx)("label",{htmlFor:"shareDataForAnalytics",children:"Share anonymous usage data to help us improve our services"})]}),(0,n.jsxs)("div",{className:u().checkboxGroup,children:[(0,n.jsx)("input",{type:"checkbox",id:"allowPersonalization",name:"allowPersonalization",checked:s.privacySettings.allowPersonalization,onChange:_}),(0,n.jsx)("label",{htmlFor:"allowPersonalization",children:"Allow personalized product recommendations based on your browsing history"})]})]}),(0,n.jsx)("button",{type:"submit",className:u().saveButton,disabled:r,children:r?"Saving...":"Save Preferences"}),c&&(0,n.jsx)("div",{className:u().successMessage,children:"Your preferences have been saved successfully."}),d&&(0,n.jsx)("div",{className:u().errorMessage,children:d})]})]})},N=s(18670),b=()=>{let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(!0),[r,o]=(0,a.useState)(null);(0,a.useEffect)(()=>{(async()=>{try{i(!0),await new Promise(e=>setTimeout(e,500)),t([{id:1,product_id:1,name:"iPhone 13 Pro OLED Screen",slug:"iphone-13-pro-oled-screen",category:"iPhone Parts",price:129.99,discount_percentage:10,image_url:"/images/iphone-screen.svg",added_at:"2023-06-10T09:15:00Z"},{id:2,product_id:2,name:"Professional Repair Tool Kit",slug:"professional-repair-tool-kit",category:"Tools",price:89.99,discount_percentage:0,image_url:"/images/repair-tools.svg",added_at:"2023-06-05T14:30:00Z"},{id:3,product_id:3,name:"Samsung Galaxy S22 Battery",slug:"samsung-galaxy-s22-battery",category:"Samsung Parts",price:39.99,discount_percentage:15,image_url:"/images/samsung-battery.svg",added_at:"2023-05-28T11:45:00Z"},{id:4,product_id:5,name:'iPad Pro 12.9" LCD Assembly',slug:"ipad-pro-12-9-lcd-assembly",category:"iPad Parts",price:199.99,discount_percentage:5,image_url:"/images/ipad-screen.svg",added_at:"2023-05-20T16:20:00Z"}])}catch(e){console.error("Error fetching wishlist items:",e),o("Failed to load wishlist items. Please try again later.")}finally{i(!1)}})()},[]);let c=s=>{t(e.filter(e=>e.id!==s))},l=e=>{alert("Added ".concat(e.name," to cart"))};return s?(0,n.jsxs)("div",{className:"loading-container",children:[(0,n.jsx)("div",{className:"loading-spinner"}),(0,n.jsx)("p",{children:"Loading your wishlist..."})]}):r?(0,n.jsxs)("div",{className:"error-message",children:[(0,n.jsx)("p",{children:r}),(0,n.jsx)("button",{onClick:()=>window.location.reload(),className:"btn btn-primary",children:"Try Again"})]}):0===e.length?(0,n.jsxs)("div",{className:u().emptyWishlist,children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,n.jsx)("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})}),(0,n.jsx)("h3",{children:"Your wishlist is empty"}),(0,n.jsx)("p",{children:"Save items you're interested in for later."}),(0,n.jsx)(d(),{href:"/products",className:u().browseProductsButton,children:"Browse Products"})]}):(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:u().wishlistHeader,children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{children:"My Wishlist"}),(0,n.jsx)("p",{children:"Items you've saved for later"})]}),(0,n.jsxs)("div",{className:u().shareWishlist,children:[(0,n.jsx)("h4",{children:"Share Your Wishlist"}),(0,n.jsx)(N.Z,{url:window.location.href,title:"Check out my wishlist at MDTS - Midas Technical Solutions",description:"Here are some products I'm interested in from MDTS - Midas Technical Solutions.",platforms:["facebook","twitter","email","copy"]})]})]}),(0,n.jsx)("div",{className:u().wishlistGrid,children:e.map(e=>(0,n.jsxs)("div",{className:u().wishlistCard,children:[(0,n.jsxs)("div",{className:u().wishlistImageContainer,children:[(0,n.jsx)("img",{src:e.image_url||"/images/placeholder.svg",alt:e.name,className:u().wishlistImage}),(0,n.jsx)("button",{className:u().removeWishlistItem,onClick:()=>c(e.id),title:"Remove from wishlist",children:(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,n.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),(0,n.jsxs)("div",{className:u().wishlistContent,children:[(0,n.jsx)("div",{className:u().wishlistCategory,children:e.category}),(0,n.jsx)("h3",{className:u().wishlistName,children:(0,n.jsx)(d(),{href:"/products/".concat(e.slug),children:e.name})}),(0,n.jsx)("div",{className:u().wishlistPrice,children:e.discount_percentage>0?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("span",{className:u().wishlistOriginalPrice,children:["$",(e.price/(1-e.discount_percentage/100)).toFixed(2)]}),(0,n.jsxs)("span",{className:u().wishlistCurrentPrice,children:["$",e.price.toFixed(2)]})]}):(0,n.jsxs)("span",{className:u().wishlistCurrentPrice,children:["$",e.price.toFixed(2)]})}),(0,n.jsxs)("div",{className:u().wishlistActions,children:[(0,n.jsxs)("button",{className:u().addToCartFromWishlist,onClick:()=>l(e),children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("circle",{cx:"9",cy:"21",r:"1"}),(0,n.jsx)("circle",{cx:"20",cy:"21",r:"1"}),(0,n.jsx)("path",{d:"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"})]}),"Add to Cart"]}),(0,n.jsx)(d(),{href:"/products/".concat(e.slug),className:u().viewProductFromWishlist,children:(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),(0,n.jsx)("circle",{cx:"12",cy:"12",r:"3"})]})})]})]})]},e.id))})]})},S=s(15748),C=s.n(S),A=()=>{let[e,t]=(0,a.useState)("connected"),[s,i]=(0,a.useState)("zap_123456789abcdef"),[r,o]=(0,a.useState)([{id:1,name:"New Order",url:"https://hooks.zapier.com/hooks/catch/123456/abcdef/",active:!0},{id:2,name:"Low Inventory Alert",url:"https://hooks.zapier.com/hooks/catch/123456/ghijkl/",active:!0},{id:3,name:"Customer Support Request",url:"https://hooks.zapier.com/hooks/catch/123456/mnopqr/",active:!1}]),[c,l]=(0,a.useState)("connected"),[d,h]=(0,a.useState)("secret_123456789abcdef"),[u,m]=(0,a.useState)([{id:1,name:"Inventory",databaseId:"abc123def456",active:!0},{id:2,name:"Orders",databaseId:"ghi789jkl012",active:!0},{id:3,name:"Customers",databaseId:"mno345pqr678",active:!1}]),[p,_]=(0,a.useState)("connected"),[x,v]=(0,a.useState)("https://n8n.mdtstech.store"),[g,j]=(0,a.useState)("n8n_api_123456789abcdef"),[y,w]=(0,a.useState)([{id:1,name:"Order Processing",workflowId:"wf_123456",active:!0},{id:2,name:"Inventory Sync",workflowId:"wf_234567",active:!0},{id:3,name:"Customer Notifications",workflowId:"wf_345678",active:!0},{id:4,name:"Supplier Orders",workflowId:"wf_456789",active:!1}]),[k,f]=(0,a.useState)("connected"),[N,b]=(0,a.useState)("4s_api_123456789abcdef"),[S,A]=(0,a.useState)([{id:1,name:"Main Store",storeId:"store_123456",active:!0},{id:2,name:"Wholesale Store",storeId:"store_234567",active:!1}]),[I,P]=(0,a.useState)("connected"),[B,D]=(0,a.useState)("connected"),[T,L]=(0,a.useState)("disconnected"),[M,F]=(0,a.useState)([{id:1,name:"US Marketplace",accountId:"amzn_123456",active:!0},{id:2,name:"Canada Marketplace",accountId:"amzn_234567",active:!1}]),[O,E]=(0,a.useState)([{id:1,name:"Main eBay Store",accountId:"ebay_123456",active:!0}]),[R,U]=(0,a.useState)(!1),[G,K]=(0,a.useState)(!1),[Z,H]=(0,a.useState)(null),[W,z]=(0,a.useState)("automation"),V=e=>{o(t=>t.map(t=>t.id===e?{...t,active:!t.active}:t))},q=e=>{m(t=>t.map(t=>t.id===e?{...t,active:!t.active}:t))};return(0,n.jsxs)("div",{className:C().adminSection,children:[(0,n.jsx)("h2",{children:"Integrations"}),(0,n.jsx)("p",{className:C().sectionDescription,children:"Connect your store with third-party services to automate workflows and manage data."}),G&&(0,n.jsxs)("div",{className:C().successMessage,children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}),(0,n.jsx)("polyline",{points:"22 4 12 14.01 9 11.01"})]}),(0,n.jsx)("p",{children:"Integration settings updated successfully!"})]}),Z&&(0,n.jsxs)("div",{className:C().errorMessage,children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,n.jsx)("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),(0,n.jsx)("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})]}),(0,n.jsx)("p",{children:Z})]}),(0,n.jsxs)("div",{className:C().integrationCards,children:[(0,n.jsxs)("div",{className:C().integrationCard,children:[(0,n.jsxs)("div",{className:C().integrationHeader,children:[(0,n.jsx)("div",{className:C().integrationLogo,children:(0,n.jsx)("img",{src:"/images/zapier-logo.png",alt:"Zapier Logo"})}),(0,n.jsxs)("div",{className:C().integrationInfo,children:[(0,n.jsx)("h3",{children:"Zapier"}),(0,n.jsx)("p",{children:"Connect your store with 3,000+ apps"}),(0,n.jsxs)("div",{className:C().integrationStatus,children:[(0,n.jsx)("span",{className:"".concat(C().statusIndicator," ").concat("connected"===e?C().connected:C().disconnected)}),(0,n.jsx)("span",{children:"connected"===e?"Connected":"Disconnected"})]})]})]}),(0,n.jsx)("div",{className:C().integrationBody,children:"connected"===e?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:C().integrationField,children:[(0,n.jsx)("label",{children:"API Key"}),(0,n.jsxs)("div",{className:C().apiKeyField,children:[(0,n.jsx)("input",{type:"password",value:s,readOnly:!0}),(0,n.jsx)("button",{className:C().copyButton,onClick:()=>{navigator.clipboard.writeText(s),K(!0),setTimeout(()=>K(!1),3e3)},children:(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),(0,n.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})]})})]})]}),(0,n.jsxs)("div",{className:C().webhooksList,children:[(0,n.jsx)("h4",{children:"Active Webhooks"}),r.map(e=>(0,n.jsxs)("div",{className:C().webhookItem,children:[(0,n.jsxs)("div",{className:C().webhookInfo,children:[(0,n.jsx)("span",{className:C().webhookName,children:e.name}),(0,n.jsx)("span",{className:C().webhookUrl,children:e.url})]}),(0,n.jsxs)("label",{className:C().switch,children:[(0,n.jsx)("input",{type:"checkbox",checked:e.active,onChange:()=>V(e.id)}),(0,n.jsx)("span",{className:C().slider})]})]},e.id))]}),(0,n.jsx)("button",{className:C().disconnectButton,onClick:()=>{U(!0),setTimeout(()=>{t("disconnected"),U(!1),K(!0),setTimeout(()=>{K(!1)},3e3)},1500)},disabled:R,children:R?"Disconnecting...":"Disconnect"})]}):(0,n.jsxs)("div",{className:C().connectContainer,children:[(0,n.jsx)("p",{children:"Connect your Zapier account to automate workflows and integrate with thousands of apps."}),(0,n.jsx)("button",{className:C().connectButton,onClick:()=>{U(!0),setTimeout(()=>{t("connected"),U(!1),K(!0),setTimeout(()=>{K(!1)},3e3)},1500)},disabled:R,children:R?"Connecting...":"Connect with Zapier"})]})})]}),(0,n.jsxs)("div",{className:C().integrationCard,children:[(0,n.jsxs)("div",{className:C().integrationHeader,children:[(0,n.jsx)("div",{className:C().integrationLogo,children:(0,n.jsx)("img",{src:"/images/notion-logo.png",alt:"Notion Logo"})}),(0,n.jsxs)("div",{className:C().integrationInfo,children:[(0,n.jsx)("h3",{children:"Notion"}),(0,n.jsx)("p",{children:"Sync your store data with Notion databases"}),(0,n.jsxs)("div",{className:C().integrationStatus,children:[(0,n.jsx)("span",{className:"".concat(C().statusIndicator," ").concat("connected"===c?C().connected:C().disconnected)}),(0,n.jsx)("span",{children:"connected"===c?"Connected":"Disconnected"})]})]})]}),(0,n.jsx)("div",{className:C().integrationBody,children:"connected"===c?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:C().integrationField,children:[(0,n.jsx)("label",{children:"API Key"}),(0,n.jsxs)("div",{className:C().apiKeyField,children:[(0,n.jsx)("input",{type:"password",value:d,readOnly:!0}),(0,n.jsx)("button",{className:C().copyButton,onClick:()=>{navigator.clipboard.writeText(d),K(!0),setTimeout(()=>K(!1),3e3)},children:(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),(0,n.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})]})})]})]}),(0,n.jsxs)("div",{className:C().databasesList,children:[(0,n.jsx)("h4",{children:"Connected Databases"}),u.map(e=>(0,n.jsxs)("div",{className:C().databaseItem,children:[(0,n.jsxs)("div",{className:C().databaseInfo,children:[(0,n.jsx)("span",{className:C().databaseName,children:e.name}),(0,n.jsx)("span",{className:C().databaseId,children:e.databaseId})]}),(0,n.jsxs)("label",{className:C().switch,children:[(0,n.jsx)("input",{type:"checkbox",checked:e.active,onChange:()=>q(e.id)}),(0,n.jsx)("span",{className:C().slider})]})]},e.id))]}),(0,n.jsx)("button",{className:C().disconnectButton,onClick:()=>{U(!0),setTimeout(()=>{l("disconnected"),U(!1),K(!0),setTimeout(()=>{K(!1)},3e3)},1500)},disabled:R,children:R?"Disconnecting...":"Disconnect"})]}):(0,n.jsxs)("div",{className:C().connectContainer,children:[(0,n.jsx)("p",{children:"Connect your Notion workspace to sync inventory, orders, and customer data."}),(0,n.jsx)("button",{className:C().connectButton,onClick:()=>{U(!0),setTimeout(()=>{l("connected"),U(!1),K(!0),setTimeout(()=>{K(!1)},3e3)},1500)},disabled:R,children:R?"Connecting...":"Connect with Notion"})]})})]})]}),(0,n.jsxs)("div",{className:C().integrationHelp,children:[(0,n.jsx)("h3",{children:"Need help with integrations?"}),(0,n.jsxs)("p",{children:["Check out our ",(0,n.jsx)("a",{href:"/docs/integrations",target:"_blank",children:"integration documentation"})," or ",(0,n.jsx)("a",{href:"/contact",target:"_blank",children:"contact support"})," for assistance."]})]})]})},I=s(99186),P=s.n(I),B=s(34155);function D(){let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(!0),[r,o]=(0,a.useState)(null),[c,l]=(0,a.useState)(""),[d,h]=(0,a.useState)("all"),[u,m]=(0,a.useState)("name"),[p,_]=(0,a.useState)("asc"),[x,v]=(0,a.useState)([]),[g,j]=(0,a.useState)(""),[y,w]=(0,a.useState)(!1),[k,f]=(0,a.useState)({name:"",sku:"",category:"",price:"",cost:"",stock:"",description:""}),N=["iPhone Parts","Samsung Parts","iPad Parts","MacBook Parts","Repair Tools","Accessories"];(0,a.useEffect)(()=>{(async()=>{try{i(!0),await new Promise(e=>setTimeout(e,1e3)),t([{id:1,name:"iPhone 13 Pro LCD Screen",sku:"IP13P-LCD",category:"iPhone Parts",price:89.99,cost:45,stock:24,low_stock_threshold:5,description:"Replacement LCD screen for iPhone 13 Pro",created_at:"2023-01-15T10:30:00Z",updated_at:"2023-06-20T14:45:00Z"},{id:2,name:"Samsung Galaxy S22 Battery",sku:"SG22-BAT",category:"Samsung Parts",price:39.99,cost:18.5,stock:42,low_stock_threshold:10,description:"Replacement battery for Samsung Galaxy S22",created_at:"2023-02-10T09:15:00Z",updated_at:"2023-06-18T11:20:00Z"},{id:3,name:'iPad Pro 12.9" LCD Assembly',sku:"IPP129-LCD",category:"iPad Parts",price:199.99,cost:110,stock:8,low_stock_threshold:3,description:'Replacement LCD assembly for iPad Pro 12.9"',created_at:"2023-03-05T13:45:00Z",updated_at:"2023-06-15T16:30:00Z"},{id:4,name:'MacBook Pro Retina Display 13"',sku:"MBP13-DISP",category:"MacBook Parts",price:299.99,cost:180,stock:5,low_stock_threshold:2,description:'Replacement Retina display for 13" MacBook Pro',created_at:"2023-03-20T11:30:00Z",updated_at:"2023-06-10T09:15:00Z"},{id:5,name:"Professional Repair Tool Kit",sku:"TOOL-KIT-PRO",category:"Repair Tools",price:129.99,cost:65,stock:18,low_stock_threshold:5,description:"Professional toolkit for mobile device repair",created_at:"2023-04-12T15:20:00Z",updated_at:"2023-06-05T10:45:00Z"},{id:6,name:"iPhone 12 Battery",sku:"IP12-BAT",category:"iPhone Parts",price:29.99,cost:12.5,stock:35,low_stock_threshold:10,description:"Replacement battery for iPhone 12",created_at:"2023-05-08T14:10:00Z",updated_at:"2023-06-01T13:25:00Z"}])}catch(e){console.error("Error fetching products:",e),o("Failed to load products. Please try again.")}finally{i(!1)}})()},[]);let b=e.filter(e=>{let t=e.name.toLowerCase().includes(c.toLowerCase())||e.sku.toLowerCase().includes(c.toLowerCase()),s="all"===d||e.category===d;return t&&s}).sort((e,t)=>{let s=0;return"name"===u?s=e.name.localeCompare(t.name):"sku"===u?s=e.sku.localeCompare(t.sku):"price"===u?s=e.price-t.price:"stock"===u&&(s=e.stock-t.stock),"asc"===p?s:-s}),S=(e,t)=>{e.target.checked?v([...x,t]):v(x.filter(e=>e!==t))},C=e=>{let{name:t,value:s}=e.target;f({...k,[t]:s})},A=a.useRef(null),I=async s=>{let n=s.target.files[0];if(!n)return;let a=n.name.split(".").pop().toLowerCase();if(!["csv","xlsx","xls"].includes(a)){alert("Please upload a CSV or Excel file"),s.target.value=null;return}let r=new FormData;r.append("file",n),r.append("filename",n.name),r.append("importType","inventory"),i(!0);try{let s=new FileReader;await new Promise((e,t)=>{s.onload=t=>e(t.target.result),s.onerror=e=>t(e),"csv"===a?s.readAsText(n):s.readAsArrayBuffer(n)}),await new Promise(e=>setTimeout(e,2e3));let i=[];i="csv"===a?[{id:e.length+1,name:"iPhone 15 Pro Max Screen Assembly",sku:"CSV-IP15PM-SCRN",category:"iPhone Parts",price:189.99,cost:110,stock:12,low_stock_threshold:4,description:"Premium quality iPhone 15 Pro Max screen assembly with True Tone support",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:e.length+2,name:"iPhone 15 Battery Replacement",sku:"CSV-IP15-BAT",category:"iPhone Parts",price:49.99,cost:25,stock:28,low_stock_threshold:8,description:"High capacity replacement battery for iPhone 15",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:e.length+3,name:"Samsung Galaxy S24 Ultra Back Glass",sku:"CSV-S24U-BACK",category:"Samsung Parts",price:39.99,cost:18.5,stock:15,low_stock_threshold:5,description:"Original quality back glass for Samsung Galaxy S24 Ultra",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]:[{id:e.length+1,name:"iPad Air 5 LCD Assembly",sku:"XLS-IPAD-AIR5",category:"iPad Parts",price:149.99,cost:85,stock:8,low_stock_threshold:3,description:"Complete LCD assembly for iPad Air 5th generation",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:e.length+2,name:"MacBook Pro M2 Trackpad",sku:"XLS-MBP-M2-TP",category:"MacBook Parts",price:89.99,cost:45,stock:6,low_stock_threshold:2,description:"Replacement trackpad for MacBook Pro M2",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:e.length+3,name:"Heat Gun for Phone Repair",sku:"XLS-HEAT-GUN",category:"Repair Tools",price:59.99,cost:32,stock:10,low_stock_threshold:3,description:"Professional heat gun for phone screen removal and repair",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:e.length+4,name:"Precision Screwdriver Set (24pcs)",sku:"XLS-SCREWDRIVER-24",category:"Repair Tools",price:29.99,cost:14.5,stock:20,low_stock_threshold:5,description:"24-piece precision screwdriver set for electronics repair",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}],t([...e,...i]),alert("Successfully imported ".concat(i.length," products from ").concat(n.name))}catch(e){alert("Error processing file: ".concat(e.message))}finally{i(!1),s.target.value=null}},D=async s=>{i(!0);try{if(!({"4seller":{apiUrl:"https://api.4seller.com/v2/inventory",apiKey:B.env.NEXT_PUBLIC_4SELLER_API_KEY||"demo-key",accountId:B.env.NEXT_PUBLIC_4SELLER_ACCOUNT_ID||"demo-account"},ebay:{apiUrl:"https://api.ebay.com/sell/inventory/v1/inventory_item",apiKey:B.env.NEXT_PUBLIC_EBAY_API_KEY||"demo-key",accountId:B.env.NEXT_PUBLIC_EBAY_ACCOUNT_ID||"demo-account"},amazon:{apiUrl:"https://sellingpartnerapi-na.amazon.com/listings/2021-08-01/items",apiKey:B.env.NEXT_PUBLIC_AMAZON_API_KEY||"demo-key",accountId:B.env.NEXT_PUBLIC_AMAZON_SELLER_ID||"demo-account"},tiktok:{apiUrl:"https://open-api.tiktokglobalshop.com/api/products/products",apiKey:B.env.NEXT_PUBLIC_TIKTOK_API_KEY||"demo-key",accountId:B.env.NEXT_PUBLIC_TIKTOK_SHOP_ID||"demo-account"}})[s])throw Error("Platform ".concat(s," is not supported"));await new Promise(e=>setTimeout(e,2e3));let n=[];"4seller"===s?n=[{id:e.length+1,name:"iPhone 14 Pro Screen Assembly",sku:"4S-IP14PRO-SCRN",category:"iPhone Parts",price:149.99,cost:85,stock:18,low_stock_threshold:5,description:"Premium quality iPhone 14 Pro screen assembly with True Tone support",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:e.length+2,name:"iPhone 13 Battery Original",sku:"4S-IP13-BAT-OEM",category:"iPhone Parts",price:39.99,cost:22.5,stock:24,low_stock_threshold:8,description:"Original capacity iPhone 13 battery replacement",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]:"ebay"===s?n=[{id:e.length+1,name:"Samsung Galaxy S23 OLED Screen",sku:"EB-S23-OLED",category:"Samsung Parts",price:129.99,cost:72.5,stock:12,low_stock_threshold:4,description:"Original quality Samsung Galaxy S23 OLED screen replacement",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:e.length+2,name:"Samsung Galaxy Z Flip 4 Hinge Assembly",sku:"EB-ZFLIP4-HINGE",category:"Samsung Parts",price:89.99,cost:45,stock:6,low_stock_threshold:2,description:"Replacement hinge assembly for Samsung Galaxy Z Flip 4",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]:"amazon"===s?n=[{id:e.length+1,name:'iPad Pro 11" (2022) LCD Assembly',sku:"AMZ-IPADPRO11-22",category:"iPad Parts",price:189.99,cost:105,stock:8,low_stock_threshold:3,description:'Complete LCD assembly for iPad Pro 11" (2022 model)',created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:e.length+2,name:"MacBook Air M2 Keyboard Replacement",sku:"AMZ-MBA-M2-KB",category:"MacBook Parts",price:149.99,cost:85,stock:5,low_stock_threshold:2,description:"Replacement keyboard for MacBook Air M2 (2022)",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]:"tiktok"===s&&(n=[{id:e.length+1,name:"Professional Phone Repair Tool Kit (80pcs)",sku:"TT-TOOL-KIT-80",category:"Repair Tools",price:79.99,cost:42,stock:15,low_stock_threshold:5,description:"80-piece professional phone repair tool kit with magnetic screwdrivers",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:e.length+2,name:"Phone Screen Separator Machine",sku:"TT-SCREEN-SEP",category:"Repair Tools",price:199.99,cost:120,stock:4,low_stock_threshold:2,description:"Professional LCD screen separator machine for phone repair",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]),t([...e,...n]),alert("Successfully synced ".concat(n.length," products from ").concat(s))}catch(e){alert("Failed to sync with ".concat(s,". Error: ").concat(e.message))}finally{i(!1)}};return s?(0,n.jsxs)("div",{className:P().loadingContainer,children:[(0,n.jsx)("div",{className:P().spinner}),(0,n.jsx)("p",{children:"Loading inventory data..."})]}):r?(0,n.jsxs)("div",{className:P().errorContainer,children:[(0,n.jsx)("p",{children:r}),(0,n.jsx)("button",{onClick:()=>window.location.reload(),children:"Try Again"})]}):(0,n.jsxs)("div",{className:P().inventoryManagement,children:[(0,n.jsxs)("div",{className:P().sectionHeader,children:[(0,n.jsx)("h2",{children:"Inventory Management"}),(0,n.jsx)("p",{className:P().sectionDescription,children:"Manage your product inventory, sync with external systems, and track stock levels."})]}),(0,n.jsxs)("div",{className:P().inventoryActions,children:[(0,n.jsxs)("div",{className:P().inventoryFilters,children:[(0,n.jsx)("div",{className:P().searchBox,children:(0,n.jsx)("input",{type:"text",placeholder:"Search products...",value:c,onChange:e=>l(e.target.value)})}),(0,n.jsx)("div",{className:P().filterBox,children:(0,n.jsxs)("select",{value:d,onChange:e=>h(e.target.value),children:[(0,n.jsx)("option",{value:"all",children:"All Categories"}),N.map(e=>(0,n.jsx)("option",{value:e,children:e},e))]})}),(0,n.jsxs)("div",{className:P().sortBox,children:[(0,n.jsxs)("select",{value:u,onChange:e=>m(e.target.value),children:[(0,n.jsx)("option",{value:"name",children:"Sort by Name"}),(0,n.jsx)("option",{value:"sku",children:"Sort by SKU"}),(0,n.jsx)("option",{value:"price",children:"Sort by Price"}),(0,n.jsx)("option",{value:"stock",children:"Sort by Stock"})]}),(0,n.jsx)("button",{className:P().sortOrderButton,onClick:()=>_("asc"===p?"desc":"asc"),children:"asc"===p?"↑":"↓"})]})]}),(0,n.jsxs)("div",{className:P().inventoryButtons,children:[(0,n.jsx)("button",{className:P().addButton,onClick:()=>w(!0),children:"Add Product"}),(0,n.jsx)("button",{className:P().importButton,onClick:()=>{A.current.click()},children:"Import"}),(0,n.jsx)("button",{className:P().exportButton,onClick:()=>{let t=new Blob([["ID,Name,SKU,Category,Price,Cost,Stock,Description",...e.map(e=>[e.id,'"'.concat(e.name,'"'),e.sku,'"'.concat(e.category,'"'),e.price,e.cost,e.stock,'"'.concat(e.description,'"')].join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),s=URL.createObjectURL(t),n=document.createElement("a");n.setAttribute("href",s),n.setAttribute("download","inventory-export-".concat(new Date().toISOString().split("T")[0],".csv")),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)},children:"Export"})]})]}),(0,n.jsx)("input",{type:"file",ref:A,style:{display:"none"},accept:".csv,.xlsx,.xls",onChange:I}),(0,n.jsxs)("div",{className:P().syncPlatformsContainer,children:[(0,n.jsx)("h3",{className:P().syncPlatformsTitle,children:"Sync with External Platforms"}),(0,n.jsxs)("div",{className:P().syncPlatformsButtons,children:[(0,n.jsxs)("button",{className:P().platformButton,onClick:()=>D("4seller"),children:[(0,n.jsx)("img",{src:"/images/integrations/4seller-logo.png",alt:"4seller",width:"20",height:"20",onError:e=>e.target.src="/images/placeholder.svg"}),"4seller.com"]}),(0,n.jsxs)("button",{className:P().platformButton,onClick:()=>D("ebay"),children:[(0,n.jsx)("img",{src:"/images/integrations/ebay-logo.png",alt:"eBay",width:"20",height:"20",onError:e=>e.target.src="/images/placeholder.svg"}),"eBay"]}),(0,n.jsxs)("button",{className:P().platformButton,onClick:()=>D("amazon"),children:[(0,n.jsx)("img",{src:"/images/integrations/amazon-logo.png",alt:"Amazon",width:"20",height:"20",onError:e=>e.target.src="/images/placeholder.svg"}),"Amazon"]}),(0,n.jsxs)("button",{className:P().platformButton,onClick:()=>D("tiktok"),children:[(0,n.jsx)("img",{src:"/images/integrations/tiktok-logo.png",alt:"TikTok Shop",width:"20",height:"20",onError:e=>e.target.src="/images/placeholder.svg"}),"TikTok Shop"]})]})]}),(0,n.jsxs)("table",{className:P().inventoryTable,children:[(0,n.jsx)("thead",{children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("th",{children:(0,n.jsx)("input",{type:"checkbox",checked:x.length===b.length&&b.length>0,onChange:e=>{e.target.checked?v(b.map(e=>e.id)):v([])}})}),(0,n.jsx)("th",{children:"Product"}),(0,n.jsx)("th",{children:"SKU"}),(0,n.jsx)("th",{children:"Category"}),(0,n.jsx)("th",{children:"Price"}),(0,n.jsx)("th",{children:"Stock"}),(0,n.jsx)("th",{children:"Status"}),(0,n.jsx)("th",{children:"Actions"})]})}),(0,n.jsx)("tbody",{children:0===b.length?(0,n.jsx)("tr",{children:(0,n.jsx)("td",{colSpan:"8",className:P().noResults,children:"No products found. Try adjusting your filters."})}):b.map(e=>(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{children:(0,n.jsx)("input",{type:"checkbox",checked:x.includes(e.id),onChange:t=>S(t,e.id)})}),(0,n.jsx)("td",{children:(0,n.jsxs)("div",{className:P().productCell,children:[(0,n.jsx)("div",{className:P().productImage,children:(0,n.jsx)("img",{src:"/images/products/".concat(e.sku.toLowerCase(),".jpg"),alt:e.name,onError:e=>e.target.src="/images/placeholder.svg"})}),(0,n.jsx)("div",{className:P().productName,children:e.name})]})}),(0,n.jsx)("td",{children:e.sku}),(0,n.jsx)("td",{children:e.category}),(0,n.jsxs)("td",{children:["$",e.price.toFixed(2)]}),(0,n.jsx)("td",{className:e.stock<=e.low_stock_threshold?P().lowStock:"",children:e.stock}),(0,n.jsx)("td",{children:(0,n.jsx)("span",{className:"".concat(P().status," ").concat(e.stock>0?P().inStock:P().outOfStock),children:e.stock>0?"In Stock":"Out of Stock"})}),(0,n.jsx)("td",{children:(0,n.jsxs)("div",{className:P().actionButtons,children:[(0,n.jsx)("button",{className:P().editButton,children:"Edit"}),(0,n.jsx)("button",{className:P().deleteButton,children:"Delete"})]})})]},e.id))})]}),x.length>0&&(0,n.jsxs)("div",{className:P().bulkActionBar,children:[(0,n.jsxs)("span",{children:[x.length," items selected"]}),(0,n.jsxs)("div",{className:P().bulkActionControls,children:[(0,n.jsxs)("select",{value:g,onChange:e=>j(e.target.value),className:P().bulkActionSelect,children:[(0,n.jsx)("option",{value:"",children:"Bulk Actions"}),(0,n.jsx)("option",{value:"delete",children:"Delete"}),(0,n.jsx)("option",{value:"update-stock",children:"Update Stock"}),(0,n.jsx)("option",{value:"update-price",children:"Update Price"}),(0,n.jsx)("option",{value:"export-selected",children:"Export Selected"})]}),(0,n.jsx)("button",{onClick:()=>{g&&0!==x.length&&(alert("Performing ".concat(g," on ").concat(x.length," products")),v([]),j(""))},disabled:!g,className:P().bulkActionButton,children:"Apply"})]})]}),y&&(0,n.jsx)("div",{className:P().modalOverlay,children:(0,n.jsxs)("div",{className:P().modal,children:[(0,n.jsxs)("div",{className:P().modalHeader,children:[(0,n.jsx)("h3",{className:P().modalTitle,children:"Add New Product"}),(0,n.jsx)("button",{className:P().closeButton,onClick:()=>w(!1),children:"\xd7"})]}),(0,n.jsx)("div",{className:P().modalBody,children:(0,n.jsxs)("form",{onSubmit:s=>{s.preventDefault();let n={...k,id:e.length+1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),low_stock_threshold:5};t([...e,n]),w(!1),f({name:"",sku:"",category:"",price:"",cost:"",stock:"",description:""})},children:[(0,n.jsxs)("div",{className:P().formGroup,children:[(0,n.jsx)("label",{className:P().formLabel,htmlFor:"name",children:"Product Name"}),(0,n.jsx)("input",{className:P().formInput,type:"text",id:"name",name:"name",value:k.name,onChange:C,required:!0})]}),(0,n.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,n.jsxs)("div",{className:P().formGroup,style:{flex:1},children:[(0,n.jsx)("label",{className:P().formLabel,htmlFor:"sku",children:"SKU"}),(0,n.jsx)("input",{className:P().formInput,type:"text",id:"sku",name:"sku",value:k.sku,onChange:C,required:!0})]}),(0,n.jsxs)("div",{className:P().formGroup,style:{flex:1},children:[(0,n.jsx)("label",{className:P().formLabel,htmlFor:"category",children:"Category"}),(0,n.jsxs)("select",{className:P().formSelect,id:"category",name:"category",value:k.category,onChange:C,required:!0,children:[(0,n.jsx)("option",{value:"",children:"Select Category"}),N.map(e=>(0,n.jsx)("option",{value:e,children:e},e))]})]})]}),(0,n.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,n.jsxs)("div",{className:P().formGroup,style:{flex:1},children:[(0,n.jsx)("label",{className:P().formLabel,htmlFor:"price",children:"Price ($)"}),(0,n.jsx)("input",{className:P().formInput,type:"number",id:"price",name:"price",min:"0",step:"0.01",value:k.price,onChange:C,required:!0})]}),(0,n.jsxs)("div",{className:P().formGroup,style:{flex:1},children:[(0,n.jsx)("label",{className:P().formLabel,htmlFor:"cost",children:"Cost ($)"}),(0,n.jsx)("input",{className:P().formInput,type:"number",id:"cost",name:"cost",min:"0",step:"0.01",value:k.cost,onChange:C,required:!0})]}),(0,n.jsxs)("div",{className:P().formGroup,style:{flex:1},children:[(0,n.jsx)("label",{className:P().formLabel,htmlFor:"stock",children:"Stock"}),(0,n.jsx)("input",{className:P().formInput,type:"number",id:"stock",name:"stock",min:"0",value:k.stock,onChange:C,required:!0})]})]}),(0,n.jsxs)("div",{className:P().formGroup,children:[(0,n.jsx)("label",{className:P().formLabel,htmlFor:"description",children:"Description"}),(0,n.jsx)("textarea",{className:P().formTextarea,id:"description",name:"description",rows:"3",value:k.description,onChange:C})]}),(0,n.jsxs)("div",{className:P().modalFooter,children:[(0,n.jsx)("button",{type:"button",className:P().cancelButton,onClick:()=>w(!1),children:"Cancel"}),(0,n.jsx)("button",{type:"submit",className:P().submitButton,children:"Add Product"})]})]})})]})})]})}var T=!0;function L(){let{data:e,status:t}=(0,i.useSession)(),s="loading"===t,o=(0,r.useRouter)(),[l,d]=(0,a.useState)("overview");return((0,a.useEffect)(()=>{s||e||o.push("/auth/signin?callbackUrl=/account"),o.query.tab&&d(o.query.tab)},[e,s,o]),s||!e)?(0,n.jsx)("div",{className:"container",children:(0,n.jsxs)("div",{className:"loading-container",children:[(0,n.jsx)("div",{className:"loading-spinner"}),(0,n.jsx)("p",{children:"Loading account information..."})]})}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(c(),{children:[(0,n.jsx)("title",{children:"My Account | MDTS - Midas Technical Solutions"}),(0,n.jsx)("meta",{name:"description",content:"Manage your account, orders, addresses, and preferences at MDTS - Midas Technical Solutions"})]}),(0,n.jsxs)("div",{className:"container",children:[(0,n.jsxs)("div",{className:u().accountHeader,children:[(0,n.jsx)("h1",{children:"My Account"}),(0,n.jsxs)("p",{children:["Welcome back, ",e.user.name||e.user.email,"!"]})]}),(0,n.jsxs)("div",{className:u().accountDashboard,children:[(0,n.jsx)(_,{activeTab:l,onTabChange:e=>{d(e),o.push("/account?tab=".concat(e),void 0,{shallow:!0})}}),(0,n.jsxs)("div",{className:u().accountContent,children:["overview"===l&&(0,n.jsx)(x,{session:e}),"orders"===l&&(0,n.jsx)(v,{}),"addresses"===l&&(0,n.jsx)(j,{}),"preferences"===l&&(0,n.jsx)(f,{}),"wishlist"===l&&(0,n.jsx)(b,{}),"integrations"===l&&(0,n.jsx)(A,{}),"inventory"===l&&(0,n.jsx)(D,{})]})]})]})]})}},70297:function(e){e.exports={socialShare:"SocialShare_socialShare__DoxjP",shareButtons:"SocialShare_shareButtons__EWJE5",shareButton:"SocialShare_shareButton__6NDI1",buttonText:"SocialShare_buttonText__sE4rw",facebook:"SocialShare_facebook__ucJ1U",twitter:"SocialShare_twitter___Djgs",pinterest:"SocialShare_pinterest__3oB3p",linkedin:"SocialShare_linkedin__Fex9y",email:"SocialShare_email__X_JE0",copy:"SocialShare_copy__X3EY0",tooltip:"SocialShare_tooltip__JhGxQ",show:"SocialShare_show__Vv5Mh"}},44729:function(e){e.exports={accountHeader:"Account_accountHeader__NuuDm",accountDashboard:"Account_accountDashboard__O_Iab",accountContent:"Account_accountContent__ugdrs",sidebar:"Account_sidebar__z9Wed",sidebarNav:"Account_sidebarNav__LZGFw",navItem:"Account_navItem__Y_GC3",active:"Account_active__6gv7A",sidebarDivider:"Account_sidebarDivider__bdDUL",logoutButton:"Account_logoutButton__6_glc",overviewGrid:"Account_overviewGrid__a_2_V",overviewCard:"Account_overviewCard__Ss_hg",value:"Account_value__hr8KC",recentActivity:"Account_recentActivity___JhVa",activityItem:"Account_activityItem__rCtfg",activityIcon:"Account_activityIcon__XG8Er",activityContent:"Account_activityContent__za4p_",activityDate:"Account_activityDate__o9ykx",ordersList:"Account_ordersList__gue0U",orderCard:"Account_orderCard__Dup2T",orderHeader:"Account_orderHeader__88mMm",orderNumber:"Account_orderNumber__ZY4Lb",orderDate:"Account_orderDate__xKMii",orderStatus:"Account_orderStatus__qVX_F",statusPending:"Account_statusPending__YE4Ne",statusProcessing:"Account_statusProcessing__cG57c",statusShipped:"Account_statusShipped__zLoNv",statusDelivered:"Account_statusDelivered__BTnXW",statusCancelled:"Account_statusCancelled__zz80r",orderItems:"Account_orderItems__sQgRk",orderItem:"Account_orderItem__qGrjN",orderItemImage:"Account_orderItemImage__STAi2",orderItemDetails:"Account_orderItemDetails__Kk3Nj",orderItemName:"Account_orderItemName__F8_dz",orderItemPrice:"Account_orderItemPrice__AfPG_",orderItemQuantity:"Account_orderItemQuantity__BIsi0",orderFooter:"Account_orderFooter__NyG_L",orderTotal:"Account_orderTotal__MA7Qv",orderActions:"Account_orderActions__zwwS1",orderButton:"Account_orderButton__iaCxY",viewOrderButton:"Account_viewOrderButton__yYJO5",trackOrderButton:"Account_trackOrderButton__y3E_1",addressGrid:"Account_addressGrid__9CTDi",addressCard:"Account_addressCard__a6_6y",default:"Account_default__abCxe",defaultBadge:"Account_defaultBadge__6AB89",addressType:"Account_addressType__TPTsH",addressDetails:"Account_addressDetails__Diq6n",addressActions:"Account_addressActions__5eKfP",deleteAddressButton:"Account_deleteAddressButton__x6mPx",editAddressButton:"Account_editAddressButton__0ZiXd",addAddressButton:"Account_addAddressButton__zaeF6",preferencesForm:"Account_preferencesForm__hUil_",formGroup:"Account_formGroup__Qs7NE",helperText:"Account_helperText__gtfxc",divider:"Account_divider__l1qrA",currencyConverter:"Account_currencyConverter__Dlcgp",converterForm:"Account_converterForm__e7jxf",converterInputs:"Account_converterInputs__8dGGP",switchCurrencies:"Account_switchCurrencies__N_VEe",convertButton:"Account_convertButton__o3OMI",conversionResult:"Account_conversionResult__ZcaTR",resultAmount:"Account_resultAmount__4_C92",convertedValue:"Account_convertedValue__mUJK4",exchangeRate:"Account_exchangeRate__WZbYu",disclaimer:"Account_disclaimer__h3z2u",lastUpdated:"Account_lastUpdated__Lukcz",adminSection:"Account_adminSection__m_FHP",sectionDescription:"Account_sectionDescription__dX_ey",comingSoon:"Account_comingSoon__eL7TW",checkboxGroup:"Account_checkboxGroup__nNCq5",saveButton:"Account_saveButton___uXE_",wishlistHeader:"Account_wishlistHeader__UoJ5Y",shareWishlist:"Account_shareWishlist__O50O4",wishlistGrid:"Account_wishlistGrid__8C9uR",wishlistCard:"Account_wishlistCard__c6KB1",wishlistImageContainer:"Account_wishlistImageContainer__e6grz",wishlistImage:"Account_wishlistImage__Ns9YB",removeWishlistItem:"Account_removeWishlistItem__yHhqp",wishlistContent:"Account_wishlistContent__ppwih",wishlistCategory:"Account_wishlistCategory__PM_OO",wishlistName:"Account_wishlistName__kslEA",wishlistPrice:"Account_wishlistPrice__lM_W3",wishlistOriginalPrice:"Account_wishlistOriginalPrice__hcGW0",wishlistCurrentPrice:"Account_wishlistCurrentPrice__ztx5_",wishlistActions:"Account_wishlistActions__yzZse",addToCartFromWishlist:"Account_addToCartFromWishlist__vrfK5",viewProductFromWishlist:"Account_viewProductFromWishlist__nWAbt",emptyWishlist:"Account_emptyWishlist__XESeS",browseProductsButton:"Account_browseProductsButton__OdPB1"}},15748:function(e){e.exports={adminContainer:"Admin_adminContainer__fnIiy",adminSidebar:"Admin_adminSidebar__ECUVT",adminLogo:"Admin_adminLogo__KRj8V",adminNav:"Admin_adminNav__6VV7e",adminNavItem:"Admin_adminNavItem__vGP0q",active:"Admin_active__BhXLC",adminContent:"Admin_adminContent__la1bh",adminHeader:"Admin_adminHeader__D8Awi",adminUser:"Admin_adminUser__9UhpZ",adminTabs:"Admin_adminTabs__cqvDR",adminTab:"Admin_adminTab__W_1Cu",activeTab:"Admin_activeTab__fvtdm",adminTabContent:"Admin_adminTabContent__2Pyru",loadingContainer:"Admin_loadingContainer__co6Jm",spinner:"Admin_spinner__UFXHG",spin:"Admin_spin__Ah3vg",statsGrid:"Admin_statsGrid__TB_yj",statCard:"Admin_statCard__TRCV8",statValue:"Admin_statValue__7vDB_",overviewCharts:"Admin_overviewCharts__CbCXN",chartCard:"Admin_chartCard__nhG5d",barChart:"Admin_barChart__P7yDX",barChartItem:"Admin_barChartItem__upy5l",barLabel:"Admin_barLabel__zg5pf",barContainer:"Admin_barContainer__rV5lJ",bar:"Admin_bar__KT2HJ",redBar:"Admin_redBar__Ux63t",barValue:"Admin_barValue__by0tg",tableContainer:"Admin_tableContainer__jS7Fw",dataTable:"Admin_dataTable__HJk6Z",actionButton:"Admin_actionButton__UMz0P",zeroResultsActions:"Admin_zeroResultsActions__a8LFP",recommendationsList:"Admin_recommendationsList__kt0vF",marketplacesTab:"Admin_marketplacesTab__F1B_G",tabDescription:"Admin_tabDescription__4232R",customersTab:"Admin_customersTab__K7_If",ordersTab:"Admin_ordersTab__nYVxi",productsTab:"Admin_productsTab__c7E0b",settingsTab:"Admin_settingsTab__uKxYF",adminSection:"Admin_adminSection__vTklz",sectionDescription:"Admin_sectionDescription__fkI2f",errorMessage:"Admin_errorMessage__X7vgq",successMessage:"Admin_successMessage__TfbaZ",integrationCards:"Admin_integrationCards__ahfcD",integrationCard:"Admin_integrationCard__UDVVJ",integrationHeader:"Admin_integrationHeader__ZpOSz",integrationLogo:"Admin_integrationLogo__L6Jp1",integrationInfo:"Admin_integrationInfo__xh2eJ",integrationStatus:"Admin_integrationStatus___Iy_i",statusIndicator:"Admin_statusIndicator__71aId",connected:"Admin_connected__7QFvd",disconnected:"Admin_disconnected__Awmdk",integrationBody:"Admin_integrationBody__DHwlP",integrationField:"Admin_integrationField__vCIAg",apiKeyField:"Admin_apiKeyField__Flra0",copyButton:"Admin_copyButton__jtW5y",databasesList:"Admin_databasesList__ANXzk",webhooksList:"Admin_webhooksList__4hKEU",databaseItem:"Admin_databaseItem__By6lR",webhookItem:"Admin_webhookItem__TtL6j",databaseInfo:"Admin_databaseInfo__5cusK",webhookInfo:"Admin_webhookInfo__rvvlE",databaseName:"Admin_databaseName__mbr33",webhookName:"Admin_webhookName__fRaXr",databaseId:"Admin_databaseId__QqduL",webhookUrl:"Admin_webhookUrl__6HqB3",switch:"Admin_switch__9nB1M",slider:"Admin_slider__GAnsH",connectContainer:"Admin_connectContainer__U5lly",connectButton:"Admin_connectButton__x3D79",disconnectButton:"Admin_disconnectButton__NA_DW",integrationHelp:"Admin_integrationHelp__oqKc1"}},99186:function(e){e.exports={inventoryManagement:"InventoryManagement_inventoryManagement__BEzaQ",sectionHeader:"InventoryManagement_sectionHeader__5XB_R",sectionDescription:"InventoryManagement_sectionDescription__j4BJ_",inventoryActions:"InventoryManagement_inventoryActions__9_0uE",inventoryFilters:"InventoryManagement_inventoryFilters__h_o9R",searchBox:"InventoryManagement_searchBox__gQzcB",filterBox:"InventoryManagement_filterBox__S9AQU",sortBox:"InventoryManagement_sortBox__PlBmL",sortOrderButton:"InventoryManagement_sortOrderButton___VdGE",inventoryButtons:"InventoryManagement_inventoryButtons__1rsN7",addButton:"InventoryManagement_addButton__jPCeN",exportButton:"InventoryManagement_exportButton__nRKfr",importButton:"InventoryManagement_importButton__gCZSl",syncButton:"InventoryManagement_syncButton__k_UuJ",inventoryTable:"InventoryManagement_inventoryTable__MBrNo",productCell:"InventoryManagement_productCell__vokTG",productImage:"InventoryManagement_productImage__RK2T0",productName:"InventoryManagement_productName__TZSBG",status:"InventoryManagement_status__80YEx",inStock:"InventoryManagement_inStock__M7wU8",outOfStock:"InventoryManagement_outOfStock__rqxFx",lowStock:"InventoryManagement_lowStock__fWUE0",actionButtons:"InventoryManagement_actionButtons__oOcQO",deleteButton:"InventoryManagement_deleteButton__fUezu",editButton:"InventoryManagement_editButton__VSpKZ",syncPlatformsContainer:"InventoryManagement_syncPlatformsContainer__71l3f",syncPlatformsTitle:"InventoryManagement_syncPlatformsTitle__FsAXV",syncPlatformsButtons:"InventoryManagement_syncPlatformsButtons__tjoxj",platformButton:"InventoryManagement_platformButton__OIdNU",noResults:"InventoryManagement_noResults___oAzQ",errorContainer:"InventoryManagement_errorContainer__6NLpA",loadingContainer:"InventoryManagement_loadingContainer__EQXsu",spinner:"InventoryManagement_spinner__JbZua",spin:"InventoryManagement_spin__yLIum",modalOverlay:"InventoryManagement_modalOverlay__8jb8b",modal:"InventoryManagement_modal__BT_Px",modalHeader:"InventoryManagement_modalHeader__Y3Jzk",modalTitle:"InventoryManagement_modalTitle__09NDI",closeButton:"InventoryManagement_closeButton___zFx8",modalBody:"InventoryManagement_modalBody__cRCcf",formGroup:"InventoryManagement_formGroup__B7lGP",formLabel:"InventoryManagement_formLabel__ZxyxA",formInput:"InventoryManagement_formInput__2wJzI",formSelect:"InventoryManagement_formSelect__ARkiY",formTextarea:"InventoryManagement_formTextarea__CnfQ1",modalFooter:"InventoryManagement_modalFooter__2p6aQ",cancelButton:"InventoryManagement_cancelButton__6zRF0",submitButton:"InventoryManagement_submitButton__qQlQn"}},44339:function(e){e.exports={accountSidebar:"ZIndexFix_accountSidebar__DemMy",accountSidebarNav:"ZIndexFix_accountSidebarNav__FRFWE",chatbotContainer:"ZIndexFix_chatbotContainer__XyEXc",whatsappContainer:"ZIndexFix_whatsappContainer__H3oi2",accessibilityButton:"ZIndexFix_accessibilityButton__XHcl9",notificationsContainer:"ZIndexFix_notificationsContainer__c_ImF",modalOverlay:"ZIndexFix_modalOverlay__xWF1c",modalContent:"ZIndexFix_modalContent__8DeUW",chatbotMessage:"ZIndexFix_chatbotMessage__uUrvD",chatbotUserMessage:"ZIndexFix_chatbotUserMessage__ZLtd6",chatbotBotMessage:"ZIndexFix_chatbotBotMessage__Syo1y",uiSpacing:"ZIndexFix_uiSpacing__fm_DG",fixHiddenElement:"ZIndexFix_fixHiddenElement__pjppm",notificationFix:"ZIndexFix_notificationFix__0LEMB"}},11163:function(e,t,s){e.exports=s(43079)}},function(e){e.O(0,[1664,5675,2888,9774,179],function(){return e(e.s=84865)}),_N_E=e.O()}]);