(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[273],{85901:function(e,i,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/orders/track/[id]",function(){return r(56770)}])},56770:function(e,i,r){"use strict";r.r(i),r.d(i,{__N_SSP:function(){return g},default:function(){return u}});var a=r(85893),n=r(67294),t=r(11163),s=r(33299),c=r(41664),l=r.n(c),d=r(9008),o=r.n(d),m=r(13014),_=r.n(m),g=!0;function u(){let e=(0,t.useRouter)(),{id:i}=e.query,{data:r,status:c}=(0,s.useSession)(),[d,m]=(0,n.useState)(null),[g,u]=(0,n.useState)(!0),[k,h]=(0,n.useState)(null);(0,n.useEffect)(()=>{i&&(async()=>{try{u(!0),await new Promise(e=>setTimeout(e,500)),m({order_id:i,tracking_number:"TRK123456789",carrier:"USPS",estimated_delivery:"2023-06-19T15:45:00Z",status:"delivered",origin:{city:"Vienna",state:"VA",country:"US"},destination:{city:"New York",state:"NY",country:"US"},events:[{status:"Delivered",location:"New York, NY",timestamp:"2023-06-19T15:45:00Z",description:"Package delivered to recipient"},{status:"Out for Delivery",location:"New York, NY",timestamp:"2023-06-19T08:30:00Z",description:"Package is out for delivery"},{status:"Arrived at Local Facility",location:"New York, NY",timestamp:"2023-06-18T22:15:00Z",description:"Package arrived at local facility"},{status:"In Transit",location:"Philadelphia, PA",timestamp:"2023-06-18T14:20:00Z",description:"Package in transit to destination"},{status:"Departed Shipping Facility",location:"Vienna, VA",timestamp:"2023-06-17T16:45:00Z",description:"Package has left the shipping facility"},{status:"Shipping Label Created",location:"Vienna, VA",timestamp:"2023-06-17T11:20:00Z",description:"Shipping label created, package ready for UPS"}]})}catch(e){console.error("Error fetching tracking:",e),h("Failed to load tracking information. Please try again later.")}finally{u(!1)}})()},[i]);let v=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),p=e=>{switch(e.toLowerCase()){case"delivered":return _().statusDelivered;case"out for delivery":return _().statusOutForDelivery;case"in transit":return _().statusInTransit;case"shipping label created":return _().statusCreated;default:return _().statusProcessing}};return g?(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"loading-container",children:[(0,a.jsx)("div",{className:"loading-spinner"}),(0,a.jsx)("p",{children:"Loading tracking information..."})]})}):k?(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"error-message",children:[(0,a.jsx)("p",{children:k}),(0,a.jsx)("button",{onClick:()=>e.reload(),className:"btn btn-primary",children:"Try Again"})]})}):d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(o(),{children:[(0,a.jsxs)("title",{children:["Track Order ",d.order_id," | MDTS - Midas Technical Solutions"]}),(0,a.jsx)("meta",{name:"description",content:"Track your order ".concat(d.order_id," at MDTS - Midas Technical Solutions")})]}),(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)("div",{className:_().trackingHeader,children:[(0,a.jsx)(l(),{href:"/orders/".concat(d.order_id),className:_().backLink,children:"← Back to Order Details"}),(0,a.jsx)("h1",{children:"Track Your Order"}),(0,a.jsxs)("div",{className:_().orderNumber,children:["Order #",d.order_id]})]}),(0,a.jsxs)("div",{className:_().trackingContent,children:[(0,a.jsxs)("div",{className:_().trackingInfo,children:[(0,a.jsxs)("div",{className:_().trackingInfoItem,children:[(0,a.jsx)("div",{className:_().trackingInfoLabel,children:"Tracking Number:"}),(0,a.jsx)("div",{className:_().trackingInfoValue,children:d.tracking_number})]}),(0,a.jsxs)("div",{className:_().trackingInfoItem,children:[(0,a.jsx)("div",{className:_().trackingInfoLabel,children:"Carrier:"}),(0,a.jsx)("div",{className:_().trackingInfoValue,children:d.carrier})]}),(0,a.jsxs)("div",{className:_().trackingInfoItem,children:[(0,a.jsx)("div",{className:_().trackingInfoLabel,children:"Status:"}),(0,a.jsx)("div",{className:"".concat(_().trackingInfoValue," ").concat(p(d.status)),children:d.status.charAt(0).toUpperCase()+d.status.slice(1)})]}),(0,a.jsxs)("div",{className:_().trackingInfoItem,children:[(0,a.jsx)("div",{className:_().trackingInfoLabel,children:"Estimated Delivery:"}),(0,a.jsx)("div",{className:_().trackingInfoValue,children:v(d.estimated_delivery)})]})]}),(0,a.jsxs)("div",{className:_().trackingMap,children:[(0,a.jsxs)("div",{className:_().mapPlaceholder,children:[(0,a.jsxs)("div",{className:_().mapOrigin,children:[(0,a.jsxs)("div",{className:_().mapLocation,children:[d.origin.city,", ",d.origin.state]}),(0,a.jsx)("div",{className:_().mapLabel,children:"Origin"})]}),(0,a.jsx)("div",{className:_().mapProgress,children:(0,a.jsx)("div",{className:_().mapProgressBar,children:(0,a.jsx)("div",{className:_().mapProgressFill,style:{width:"delivered"===d.status?"100%":"out for delivery"===d.status?"90%":"in transit"===d.status?"50%":"10%"}})})}),(0,a.jsxs)("div",{className:_().mapDestination,children:[(0,a.jsxs)("div",{className:_().mapLocation,children:[d.destination.city,", ",d.destination.state]}),(0,a.jsx)("div",{className:_().mapLabel,children:"Destination"})]})]}),(0,a.jsxs)("div",{className:_().mapNote,children:[(0,a.jsxs)("p",{children:["For detailed tracking information, visit the ",d.carrier," website using your tracking number."]}),(0,a.jsxs)("a",{href:"https://www.".concat(d.carrier.toLowerCase(),".com/tracking/details/").concat(d.tracking_number),target:"_blank",rel:"noopener noreferrer",className:_().carrierLink,children:["Track on ",d.carrier," Website"]})]})]}),(0,a.jsxs)("div",{className:_().trackingTimeline,children:[(0,a.jsx)("h2",{children:"Tracking History"}),(0,a.jsx)("div",{className:_().timeline,children:d.events.map((e,i)=>(0,a.jsxs)("div",{className:_().timelineEvent,children:[(0,a.jsx)("div",{className:_().timelineEventIcon,children:(0,a.jsx)("div",{className:"".concat(_().timelineEventIconInner," ").concat(p(e.status))})}),(0,a.jsxs)("div",{className:_().timelineEventContent,children:[(0,a.jsxs)("div",{className:_().timelineEventHeader,children:[(0,a.jsx)("div",{className:_().timelineEventStatus,children:e.status}),(0,a.jsx)("div",{className:_().timelineEventDate,children:v(e.timestamp)})]}),(0,a.jsx)("div",{className:_().timelineEventLocation,children:e.location}),(0,a.jsx)("div",{className:_().timelineEventDescription,children:e.description})]})]},i))})]}),(0,a.jsxs)("div",{className:_().trackingHelp,children:[(0,a.jsx)("h2",{children:"Need Help?"}),(0,a.jsx)("p",{children:"If you have any questions about your shipment, please contact our customer support team."}),(0,a.jsx)(l(),{href:"/contact",className:_().contactButton,children:"Contact Support"})]})]})]})]}):(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"error-message",children:[(0,a.jsx)("p",{children:"Tracking information not found."}),(0,a.jsx)(l(),{href:"/orders/".concat(i),className:"btn btn-primary",children:"Back to Order Details"})]})})}},13014:function(e){e.exports={trackingHeader:"OrderTracking_trackingHeader__JPHI1",backLink:"OrderTracking_backLink__yTq6r",orderNumber:"OrderTracking_orderNumber__jin06",trackingContent:"OrderTracking_trackingContent__G7zHe",trackingInfo:"OrderTracking_trackingInfo__tMmJm",trackingInfoItem:"OrderTracking_trackingInfoItem__vUv_V",trackingInfoLabel:"OrderTracking_trackingInfoLabel__h5_Gi",trackingInfoValue:"OrderTracking_trackingInfoValue__2_wqq",statusDelivered:"OrderTracking_statusDelivered__LXahO",statusOutForDelivery:"OrderTracking_statusOutForDelivery__Uh7K8",statusInTransit:"OrderTracking_statusInTransit__oQYZX",statusProcessing:"OrderTracking_statusProcessing__Ru7Hd",statusCreated:"OrderTracking_statusCreated___Qhe0",trackingMap:"OrderTracking_trackingMap__ZXMuu",mapPlaceholder:"OrderTracking_mapPlaceholder__wdJAs",mapDestination:"OrderTracking_mapDestination__FakVG",mapOrigin:"OrderTracking_mapOrigin___jdqT",mapLocation:"OrderTracking_mapLocation__MjVHN",mapLabel:"OrderTracking_mapLabel__pewOu",mapProgress:"OrderTracking_mapProgress__8y_bg",mapProgressBar:"OrderTracking_mapProgressBar__VQ_Yc",mapProgressFill:"OrderTracking_mapProgressFill__gUCmP",mapNote:"OrderTracking_mapNote__ANHdv",carrierLink:"OrderTracking_carrierLink__QVZBA",trackingTimeline:"OrderTracking_trackingTimeline__FjNKE",timeline:"OrderTracking_timeline__eEtMW",timelineEvent:"OrderTracking_timelineEvent__y1O8g",timelineEventIcon:"OrderTracking_timelineEventIcon__A28io",timelineEventIconInner:"OrderTracking_timelineEventIconInner__7V4O0",timelineEventContent:"OrderTracking_timelineEventContent__V2KwB",timelineEventHeader:"OrderTracking_timelineEventHeader__L1lRJ",timelineEventStatus:"OrderTracking_timelineEventStatus__l_dAO",timelineEventDate:"OrderTracking_timelineEventDate__aPzr9",timelineEventLocation:"OrderTracking_timelineEventLocation__CLa0_",timelineEventDescription:"OrderTracking_timelineEventDescription__oegc2",trackingHelp:"OrderTracking_trackingHelp__dGn1h",contactButton:"OrderTracking_contactButton__DONkm"}},11163:function(e,i,r){e.exports=r(43079)}},function(e){e.O(0,[1664,2888,9774,179],function(){return e(e.s=85901)}),_N_E=e.O()}]);