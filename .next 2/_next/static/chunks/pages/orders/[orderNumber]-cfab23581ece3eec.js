(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5258],{5160:function(s,e,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/orders/[orderNumber]",function(){return r(51811)}])},51811:function(s,e,r){"use strict";r.r(e),r.d(e,{default:function(){return l}});var i=r(85893);r(25675);var d=r(67294),n=r(11163),a=r(33299),t=r(41664),c=r.n(t);function l(){let{data:s,status:e}=(0,a.useSession)(),{orderNumber:r}=(0,n.useRouter)().query,[t,l]=(0,d.useState)(null),[o,h]=(0,d.useState)(!0),[m,p]=(0,d.useState)(null),x=async()=>{try{h(!0);let s=await fetch("/api/orders/".concat(r));if(!s.ok)throw Error("Failed to fetch order");let e=await s.json();if(e.success)l(e.order);else throw Error(e.message||"Failed to fetch order")}catch(s){console.error("Error fetching order:",s),p(s.message)}finally{h(!1)}};if((0,d.useEffect)(()=>{r&&"authenticated"===e&&x()},[r,e]),"loading"===e||o)return(0,i.jsxs)("div",{className:"container",children:[(0,i.jsx)("h1",{children:"Order Confirmation"}),(0,i.jsx)("p",{children:"Loading order details..."})]});if(m)return(0,i.jsxs)("div",{className:"container",children:[(0,i.jsx)("h1",{children:"Order Confirmation"}),(0,i.jsxs)("div",{className:"error-message",children:[(0,i.jsx)("p",{children:m}),(0,i.jsx)("button",{onClick:x,children:"Try Again"})]})]});if(!t)return(0,i.jsxs)("div",{className:"container",children:[(0,i.jsx)("h1",{children:"Order Confirmation"}),(0,i.jsx)("p",{children:"Loading order details..."})]});let u=s=>new Date(s).toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"order-confirmation",children:[(0,i.jsxs)("div",{className:"order-confirmation-header",children:[(0,i.jsx)("h1",{children:"Order Confirmation"}),(0,i.jsx)("div",{className:"order-confirmation-status",children:(0,i.jsx)("span",{className:"status-badge status-".concat(t.status),children:t.status.toUpperCase()})})]}),(0,i.jsxs)("div",{className:"order-confirmation-message",children:[(0,i.jsx)("p",{children:"Thank you for your order! Your order has been received and is being processed."}),(0,i.jsxs)("p",{children:["Order Number: ",(0,i.jsx)("strong",{children:t.order_number})]}),(0,i.jsxs)("p",{children:["Date: ",u(t.created_at)]})]}),(0,i.jsxs)("div",{className:"order-details",children:[(0,i.jsxs)("div",{className:"order-section",children:[(0,i.jsx)("h2",{children:"Order Summary"}),(0,i.jsx)("div",{className:"order-items",children:t.items.map(s=>(0,i.jsxs)("div",{className:"order-item",children:[(0,i.jsx)("div",{className:"order-item-image",children:(0,i.jsx)("img",{src:s.image_url||"/placeholder.svg",alt:s.product_name})}),(0,i.jsxs)("div",{className:"order-item-details",children:[(0,i.jsx)("div",{className:"order-item-name",children:s.slug?(0,i.jsx)(c(),{href:"/products/".concat(s.slug),children:s.product_name}):s.product_name}),(0,i.jsxs)("div",{className:"order-item-price",children:["$",s.product_price.toFixed(2),s.discount_percentage>0&&(0,i.jsxs)("span",{className:"discount-badge",children:[s.discount_percentage,"% OFF"]})]}),(0,i.jsxs)("div",{className:"order-item-quantity",children:["Quantity: ",s.quantity]})]}),(0,i.jsxs)("div",{className:"order-item-total",children:["$",s.total_price.toFixed(2)]})]},s.id))}),(0,i.jsxs)("div",{className:"order-totals",children:[(0,i.jsxs)("div",{className:"summary-row",children:[(0,i.jsx)("span",{children:"Subtotal:"}),(0,i.jsxs)("span",{children:["$",t.total_amount.toFixed(2)]})]}),(0,i.jsxs)("div",{className:"summary-row",children:[(0,i.jsx)("span",{children:"Shipping:"}),(0,i.jsx)("span",{children:t.shipping_cost>0?"$".concat(t.shipping_cost.toFixed(2)):"Free"})]}),(0,i.jsxs)("div",{className:"summary-row total",children:[(0,i.jsx)("span",{children:"Total:"}),(0,i.jsxs)("span",{children:["$",(t.total_amount+t.shipping_cost).toFixed(2)]})]})]})]}),(0,i.jsxs)("div",{className:"order-section order-info-grid",children:[(0,i.jsxs)("div",{className:"order-info-column",children:[(0,i.jsx)("h3",{children:"Shipping Address"}),(0,i.jsxs)("div",{className:"address-box",children:[(0,i.jsx)("p",{children:t.shipping_address.name}),(0,i.jsx)("p",{children:t.shipping_address.address}),(0,i.jsxs)("p",{children:[t.shipping_address.city,", ",t.shipping_address.state," ",t.shipping_address.zip]}),(0,i.jsx)("p",{children:t.shipping_address.country}),(0,i.jsx)("p",{children:t.shipping_address.phone}),(0,i.jsx)("p",{children:t.shipping_address.email})]})]}),(0,i.jsxs)("div",{className:"order-info-column",children:[(0,i.jsx)("h3",{children:"Billing Address"}),(0,i.jsxs)("div",{className:"address-box",children:[(0,i.jsx)("p",{children:t.billing_address.name}),(0,i.jsx)("p",{children:t.billing_address.address}),(0,i.jsxs)("p",{children:[t.billing_address.city,", ",t.billing_address.state," ",t.billing_address.zip]}),(0,i.jsx)("p",{children:t.billing_address.country}),(0,i.jsx)("p",{children:t.billing_address.phone}),(0,i.jsx)("p",{children:t.billing_address.email})]})]}),(0,i.jsxs)("div",{className:"order-info-column",children:[(0,i.jsx)("h3",{children:"Payment Method"}),(0,i.jsx)("div",{className:"payment-box",children:"credit_card"===t.payment_method.type?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("p",{children:"Credit Card"}),(0,i.jsxs)("p",{children:["Card ending in ",t.payment_method.card_number.slice(-4)]}),(0,i.jsxs)("p",{children:["Status: ",t.payment_status]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("p",{children:"PayPal"}),(0,i.jsxs)("p",{children:["Status: ",t.payment_status]})]})})]}),(0,i.jsxs)("div",{className:"order-info-column",children:[(0,i.jsx)("h3",{children:"Order Status"}),(0,i.jsx)("div",{className:"status-box",children:t.status_history.map((s,e)=>(0,i.jsxs)("div",{className:"status-item",children:[(0,i.jsx)("div",{className:"status-date",children:u(s.created_at)}),(0,i.jsxs)("div",{className:"status-info",children:[(0,i.jsx)("span",{className:"status-badge status-".concat(s.status),children:s.status.toUpperCase()}),s.notes&&(0,i.jsx)("p",{children:s.notes})]})]},e))})]})]})]}),(0,i.jsxs)("div",{className:"order-actions",children:[(0,i.jsx)(c(),{href:"/products",className:"btn btn-primary",children:"Continue Shopping"}),(0,i.jsx)(c(),{href:"/user/orders",className:"btn btn-secondary",children:"View All Orders"})]})]})})}},11163:function(s,e,r){s.exports=r(43079)}},function(s){s.O(0,[1664,5675,2888,9774,179],function(){return s(s.s=5160)}),_N_E=s.O()}]);