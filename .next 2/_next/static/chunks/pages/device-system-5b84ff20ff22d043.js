(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[305],{9889:function(e,i,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/device-system",function(){return s(58231)}])},58231:function(e,i,s){"use strict";let c,n;s.r(i),s.d(i,{__N_SSP:function(){return h},default:function(){return g}});var t=s(85893),a=s(67294),o=s(33299),r=s(11163),d=s(9008),l=s.n(d),m=s(41664),v=s.n(m);try{c=s(56029).Z}catch(e){c=e=>{let{children:i}=e;return(0,t.jsx)("div",{children:(0,t.jsx)("main",{children:i})})}}try{n=s(72381)}catch(e){n={deviceSystemHeader:"",deviceSystemFilters:"",filterGroup:"",deviceSystemResults:"",resultsHeader:"",downloadSection:"",downloadButton:"",deviceGrid:"",deviceCard:"",deviceImageContainer:"",deviceImage:"",conditionBadge:"",outOfStockBadge:"",deviceContent:"",deviceCategory:"",deviceName:"",deviceSpecs:"",specItem:"",specLabel:"",specValue:"",deviceFooter:"",devicePrice:"",deviceStock:"",inStock:"",outOfStock:"",deviceActions:"",detailsButton:"",orderButton:"",noResults:"",resetButton:"",deviceSystemInfo:"",gradingLegend:"",gradingItem:"",gradeBadge:"",conditionA:"",conditionB:"",conditionC:"",conditionD:"",learnMoreLink:"",loadingContainer:"",spinner:"",errorContainer:"",retryButton:""}}var h=!0;function g(){let{data:e,status:i}=(0,o.useSession)(),s=(0,r.useRouter)(),{instock:d}=s.query,[m,h]=(0,a.useState)([]),[g,_]=(0,a.useState)(!0),[p,u]=(0,a.useState)(null),[y,x]=(0,a.useState)({category:"all",brand:"all",condition:"all",inStock:"1"===d,search:""}),S=[{id:1,name:"iPhone 13 Pro",brand:"Apple",category:"Smartphone",condition:"A",price:699.99,stock:15,image:"/images/devices/iphone-13-pro.jpg",specs:{storage:"128GB",color:"Graphite",screen:'6.1" OLED',camera:"Triple 12MP",battery:"3095mAh"}},{id:2,name:"Samsung Galaxy S22",brand:"Samsung",category:"Smartphone",condition:"A",price:649.99,stock:8,image:"/images/devices/samsung-s22.jpg",specs:{storage:"256GB",color:"Phantom Black",screen:'6.1" Dynamic AMOLED',camera:"Triple 50MP",battery:"3700mAh"}},{id:3,name:'iPad Pro 12.9"',brand:"Apple",category:"Tablet",condition:"B",price:899.99,stock:5,image:"/images/devices/ipad-pro.jpg",specs:{storage:"256GB",color:"Space Gray",screen:'12.9" Liquid Retina XDR',camera:"12MP Wide",battery:"10,758mAh"}},{id:4,name:'MacBook Pro 14"',brand:"Apple",category:"Laptop",condition:"A",price:1499.99,stock:3,image:"/images/devices/macbook-pro.jpg",specs:{storage:"512GB SSD",color:"Silver",screen:'14" Liquid Retina XDR',processor:"M1 Pro",memory:"16GB"}},{id:5,name:"Google Pixel 6",brand:"Google",category:"Smartphone",condition:"B",price:499.99,stock:0,image:"/images/devices/pixel-6.jpg",specs:{storage:"128GB",color:"Stormy Black",screen:'6.4" OLED',camera:"50MP Wide",battery:"4614mAh"}},{id:6,name:"Samsung Galaxy Tab S8",brand:"Samsung",category:"Tablet",condition:"A",price:649.99,stock:7,image:"/images/devices/galaxy-tab-s8.jpg",specs:{storage:"128GB",color:"Graphite",screen:'11" TFT LCD',camera:"13MP",battery:"8000mAh"}},{id:7,name:"iPhone 12",brand:"Apple",category:"Smartphone",condition:"C",price:499.99,stock:12,image:"/images/devices/iphone-12.jpg",specs:{storage:"64GB",color:"Blue",screen:'6.1" Super Retina XDR',camera:"Dual 12MP",battery:"2815mAh"}},{id:8,name:"Dell XPS 13",brand:"Dell",category:"Laptop",condition:"B",price:999.99,stock:0,image:"/images/devices/dell-xps-13.jpg",specs:{storage:"512GB SSD",color:"Platinum Silver",screen:'13.4" FHD+',processor:"Intel Core i7",memory:"16GB"}}];(0,a.useEffect)(()=>{(async()=>{try{_(!0),await new Promise(e=>setTimeout(e,1e3)),h(S)}catch(e){console.error("Error fetching devices:",e),u("Failed to load devices. Please try again.")}finally{_(!1)}})()},[]);let j=(e,i)=>{x(s=>({...s,[e]:i}))},k=m.filter(e=>(!y.inStock||!(e.stock<=0))&&("all"===y.category||e.category===y.category)&&("all"===y.brand||e.brand===y.brand)&&("all"===y.condition||e.condition===y.condition)&&(!y.search||!!e.name.toLowerCase().includes(y.search.toLowerCase()))),D=["all",...new Set(m.map(e=>e.category))],B=["all",...new Set(m.map(e=>e.brand))];return((0,a.useEffect)(()=>{"unauthenticated"===i&&s.push("/auth/signin?callbackUrl=/device-system")},[i,s]),"loading"===i||g)?(0,t.jsx)(c,{children:(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:n.loadingContainer,children:[(0,t.jsx)("div",{className:n.spinner}),(0,t.jsx)("p",{children:"Loading device inventory..."})]})})}):p?(0,t.jsx)(c,{children:(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:n.errorContainer,children:[(0,t.jsx)("h2",{children:"Error"}),(0,t.jsx)("p",{children:p}),(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:n.retryButton,children:"Try Again"})]})})}):(0,t.jsxs)(c,{children:[(0,t.jsxs)(l(),{children:[(0,t.jsx)("title",{children:"Device Inventory System | MDTS - Midas Technical Solutions"}),(0,t.jsx)("meta",{name:"description",content:"Browse our inventory of devices and parts at MDTS - Midas Technical Solutions."})]}),(0,t.jsx)("main",{className:"main-content",children:(0,t.jsxs)("div",{className:"container",children:[(0,t.jsxs)("div",{className:n.deviceSystemHeader,children:[(0,t.jsx)("h1",{children:"Device Inventory System"}),(0,t.jsx)("p",{children:"Browse our complete inventory of devices and parts. Use the filters below to find exactly what you need."})]}),(0,t.jsxs)("div",{className:n.deviceSystemFilters,children:[(0,t.jsxs)("div",{className:n.filterGroup,children:[(0,t.jsx)("label",{htmlFor:"search",children:"Search:"}),(0,t.jsx)("input",{type:"text",id:"search",placeholder:"Search devices...",value:y.search,onChange:e=>j("search",e.target.value)})]}),(0,t.jsxs)("div",{className:n.filterGroup,children:[(0,t.jsx)("label",{htmlFor:"category",children:"Category:"}),(0,t.jsx)("select",{id:"category",value:y.category,onChange:e=>j("category",e.target.value),children:D.map(e=>(0,t.jsx)("option",{value:e,children:"all"===e?"All Categories":e},e))})]}),(0,t.jsxs)("div",{className:n.filterGroup,children:[(0,t.jsx)("label",{htmlFor:"brand",children:"Brand:"}),(0,t.jsx)("select",{id:"brand",value:y.brand,onChange:e=>j("brand",e.target.value),children:B.map(e=>(0,t.jsx)("option",{value:e,children:"all"===e?"All Brands":e},e))})]}),(0,t.jsxs)("div",{className:n.filterGroup,children:[(0,t.jsx)("label",{htmlFor:"condition",children:"Condition:"}),(0,t.jsx)("select",{id:"condition",value:y.condition,onChange:e=>j("condition",e.target.value),children:["all","A","B","C","D"].map(e=>(0,t.jsx)("option",{value:e,children:"all"===e?"All Conditions":"Grade ".concat(e)},e))})]}),(0,t.jsx)("div",{className:n.filterGroup,children:(0,t.jsxs)("label",{className:n.checkboxLabel,children:[(0,t.jsx)("input",{type:"checkbox",checked:y.inStock,onChange:e=>j("inStock",e.target.checked)}),"In Stock Only"]})})]}),(0,t.jsxs)("div",{className:n.deviceSystemResults,children:[(0,t.jsxs)("div",{className:n.resultsHeader,children:[(0,t.jsxs)("h2",{children:["Results (",k.length," devices)"]}),(0,t.jsxs)("div",{className:n.downloadSection,children:[(0,t.jsxs)("button",{className:n.downloadButton,children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}),(0,t.jsx)("polyline",{points:"7 10 12 15 17 10"}),(0,t.jsx)("line",{x1:"12",y1:"15",x2:"12",y2:"3"})]}),"Download Inventory CSV"]}),(0,t.jsxs)("button",{className:n.downloadButton,children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),(0,t.jsx)("polyline",{points:"14 2 14 8 20 8"}),(0,t.jsx)("line",{x1:"16",y1:"13",x2:"8",y2:"13"}),(0,t.jsx)("line",{x1:"16",y1:"17",x2:"8",y2:"17"}),(0,t.jsx)("polyline",{points:"10 9 9 9 8 9"})]}),"Download PDF Catalog"]})]})]}),0===k.length?(0,t.jsxs)("div",{className:n.noResults,children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("circle",{cx:"11",cy:"11",r:"8"}),(0,t.jsx)("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65"}),(0,t.jsx)("line",{x1:"8",y1:"11",x2:"14",y2:"11"})]}),(0,t.jsx)("h3",{children:"No devices found"}),(0,t.jsx)("p",{children:"Try adjusting your filters or search criteria."}),(0,t.jsx)("button",{onClick:()=>x({category:"all",brand:"all",condition:"all",inStock:!1,search:""}),className:n.resetButton,children:"Reset Filters"})]}):(0,t.jsx)("div",{className:n.deviceGrid,children:k.map(e=>(0,t.jsxs)("div",{className:n.deviceCard,children:[(0,t.jsxs)("div",{className:n.deviceImageContainer,children:[(0,t.jsx)("img",{src:e.image||"/images/placeholder.svg",alt:e.name,className:n.deviceImage}),(0,t.jsxs)("div",{className:"".concat(n.conditionBadge," ").concat(n["condition".concat(e.condition)]),children:["Grade ",e.condition]}),e.stock<=0&&(0,t.jsx)("div",{className:n.outOfStockBadge,children:"Out of Stock"})]}),(0,t.jsxs)("div",{className:n.deviceContent,children:[(0,t.jsxs)("div",{className:n.deviceCategory,children:[e.category," • ",e.brand]}),(0,t.jsx)("h3",{className:n.deviceName,children:e.name}),(0,t.jsx)("div",{className:n.deviceSpecs,children:Object.entries(e.specs).map(e=>{let[i,s]=e;return(0,t.jsxs)("div",{className:n.specItem,children:[(0,t.jsxs)("span",{className:n.specLabel,children:[i,":"]}),(0,t.jsx)("span",{className:n.specValue,children:s})]},i)})}),(0,t.jsxs)("div",{className:n.deviceFooter,children:[(0,t.jsxs)("div",{className:n.devicePrice,children:["$",e.price.toFixed(2)]}),(0,t.jsx)("div",{className:n.deviceStock,children:e.stock>0?(0,t.jsxs)("span",{className:n.inStock,children:["In Stock: ",e.stock]}):(0,t.jsx)("span",{className:n.outOfStock,children:"Out of Stock"})})]}),(0,t.jsxs)("div",{className:n.deviceActions,children:[(0,t.jsx)("button",{className:n.detailsButton,onClick:()=>s.push("/devices/".concat(e.id)),children:"View Details"}),(0,t.jsx)("button",{className:n.orderButton,disabled:e.stock<=0,children:"Order Now"})]})]})]},e.id))})]}),(0,t.jsxs)("div",{className:n.deviceSystemInfo,children:[(0,t.jsx)("h2",{children:"Device Grading System"}),(0,t.jsx)("p",{children:"All devices in our inventory are graded according to our standardized grading system. This ensures transparency and helps you make informed decisions."}),(0,t.jsxs)("div",{className:n.gradingLegend,children:[(0,t.jsxs)("div",{className:n.gradingItem,children:[(0,t.jsx)("div",{className:"".concat(n.gradeBadge," ").concat(n.conditionA),children:"Grade A"}),(0,t.jsx)("span",{children:"Excellent condition, like new"})]}),(0,t.jsxs)("div",{className:n.gradingItem,children:[(0,t.jsx)("div",{className:"".concat(n.gradeBadge," ").concat(n.conditionB),children:"Grade B"}),(0,t.jsx)("span",{children:"Good condition, minor wear"})]}),(0,t.jsxs)("div",{className:n.gradingItem,children:[(0,t.jsx)("div",{className:"".concat(n.gradeBadge," ").concat(n.conditionC),children:"Grade C"}),(0,t.jsx)("span",{children:"Fair condition, noticeable wear"})]}),(0,t.jsxs)("div",{className:n.gradingItem,children:[(0,t.jsx)("div",{className:"".concat(n.gradeBadge," ").concat(n.conditionD),children:"Grade D"}),(0,t.jsx)("span",{children:"Poor condition, significant wear"})]})]}),(0,t.jsx)(v(),{href:"/device-grading",className:n.learnMoreLink,passHref:!0,children:(0,t.jsx)("span",{children:"Learn more about our grading system"})})]})]})})]})}},72381:function(e){e.exports={deviceSystemHeader:"DeviceSystem_deviceSystemHeader__rmz0z",deviceSystemFilters:"DeviceSystem_deviceSystemFilters__LdJee",filterGroup:"DeviceSystem_filterGroup__x9Kk9",checkboxLabel:"DeviceSystem_checkboxLabel__24V36",deviceSystemResults:"DeviceSystem_deviceSystemResults__fv7es",resultsHeader:"DeviceSystem_resultsHeader__WpmtC",downloadSection:"DeviceSystem_downloadSection__4BriD",downloadButton:"DeviceSystem_downloadButton__ZBkWt",deviceGrid:"DeviceSystem_deviceGrid__1xqQE",deviceCard:"DeviceSystem_deviceCard__Y_cVD",deviceImageContainer:"DeviceSystem_deviceImageContainer__avm_B",deviceImage:"DeviceSystem_deviceImage__ky9R1",conditionBadge:"DeviceSystem_conditionBadge__LN6Hj",conditionA:"DeviceSystem_conditionA__ZxdOn",conditionB:"DeviceSystem_conditionB__GaMqQ",conditionC:"DeviceSystem_conditionC__l3_Cj",conditionD:"DeviceSystem_conditionD__8PyF7",outOfStockBadge:"DeviceSystem_outOfStockBadge__XYIPe",deviceContent:"DeviceSystem_deviceContent__ucwys",deviceCategory:"DeviceSystem_deviceCategory__ZS5T0",deviceName:"DeviceSystem_deviceName__rHLKs",deviceSpecs:"DeviceSystem_deviceSpecs__P7JMU",specItem:"DeviceSystem_specItem__cpxGJ",specLabel:"DeviceSystem_specLabel____7vA",specValue:"DeviceSystem_specValue__21kTh",deviceFooter:"DeviceSystem_deviceFooter__BJEvR",devicePrice:"DeviceSystem_devicePrice__3d7Ap",deviceStock:"DeviceSystem_deviceStock__KITr3",inStock:"DeviceSystem_inStock__j2pKH",outOfStock:"DeviceSystem_outOfStock__PKPXM",deviceActions:"DeviceSystem_deviceActions__gS4Ox",detailsButton:"DeviceSystem_detailsButton__ht2lY",orderButton:"DeviceSystem_orderButton__PkQgo",noResults:"DeviceSystem_noResults__UDWDY",resetButton:"DeviceSystem_resetButton__Oc67E",deviceSystemInfo:"DeviceSystem_deviceSystemInfo__2aUOP",gradingLegend:"DeviceSystem_gradingLegend__PasRh",gradingItem:"DeviceSystem_gradingItem__iBiXW",gradeBadge:"DeviceSystem_gradeBadge__xCsS2",learnMoreLink:"DeviceSystem_learnMoreLink___wjRb",loadingContainer:"DeviceSystem_loadingContainer__Oq1YC",spinner:"DeviceSystem_spinner__O1Hlo",spin:"DeviceSystem_spin__vSGFB",errorContainer:"DeviceSystem_errorContainer__vqhNS",retryButton:"DeviceSystem_retryButton__IDW_A"}},11163:function(e,i,s){e.exports=s(43079)}},function(e){e.O(0,[1664,5675,6029,2888,9774,179],function(){return e(e.s=9889)}),_N_E=e.O()}]);