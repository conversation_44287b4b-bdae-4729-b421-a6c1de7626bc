(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9591],{20813:function(e,a,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/categories/[slug]",function(){return s(498)}])},498:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return u}});var t=s(85893),r=s(67294),i=s(11163),n=s(9008),o=s.n(n),c=s(41664),d=s.n(c),l=s(76215),g=s(8788),p=s.n(g);function u(){let e=(0,i.useRouter)(),{slug:a}=e.query,[s,n]=(0,r.useState)([]),[c,g]=(0,r.useState)(null),[u,m]=(0,r.useState)(!0),[h,_]=(0,r.useState)(null),[x,j]=(0,r.useState)(1),[y]=(0,r.useState)(12),[P,w]=(0,r.useState)(0),[C,v]=(0,r.useState)(1),[f,k]=(0,r.useState)([{id:1,name:"Apple",slug:"apple"},{id:2,name:"Samsung",slug:"samsung"},{id:3,name:"LG",slug:"lg"},{id:4,name:"Huawei",slug:"huawei"},{id:5,name:"Xiaomi",slug:"xiaomi"}]),N={"iphone-parts":{name:"iPhone Parts",description:"High-quality replacement parts for all iPhone models",image:"/images/gapp/iphone-parts.png",models:["iPhone 15","iPhone 14","iPhone 13","iPhone 12","iPhone 11","iPhone X","iPhone 8","iPhone 7","iPhone 6"]},"samsung-parts":{name:"Samsung Parts",description:"Genuine replacement parts for Samsung Galaxy devices",image:"/images/gapp/samsung-parts.png",models:["Galaxy S23","Galaxy S22","Galaxy S21","Galaxy S20","Galaxy Note 20","Galaxy Note 10","Galaxy A Series"]},"ipad-parts":{name:"iPad Parts",description:"Premium replacement parts for all iPad models",image:"/images/gapp/ipad-parts.png",models:["iPad Pro","iPad Air","iPad Mini","iPad (Standard)"]},"macbook-parts":{name:"MacBook Parts",description:"High-quality replacement parts for MacBook laptops",image:"/images/gapp/macbook-parts.png",models:["MacBook Pro","MacBook Air","MacBook (Standard)"]},"repair-tools":{name:"Repair Tools",description:"Professional-grade tools for device repair and maintenance",image:"/images/gapp/repair-tools.png",types:["Screwdriver Sets","Opening Tools","Heat Guns","Soldering Equipment","Microscopes","Testing Equipment"]}},b=e=>{var a,s;let t=(null===(a=e.name)||void 0===a?void 0:a.toLowerCase())||"",r=(null===(s=e.category_name)||void 0===s?void 0:s.toLowerCase())||"";if(r.includes("iphone")){if(t.includes("screen")||t.includes("display")||t.includes("lcd"))return"/images/gapp/iphone-screen.jpg";if(t.includes("battery"))return"/images/gapp/iphone-battery.jpg";if(t.includes("camera"))return"/images/gapp/iphone-camera.jpg";if(t.includes("charging")||t.includes("port")||t.includes("connector"))return"/images/gapp/iphone-charging-port.jpg";else if(t.includes("back")||t.includes("glass")||t.includes("housing"))return"/images/gapp/iphone-back-glass.jpg";else return"/images/gapp/iphone-parts.png"}if(r.includes("samsung"))return t.includes("screen")||t.includes("display")||t.includes("lcd")?"/images/gapp/samsung-screen.jpg":t.includes("battery")?"/images/gapp/samsung-battery.jpg":t.includes("camera")?"/images/gapp/samsung-camera.jpg":t.includes("charging")||t.includes("port")||t.includes("connector")?"/images/gapp/samsung-charging-port.jpg":"/images/gapp/samsung-parts.png";if(r.includes("ipad"))return t.includes("screen")||t.includes("display")||t.includes("lcd")?"/images/gapp/ipad-screen.jpg":t.includes("battery")?"/images/gapp/ipad-battery.jpg":t.includes("camera")?"/images/gapp/ipad-camera.jpg":"/images/gapp/ipad-parts.png";if(r.includes("macbook"))return t.includes("screen")||t.includes("display")||t.includes("lcd")?"/images/gapp/macbook-screen.jpg":t.includes("keyboard")?"/images/gapp/macbook-keyboard.jpg":t.includes("battery")?"/images/gapp/macbook-battery.jpg":t.includes("trackpad")?"/images/gapp/macbook-trackpad.jpg":"/images/gapp/macbook-parts.png";if(!r.includes("tool"))return"/images/gapp/certified.png";if(t.includes("screwdriver")||t.includes("driver"))return"/images/gapp/screwdriver-set.jpg";if(t.includes("heat")||t.includes("gun"))return"/images/gapp/heat-gun.jpg";if(t.includes("opening")||t.includes("pry"))return"/images/gapp/opening-tools.jpg";if(t.includes("solder"))return"/images/gapp/soldering-kit.jpg";if(t.includes("microscope")||t.includes("magnifier"))return"/images/gapp/microscope.jpg";else return"/images/gapp/repair-tools.png"};return((0,r.useEffect)(()=>{a&&(g(N[a]||null),(async()=>{try{m(!0);let e="page=".concat(x,"&limit=").concat(y,"&category=").concat(a),s=await fetch("/api/products?".concat(e));if(!s.ok)throw Error("Failed to fetch products");let t=await s.json();if(t.success)n(t.products),w(t.total||t.products.length),v(t.totalPages||Math.ceil(t.total/y)||1);else throw Error(t.message||"Failed to fetch products")}catch(e){console.error("Error fetching products:",e),_(e.message)}finally{m(!1)}})())},[a,x,y]),a&&c)?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(o(),{children:[(0,t.jsxs)("title",{children:[c.name," | MDTS - Midas Technical Solutions"]}),(0,t.jsx)("meta",{name:"description",content:c.description})]}),(0,t.jsxs)("div",{className:"container",children:[(0,t.jsxs)("div",{className:p().categoryHeader,children:[(0,t.jsxs)("div",{className:p().categoryInfo,children:[(0,t.jsx)("h1",{children:c.name}),(0,t.jsx)("p",{children:c.description}),(0,t.jsxs)("div",{className:p().breadcrumbs,children:[(0,t.jsx)(d(),{href:"/",children:"Home"})," >",(0,t.jsx)(d(),{href:"/categories",children:"Categories"})," >",(0,t.jsx)("span",{children:c.name})]})]}),(0,t.jsx)("div",{className:p().categoryImage,children:(0,t.jsx)("img",{src:c.image,alt:c.name})})]}),(0,t.jsxs)("div",{className:p().modelsSection,children:[(0,t.jsx)("h2",{children:c.models?"Available Models":"Available Types"}),(0,t.jsx)("div",{className:p().modelsList,children:c.models?c.models.map((e,s)=>(0,t.jsx)(d(),{href:"/products?category=".concat(a,"&model=").concat(e.toLowerCase().replace(/\s+/g,"-")),className:p().modelItem,children:e},s)):c.types.map((e,s)=>(0,t.jsx)(d(),{href:"/products?category=".concat(a,"&type=").concat(e.toLowerCase().replace(/\s+/g,"-")),className:p().modelItem,children:e},s))})]}),(0,t.jsxs)("div",{className:p().productsLayout,children:[(0,t.jsx)(l.Z,{brands:f,onFilterChange:e=>{}}),(0,t.jsx)("div",{className:p().productsContent,children:u?(0,t.jsxs)("div",{className:"loading-container",children:[(0,t.jsx)("div",{className:"loading-spinner"}),(0,t.jsx)("p",{children:"Loading products..."})]}):h?(0,t.jsxs)("div",{className:"error-message",children:[(0,t.jsxs)("p",{children:["Error: ",h]}),(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:"btn btn-primary",children:"Try Again"})]}):0===s.length?(0,t.jsxs)("div",{className:p().emptyProducts,children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("circle",{cx:"9",cy:"21",r:"1"}),(0,t.jsx)("circle",{cx:"20",cy:"21",r:"1"}),(0,t.jsx)("path",{d:"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"})]}),(0,t.jsx)("h3",{children:"No products found"}),(0,t.jsx)("p",{children:"Try adjusting your filters or browse all products"}),(0,t.jsx)("button",{className:p().resetButton,onClick:()=>e.push("/categories/".concat(a)),children:"Reset Filters"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:p().resultsInfo,children:(0,t.jsxs)("p",{children:["Showing ",s.length," of ",P," products"]})}),(0,t.jsx)("div",{className:p().productsGrid,children:s.map(e=>(0,t.jsxs)("div",{className:p().productCard,children:[(0,t.jsxs)("div",{className:p().productImageContainer,children:[(0,t.jsx)("img",{src:e.image_url||b(e),alt:e.name,className:p().productImage}),e.discount_percentage>0&&(0,t.jsxs)("span",{className:p().discountBadge,children:[e.discount_percentage,"% OFF"]}),(0,t.jsxs)("div",{className:p().productActions,children:[(0,t.jsx)("button",{className:p().wishlistButton,title:"Add to Wishlist",children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,t.jsx)("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})})}),(0,t.jsx)("button",{className:p().quickViewButton,title:"Quick View",children:(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),(0,t.jsx)("circle",{cx:"12",cy:"12",r:"3"})]})})]})]}),(0,t.jsxs)("div",{className:p().productContent,children:[(0,t.jsx)("div",{className:p().productCategory,children:e.category_name}),(0,t.jsx)("h3",{className:p().productName,children:(0,t.jsx)(d(),{href:"/products/".concat(e.slug),children:e.name})}),(0,t.jsx)("div",{className:p().productPrice,children:e.discount_percentage>0?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("span",{className:p().originalPrice,children:["$",(e.price/(1-e.discount_percentage/100)).toFixed(2)]}),(0,t.jsxs)("span",{className:p().currentPrice,children:["$",e.price.toFixed(2)]})]}):(0,t.jsxs)("span",{className:p().currentPrice,children:["$",e.price.toFixed(2)]})}),(0,t.jsxs)("div",{className:p().productButtons,children:[(0,t.jsx)(d(),{href:"/products/".concat(e.slug),className:p().viewDetailsButton,children:"View Details"}),(0,t.jsx)("button",{className:p().addToCartButton,children:(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("circle",{cx:"9",cy:"21",r:"1"}),(0,t.jsx)("circle",{cx:"20",cy:"21",r:"1"}),(0,t.jsx)("path",{d:"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"})]})})]})]})]},e.id))}),(0,t.jsxs)("div",{className:p().pagination,children:[(0,t.jsxs)("button",{onClick:()=>{j(Math.max(x-1,1))},disabled:1===x,className:"".concat(p().paginationButton," ").concat(1===x?p().disabled:""),children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,t.jsx)("polyline",{points:"15 18 9 12 15 6"})}),"Previous"]}),(0,t.jsx)("div",{className:p().paginationPages,children:[...Array(C)].map((e,a)=>(0,t.jsx)("button",{onClick:()=>j(a+1),className:"".concat(p().pageNumber," ").concat(x===a+1?p().activePage:""),children:a+1},a))}),(0,t.jsxs)("button",{onClick:()=>{j(x+1)},disabled:x>=C,className:"".concat(p().paginationButton," ").concat(x>=C?p().disabled:""),children:["Next",(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,t.jsx)("polyline",{points:"9 18 15 12 9 6"})})]})]})]})})]})]})]}):(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"loading-container",children:[(0,t.jsx)("div",{className:"loading-spinner"}),(0,t.jsx)("p",{children:"Loading category..."})]})})}},8788:function(e){e.exports={categoryHeader:"CategoryPage_categoryHeader__1F_uV",categoryInfo:"CategoryPage_categoryInfo__vaQVt",breadcrumbs:"CategoryPage_breadcrumbs__4wz1I",categoryImage:"CategoryPage_categoryImage__ZhC8b",modelsSection:"CategoryPage_modelsSection__BCRAD",modelsList:"CategoryPage_modelsList__eTK25",modelItem:"CategoryPage_modelItem__uIkKk",productsLayout:"CategoryPage_productsLayout__wYkNs",productsContent:"CategoryPage_productsContent___HA1r",resultsInfo:"CategoryPage_resultsInfo__7hrp_",productsGrid:"CategoryPage_productsGrid__JQJjI",productCard:"CategoryPage_productCard__1XN_2",productImageContainer:"CategoryPage_productImageContainer__3b89y",productImage:"CategoryPage_productImage__WU86q",discountBadge:"CategoryPage_discountBadge__wVQto",productActions:"CategoryPage_productActions__kuwyI",quickViewButton:"CategoryPage_quickViewButton__PU04D",wishlistButton:"CategoryPage_wishlistButton__z1GJe",productContent:"CategoryPage_productContent__8z7ZH",productCategory:"CategoryPage_productCategory__KSdLt",productName:"CategoryPage_productName__TMtM_",productPrice:"CategoryPage_productPrice__FnxgY",originalPrice:"CategoryPage_originalPrice__4F3gm",currentPrice:"CategoryPage_currentPrice__8SW_n",productButtons:"CategoryPage_productButtons__x_FYA",viewDetailsButton:"CategoryPage_viewDetailsButton__A9pku",addToCartButton:"CategoryPage_addToCartButton__7hpA_",emptyProducts:"CategoryPage_emptyProducts__dXD4m",resetButton:"CategoryPage_resetButton__e8IA3",pagination:"CategoryPage_pagination__xhe4q",paginationButton:"CategoryPage_paginationButton__G2GdW",disabled:"CategoryPage_disabled__yALpY",paginationPages:"CategoryPage_paginationPages___HGku",pageNumber:"CategoryPage_pageNumber__iwIXy",activePage:"CategoryPage_activePage__XfgoN"}}},function(e){e.O(0,[1664,6215,2888,9774,179],function(){return e(e.s=20813)}),_N_E=e.O()}]);