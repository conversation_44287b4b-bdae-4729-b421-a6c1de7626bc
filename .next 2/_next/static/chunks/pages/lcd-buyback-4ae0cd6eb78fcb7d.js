(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7238],{38301:function(e,o,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/lcd-buyback",function(){return r(30852)}])},30852:function(e,o,r){"use strict";r.r(o),r.d(o,{default:function(){return d}});var i=r(85893),n=r(67294);r(9008);var c=r(58144),s=r.n(c);r(41664);var a=r(11163),t=r(56029);function d(){(0,a.useRouter)();let[e,o]=(0,n.useState)({deviceType:"",deviceModel:"",condition:"",quantity:1,email:"",phone:"",comments:""}),[r,c]=(0,n.useState)(!1),[d,l]=(0,n.useState)(""),[u,h]=(0,n.useState)(""),m=e=>{let{name:r,value:i}=e.target;o(e=>({...e,[r]:i}))},_=async r=>{r.preventDefault(),c(!0),l(""),h("");try{(await fetch("/api/send-email",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({to:"<EMAIL>",subject:"New LCD Buyback Request",formData:e})})).ok?(h("success"),l("Your request has been submitted successfully! We will contact you shortly with a quote."),o({deviceType:"",deviceModel:"",condition:"",quantity:1,email:"",phone:"",comments:""})):(h("error"),l("There was an error submitting your request. Please try again or contact us directly."))}catch(e){console.error("Error submitting form:",e),h("error"),l("There was an error submitting your request. Please try again or contact us directly.")}finally{c(!1)}};return(0,i.jsxs)(t.Z,{title:"LCD Buyback Program - Midas Technical Solutions",description:"Sell your old LCD screens and get cash back with our LCD Buyback Program. Fast, easy, and competitive prices.",children:[(0,i.jsx)("div",{className:s().breadcrumb,children:(0,i.jsxs)("div",{className:s().container,children:[(0,i.jsx)("span",{children:"Home"})," > ",(0,i.jsx)("span",{className:s().active,children:"LCD Buyback"})]})}),(0,i.jsxs)("main",{children:[(0,i.jsx)("section",{className:s().hero,children:(0,i.jsxs)("div",{className:s().heroContent,children:[(0,i.jsx)("h1",{children:"LCD Buyback Program"}),(0,i.jsx)("p",{children:"Turn your old LCD screens into cash! We buy broken, used, and new LCD screens for iPhones, Samsung, iPads, and more."}),(0,i.jsx)("button",{className:s().heroButton,children:"Get Started"})]})}),(0,i.jsx)("section",{className:s().infoSection,children:(0,i.jsx)("div",{className:s().container,children:(0,i.jsxs)("div",{className:s().infoGrid,children:[(0,i.jsxs)("div",{className:s().infoCard,children:[(0,i.jsx)("div",{className:s().infoIcon,children:"\uD83D\uDCB0"}),(0,i.jsx)("h3",{children:"Competitive Pricing"}),(0,i.jsx)("p",{children:"We offer some of the best prices in the industry for your used and broken LCD screens."})]}),(0,i.jsxs)("div",{className:s().infoCard,children:[(0,i.jsx)("div",{className:s().infoIcon,children:"\uD83D\uDE9A"}),(0,i.jsx)("h3",{children:"Free Shipping"}),(0,i.jsx)("p",{children:"We provide free shipping labels for all buyback orders over $1000."})]}),(0,i.jsxs)("div",{className:s().infoCard,children:[(0,i.jsx)("div",{className:s().infoIcon,children:"⚡"}),(0,i.jsx)("h3",{children:"Fast Payment"}),(0,i.jsx)("p",{children:"Get paid within 48 hours of us receiving and verifying your LCD screens."})]}),(0,i.jsxs)("div",{className:s().infoCard,children:[(0,i.jsx)("div",{className:s().infoIcon,children:"♻️"}),(0,i.jsx)("h3",{children:"Eco-Friendly"}),(0,i.jsx)("p",{children:"Help the environment by recycling your old LCD screens instead of throwing them away."})]})]})})}),(0,i.jsx)("section",{className:s().formSection,children:(0,i.jsx)("div",{className:s().container,children:(0,i.jsxs)("div",{className:s().formWrapper,children:[(0,i.jsx)("h2",{children:"Submit Your Device Details"}),(0,i.jsx)("p",{children:"Fill out the form below to get an instant quote for your LCD screens."}),(0,i.jsxs)("form",{className:s().buybackForm,onSubmit:_,children:[d&&(0,i.jsx)("div",{className:"".concat(s().formMessage," ").concat(s()[u]),children:d}),(0,i.jsxs)("div",{className:s().formGroup,children:[(0,i.jsx)("label",{htmlFor:"deviceType",children:"Device Type"}),(0,i.jsxs)("select",{id:"deviceType",name:"deviceType",value:e.deviceType,onChange:m,required:!0,children:[(0,i.jsx)("option",{value:"",children:"Select Device Type"}),(0,i.jsx)("option",{value:"iphone",children:"iPhone"}),(0,i.jsx)("option",{value:"samsung",children:"Samsung"}),(0,i.jsx)("option",{value:"ipad",children:"iPad"}),(0,i.jsx)("option",{value:"macbook",children:"MacBook"}),(0,i.jsx)("option",{value:"other",children:"Other"})]})]}),(0,i.jsxs)("div",{className:s().formGroup,children:[(0,i.jsx)("label",{htmlFor:"deviceModel",children:"Device Model"}),(0,i.jsx)("input",{type:"text",id:"deviceModel",name:"deviceModel",placeholder:"e.g., iPhone 13 Pro, Galaxy S22",value:e.deviceModel,onChange:m,required:!0})]}),(0,i.jsxs)("div",{className:s().formGroup,children:[(0,i.jsx)("label",{htmlFor:"condition",children:"Condition"}),(0,i.jsxs)("select",{id:"condition",name:"condition",value:e.condition,onChange:m,required:!0,children:[(0,i.jsx)("option",{value:"",children:"Select Condition"}),(0,i.jsx)("option",{value:"new",children:"New/Like New"}),(0,i.jsx)("option",{value:"good",children:"Good (Minor Scratches)"}),(0,i.jsx)("option",{value:"fair",children:"Fair (Visible Wear)"}),(0,i.jsx)("option",{value:"broken",children:"Broken (Still Powers On)"}),(0,i.jsx)("option",{value:"damaged",children:"Damaged (Does Not Power On)"})]})]}),(0,i.jsxs)("div",{className:s().formGroup,children:[(0,i.jsx)("label",{htmlFor:"quantity",children:"Quantity"}),(0,i.jsx)("input",{type:"number",id:"quantity",name:"quantity",min:"1",value:e.quantity,onChange:m,required:!0})]}),(0,i.jsxs)("div",{className:s().formGroup,children:[(0,i.jsx)("label",{htmlFor:"email",children:"Email Address"}),(0,i.jsx)("input",{type:"email",id:"email",name:"email",placeholder:"Your email address",value:e.email,onChange:m,required:!0})]}),(0,i.jsxs)("div",{className:s().formGroup,children:[(0,i.jsx)("label",{htmlFor:"phone",children:"Phone Number (Optional)"}),(0,i.jsx)("input",{type:"tel",id:"phone",name:"phone",placeholder:"Your phone number",value:e.phone,onChange:m})]}),(0,i.jsxs)("div",{className:s().formGroup,children:[(0,i.jsx)("label",{htmlFor:"comments",children:"Additional Comments (Optional)"}),(0,i.jsx)("textarea",{id:"comments",name:"comments",rows:"3",placeholder:"Any additional information about your LCD screens",value:e.comments,onChange:m})]}),(0,i.jsx)("button",{type:"submit",className:s().submitButton,disabled:r,children:r?"Submitting...":"Get Quote"})]})]})})}),(0,i.jsx)("section",{className:s().faqSection,children:(0,i.jsxs)("div",{className:s().container,children:[(0,i.jsx)("h2",{children:"Frequently Asked Questions"}),(0,i.jsxs)("div",{className:s().faqGrid,children:[(0,i.jsxs)("div",{className:s().faqItem,children:[(0,i.jsx)("h3",{children:"What types of LCD screens do you buy?"}),(0,i.jsx)("p",{children:"We buy LCD screens for iPhones, Samsung phones, iPads, MacBooks, and other popular devices. Both working and non-working screens are accepted."})]}),(0,i.jsxs)("div",{className:s().faqItem,children:[(0,i.jsx)("h3",{children:"How is the value determined?"}),(0,i.jsx)("p",{children:"The value is determined based on the device model, condition of the screen, and current market demand. We strive to offer competitive prices."})]}),(0,i.jsxs)("div",{className:s().faqItem,children:[(0,i.jsx)("h3",{children:"How do I ship my LCD screens to you?"}),(0,i.jsx)("p",{children:"After accepting our quote, we'll provide shipping instructions and a prepaid shipping label for orders over $1000. For smaller orders, you can use your preferred shipping method."})]}),(0,i.jsxs)("div",{className:s().faqItem,children:[(0,i.jsx)("h3",{children:"How will I get paid?"}),(0,i.jsx)("p",{children:"We offer payment via PayPal, direct bank transfer, or store credit (with a 10% bonus). Payments are processed within 48 hours after we receive and verify your LCD screens."})]})]})]})})]})]})}},58144:function(e){e.exports={breadcrumb:"LcdBuyback_breadcrumb__RtFaD",container:"LcdBuyback_container__VRH_6",active:"LcdBuyback_active__fpzR2",hero:"LcdBuyback_hero__KAnbe",heroContent:"LcdBuyback_heroContent__DCqor",heroButton:"LcdBuyback_heroButton__P74f1",infoSection:"LcdBuyback_infoSection__f_TFF",infoGrid:"LcdBuyback_infoGrid__Hxtz_",infoCard:"LcdBuyback_infoCard__uVvM1",infoIcon:"LcdBuyback_infoIcon__U3QOL",formSection:"LcdBuyback_formSection__ycWEe",formWrapper:"LcdBuyback_formWrapper__xlZqk",buybackForm:"LcdBuyback_buybackForm__CcrLm",formGroup:"LcdBuyback_formGroup__hX4bU",submitButton:"LcdBuyback_submitButton__TzEaf",formMessage:"LcdBuyback_formMessage__4I2Dk",success:"LcdBuyback_success__ANtwj",error:"LcdBuyback_error__ZMa7s",faqSection:"LcdBuyback_faqSection__C_nUI",faqGrid:"LcdBuyback_faqGrid__abb6o",faqItem:"LcdBuyback_faqItem__xgSDO",footer:"LcdBuyback_footer__QEldZ",footerContainer:"LcdBuyback_footerContainer__WPGF4",footerTop:"LcdBuyback_footerTop__VRKao",footerNewsletter:"LcdBuyback_footerNewsletter__z0OZX",footerForm:"LcdBuyback_footerForm__pZsD3",footerServices:"LcdBuyback_footerServices___qQIc",footerService:"LcdBuyback_footerService__fLHR8",footerServiceIcon:"LcdBuyback_footerServiceIcon__Bpj6l",footerServiceName:"LcdBuyback_footerServiceName___Gcq_",footerServiceDescription:"LcdBuyback_footerServiceDescription__MMtQY",footerMiddle:"LcdBuyback_footerMiddle__ATlye",footerColumn:"LcdBuyback_footerColumn__WBdfS",footerLinks:"LcdBuyback_footerLinks__cmWGs",footerBottom:"LcdBuyback_footerBottom__EkPCA",footerCopyright:"LcdBuyback_footerCopyright__g59np",footerPaymentMethods:"LcdBuyback_footerPaymentMethods__4ixjl",footerPaymentIcon:"LcdBuyback_footerPaymentIcon__xDwSw",navigation:"LcdBuyback_navigation__2NaaQ"}},11163:function(e,o,r){e.exports=r(43079)}},function(e){e.O(0,[1664,5675,6029,2888,9774,179],function(){return e(e.s=38301)}),_N_E=e.O()}]);