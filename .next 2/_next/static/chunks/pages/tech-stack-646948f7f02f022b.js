(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8566],{49574:function(e,s,c){(window.__NEXT_P=window.__NEXT_P||[]).push(["/tech-stack",function(){return c(72467)}])},72467:function(e,s,c){"use strict";c.r(s),c.d(s,{default:function(){return h}});var a=c(85893);c(67294);var i=c(9008),r=c.n(i),t=c(41664),n=c.n(t),l=c(54534),d=c.n(l);function h(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(r(),{children:[(0,a.jsx)("title",{children:"Our Tech Stack | MDTS - Midas Technical Solutions"}),(0,a.jsx)("meta",{name:"description",content:"Learn about the technologies we use to build and maintain our e-commerce platform."})]}),(0,a.jsxs)("div",{className:d().container,children:[(0,a.jsxs)("div",{className:d().header,children:[(0,a.jsx)("h1",{children:"Our Technology Stack"}),(0,a.jsx)("p",{children:"The cutting-edge technologies that power our e-commerce platform"})]}),(0,a.jsxs)("section",{id:"frontend",className:d().section,children:[(0,a.jsx)("h2",{children:"Frontend Technologies"}),(0,a.jsxs)("div",{className:d().techGrid,children:[(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/react.svg",alt:"React"})}),(0,a.jsx)("h3",{children:"React"}),(0,a.jsx)("p",{children:"A JavaScript library for building user interfaces with reusable components"})]}),(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/nextjs.svg",alt:"Next.js"})}),(0,a.jsx)("h3",{children:"Next.js"}),(0,a.jsx)("p",{children:"React framework for server-side rendering and static site generation"})]}),(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/css.svg",alt:"CSS Modules"})}),(0,a.jsx)("h3",{children:"CSS Modules"}),(0,a.jsx)("p",{children:"Locally scoped CSS for component-based styling"})]})]})]}),(0,a.jsxs)("section",{id:"backend",className:d().section,children:[(0,a.jsx)("h2",{children:"Backend Technologies"}),(0,a.jsxs)("div",{className:d().techGrid,children:[(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/nodejs.svg",alt:"Node.js"})}),(0,a.jsx)("h3",{children:"Node.js"}),(0,a.jsx)("p",{children:"JavaScript runtime for building scalable server-side applications"})]}),(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/express.svg",alt:"Express.js"})}),(0,a.jsx)("h3",{children:"Express.js"}),(0,a.jsx)("p",{children:"Web application framework for Node.js"})]}),(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/nextauth.png",alt:"NextAuth.js"})}),(0,a.jsx)("h3",{children:"NextAuth.js"}),(0,a.jsx)("p",{children:"Authentication for Next.js applications"})]})]})]}),(0,a.jsxs)("section",{id:"database",className:d().section,children:[(0,a.jsx)("h2",{children:"Database Solutions"}),(0,a.jsxs)("div",{className:d().techGrid,children:[(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/postgresql.svg",alt:"PostgreSQL"})}),(0,a.jsx)("h3",{children:"PostgreSQL"}),(0,a.jsx)("p",{children:"Advanced open-source relational database"})]}),(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/supabase.svg",alt:"Supabase"})}),(0,a.jsx)("h3",{children:"Supabase"}),(0,a.jsx)("p",{children:"Open-source Firebase alternative with PostgreSQL"})]}),(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/prisma.svg",alt:"Prisma"})}),(0,a.jsx)("h3",{children:"Prisma"}),(0,a.jsx)("p",{children:"Next-generation ORM for Node.js and TypeScript"})]})]})]}),(0,a.jsxs)("section",{id:"cloud",className:d().section,children:[(0,a.jsx)("h2",{children:"Cloud Infrastructure"}),(0,a.jsxs)("div",{className:d().techGrid,children:[(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/vercel.svg",alt:"Vercel"})}),(0,a.jsx)("h3",{children:"Vercel"}),(0,a.jsx)("p",{children:"Platform for frontend frameworks and static sites"})]}),(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/aws.svg",alt:"AWS"})}),(0,a.jsx)("h3",{children:"AWS"}),(0,a.jsx)("p",{children:"Comprehensive cloud computing platform"})]}),(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/cloudflare.svg",alt:"Cloudflare"})}),(0,a.jsx)("h3",{children:"Cloudflare"}),(0,a.jsx)("p",{children:"Content delivery network and DDoS protection"})]})]})]}),(0,a.jsxs)("section",{id:"security",className:d().section,children:[(0,a.jsx)("h2",{children:"Security Measures"}),(0,a.jsxs)("div",{className:d().techGrid,children:[(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/cloudflare.svg",alt:"Cloudflare WAF"})}),(0,a.jsx)("h3",{children:"Cloudflare WAF"}),(0,a.jsx)("p",{children:"Web Application Firewall for protection against attacks"})]}),(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/duo.svg",alt:"Duo Security"})}),(0,a.jsx)("h3",{children:"Duo Security"}),(0,a.jsx)("p",{children:"Two-factor authentication provider"})]}),(0,a.jsxs)("div",{className:d().techCard,children:[(0,a.jsx)("div",{className:d().techIcon,children:(0,a.jsx)("img",{src:"/images/tech/stripe.svg",alt:"Stripe"})}),(0,a.jsx)("h3",{children:"Stripe"}),(0,a.jsx)("p",{children:"Secure payment processing infrastructure"})]})]})]}),(0,a.jsxs)("div",{className:d().cta,children:[(0,a.jsx)("h2",{children:"Want to learn more about our technology?"}),(0,a.jsx)("p",{children:"Contact our development team for more information about our tech stack and implementation."}),(0,a.jsx)(n(),{href:"/contact",className:d().ctaButton,children:"Contact Us"})]})]})]})}},54534:function(e){e.exports={container:"TechStack_container__CnXyx",header:"TechStack_header__8KDf2",section:"TechStack_section__nUizV",techGrid:"TechStack_techGrid__scZMm",techCard:"TechStack_techCard__7yris",techIcon:"TechStack_techIcon__JGN8e",cta:"TechStack_cta__ZQ0Ej",ctaButton:"TechStack_ctaButton__lCR5v"}}},function(e){e.O(0,[1664,2888,9774,179],function(){return e(e.s=49574)}),_N_E=e.O()}]);