(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3713],{87577:function(n,e,c){(window.__NEXT_P=window.__NEXT_P||[]).push(["/checkout/cancel",function(){return c(55924)}])},55924:function(n,e,c){"use strict";c.r(e);var s=c(85893),r=c(67294),i=c(41664),t=c.n(i);e.default=r.memo(function(){return(0,s.jsx)("div",{className:"container",children:(0,s.jsxs)("div",{className:"checkout-cancel",children:[(0,s.jsx)("div",{className:"cancel-icon",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"64",height:"64",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,s.jsx)("line",{x1:"15",y1:"9",x2:"9",y2:"15"}),(0,s.jsx)("line",{x1:"9",y1:"9",x2:"15",y2:"15"})]})}),(0,s.jsx)("h1",{children:"Payment Cancelled"}),(0,s.jsx)("p",{children:"Your payment was cancelled. No charges were made."}),(0,s.jsxs)("div",{className:"cancel-actions",children:[(0,s.jsx)(t(),{href:"/cart",className:"btn btn-primary",children:"Return to Cart"}),(0,s.jsx)(t(),{href:"/products",className:"btn btn-secondary",children:"Continue Shopping"})]})]})})})}},function(n){n.O(0,[1664,2888,9774,179],function(){return n(n.s=87577)}),_N_E=n.O()}]);