(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9269],{63057:function(e,s,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/checkout/success",function(){return r(8030)}])},8030:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return _}});var c=r(85893),o=r(67294),n=r(41664),t=r.n(n),u=r(11163),i=r(33299),a=r(9008),d=r.n(a),l=r(21951),h=r.n(l);function _(){let{data:e}=(0,i.useSession)(),s=(0,u.useRouter)(),{order_id:r}=s.query;return(0,o.useEffect)(()=>{if(null==e?void 0:e.user){let e=setTimeout(()=>{s.push("/account?tab=orders")},1e4);return()=>clearTimeout(e)}},[e,s]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)(d(),{children:[(0,c.jsx)("title",{children:"Order Confirmed | MDTS - Midas Technical Solutions"}),(0,c.jsx)("meta",{name:"description",content:"Your order has been confirmed. Thank you for shopping with Midas Technical Solutions."})]}),(0,c.jsx)("div",{className:h().successContainer,children:(0,c.jsxs)("div",{className:h().successCard,children:[(0,c.jsx)("div",{className:h().successIcon,children:(0,c.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"64",height:"64",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,c.jsx)("path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}),(0,c.jsx)("polyline",{points:"22 4 12 14.01 9 11.01"})]})}),(0,c.jsx)("h1",{className:h().successTitle,children:"Order Confirmed!"}),(0,c.jsx)("p",{className:h().successMessage,children:"Thank you for your purchase. Your order has been confirmed and is now being processed."}),r&&(0,c.jsxs)("div",{className:h().orderInfo,children:[(0,c.jsxs)("p",{className:h().orderNumber,children:["Order Number: ",(0,c.jsx)("span",{children:r})]}),(0,c.jsx)("p",{className:h().orderNote,children:"Please save this order number for your records. We've also sent a confirmation email with your order details."})]}),(0,c.jsxs)("div",{className:h().nextSteps,children:[(0,c.jsx)("h2",{children:"What's Next?"}),(0,c.jsxs)("ol",{children:[(0,c.jsx)("li",{children:"You'll receive an order confirmation email shortly."}),(0,c.jsx)("li",{children:"Once your order ships, we'll send you tracking information."}),(0,c.jsx)("li",{children:"Your items should arrive within 3-5 business days."})]})]}),(null==e?void 0:e.user)&&(0,c.jsx)("p",{className:h().redirectMessage,children:"You will be redirected to your orders page in 10 seconds, or you can click the button below."}),(0,c.jsxs)("div",{className:h().actionButtons,children:[(null==e?void 0:e.user)?(0,c.jsx)(t(),{href:"/account?tab=orders",className:h().viewOrderButton,children:"View Order Details"}):(0,c.jsx)(t(),{href:"/auth/register",className:h().viewOrderButton,children:"Create Account"}),(0,c.jsx)(t(),{href:"/",className:h().continueShoppingButton,children:"Continue Shopping"})]}),(0,c.jsx)("div",{className:h().supportInfo,children:(0,c.jsxs)("p",{children:["If you have any questions about your order, please contact our customer support at"," ",(0,c.jsx)("a",{href:"mailto:<EMAIL>",children:"<EMAIL>"})," or call us at"," ",(0,c.jsx)("a",{href:"tel:+***********",children:"+****************"}),"."]})})]})})]})}},21951:function(e){e.exports={successContainer:"CheckoutSuccess_successContainer__IghC6",successCard:"CheckoutSuccess_successCard__cIueV",successIcon:"CheckoutSuccess_successIcon__fBpLV",successTitle:"CheckoutSuccess_successTitle__Q2DvF",successMessage:"CheckoutSuccess_successMessage__pbGIJ",orderInfo:"CheckoutSuccess_orderInfo__PSNbu",orderNumber:"CheckoutSuccess_orderNumber__T9IzF",orderNote:"CheckoutSuccess_orderNote__5gRjR",nextSteps:"CheckoutSuccess_nextSteps__HjMxN",redirectMessage:"CheckoutSuccess_redirectMessage__2FnOr",actionButtons:"CheckoutSuccess_actionButtons__6Gq9m",viewOrderButton:"CheckoutSuccess_viewOrderButton___YEIM",continueShoppingButton:"CheckoutSuccess_continueShoppingButton__aPf2N",supportInfo:"CheckoutSuccess_supportInfo__uTpmr"}},11163:function(e,s,r){e.exports=r(43079)}},function(e){e.O(0,[1664,2888,9774,179],function(){return e(e.s=63057)}),_N_E=e.O()}]);