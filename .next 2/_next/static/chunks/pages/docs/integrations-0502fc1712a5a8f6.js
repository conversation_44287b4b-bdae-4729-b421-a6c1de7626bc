(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8023],{35868:function(e,n,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/docs/integrations",function(){return s(49039)}])},49039:function(e,n,s){"use strict";s.r(n),s.d(n,{default:function(){return h}});var i=s(85893);s(67294);var t=s(9008),o=s.n(t),r=s(41664),a=s.n(r),c=s(56029),l=s(16701),d=s.n(l);function h(){return(0,i.jsxs)(c.Z,{children:[(0,i.jsxs)(o(),{children:[(0,i.jsx)("title",{children:"Integrations | MDTS - Midas Technical Solutions"}),(0,i.jsx)("meta",{name:"description",content:"Learn about the integrations available with MDTS - Midas Technical Solutions."})]}),(0,i.jsx)("main",{className:"main-content",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:d().docsContainer,children:[(0,i.jsxs)("div",{className:d().docsSidebar,children:[(0,i.jsx)("h3",{children:"Documentation"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:(0,i.jsx)(a(),{href:"/docs",children:"Getting Started"})}),(0,i.jsx)("li",{children:(0,i.jsx)(a(),{href:"/docs/api",children:"API Reference"})}),(0,i.jsx)("li",{className:d().active,children:(0,i.jsx)(a(),{href:"/docs/integrations",children:"Integrations"})}),(0,i.jsx)("li",{children:(0,i.jsx)(a(),{href:"/docs/faq",children:"FAQ"})})]})]}),(0,i.jsxs)("div",{className:d().docsContent,children:[(0,i.jsx)("h1",{children:"Integrations"}),(0,i.jsxs)("section",{className:d().section,children:[(0,i.jsx)("h2",{children:"Available Integrations"}),(0,i.jsx)("p",{children:"MDTS offers seamless integration with various platforms and services to enhance your business operations. Below are the key integrations currently available:"})]}),(0,i.jsxs)("section",{className:d().section,children:[(0,i.jsx)("h2",{children:"4seller.com Integration"}),(0,i.jsxs)("div",{className:d().integrationCard,children:[(0,i.jsx)("div",{className:d().integrationLogo,children:(0,i.jsx)("img",{src:"/images/integrations/4seller-logo.png",alt:"4seller.com Logo"})}),(0,i.jsxs)("div",{className:d().integrationContent,children:[(0,i.jsx)("h3",{children:"4seller.com"}),(0,i.jsx)("p",{children:"Integrate your 4seller.com inventory with MDTS to synchronize product listings, inventory levels, and orders. This integration allows for real-time updates between platforms, ensuring consistent inventory management."}),(0,i.jsx)("h4",{children:"Key Features:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Automatic inventory synchronization"}),(0,i.jsx)("li",{children:"Order status updates across platforms"}),(0,i.jsx)("li",{children:"Product information consistency"}),(0,i.jsx)("li",{children:"Centralized inventory management"})]}),(0,i.jsxs)("div",{className:d().integrationActions,children:[(0,i.jsx)(a(),{href:"/account?tab=integrations",className:d().primaryButton,children:"Connect 4seller.com"}),(0,i.jsx)("a",{href:"https://www.4seller.com/",target:"_blank",rel:"noopener noreferrer",className:d().secondaryButton,children:"Learn More"})]})]})]})]}),(0,i.jsxs)("section",{className:d().section,children:[(0,i.jsx)("h2",{children:"n8n.io Integration"}),(0,i.jsxs)("div",{className:d().integrationCard,children:[(0,i.jsx)("div",{className:d().integrationLogo,children:(0,i.jsx)("img",{src:"/images/integrations/n8n-logo.png",alt:"n8n.io Logo"})}),(0,i.jsxs)("div",{className:d().integrationContent,children:[(0,i.jsx)("h3",{children:"n8n.io"}),(0,i.jsx)("p",{children:"Connect MDTS with n8n.io to create powerful automation workflows. n8n is a workflow automation tool that allows you to connect MDTS with hundreds of other applications and services."}),(0,i.jsx)("h4",{children:"Key Features:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Custom workflow automation"}),(0,i.jsx)("li",{children:"Trigger actions based on MDTS events"}),(0,i.jsx)("li",{children:"Connect with hundreds of other services"}),(0,i.jsx)("li",{children:"No-code/low-code automation builder"})]}),(0,i.jsxs)("div",{className:d().integrationActions,children:[(0,i.jsx)(a(),{href:"/account?tab=integrations",className:d().primaryButton,children:"Connect n8n.io"}),(0,i.jsx)("a",{href:"https://n8n.io/",target:"_blank",rel:"noopener noreferrer",className:d().secondaryButton,children:"Learn More"})]})]})]})]}),(0,i.jsxs)("section",{className:d().section,children:[(0,i.jsx)("h2",{children:"Marketplace Integrations"}),(0,i.jsxs)("div",{className:d().integrationGrid,children:[(0,i.jsxs)("div",{className:d().integrationGridItem,children:[(0,i.jsx)("img",{src:"/images/integrations/ebay-logo.png",alt:"eBay Logo"}),(0,i.jsx)("h3",{children:"eBay"}),(0,i.jsx)("p",{children:"Sync your eBay listings with MDTS inventory"}),(0,i.jsx)(a(),{href:"/account?tab=integrations",className:d().gridItemButton,children:"Connect"})]}),(0,i.jsxs)("div",{className:d().integrationGridItem,children:[(0,i.jsx)("img",{src:"/images/integrations/amazon-logo.png",alt:"Amazon Logo"}),(0,i.jsx)("h3",{children:"Amazon"}),(0,i.jsx)("p",{children:"Manage Amazon listings through MDTS"}),(0,i.jsx)(a(),{href:"/account?tab=integrations",className:d().gridItemButton,children:"Connect"})]}),(0,i.jsxs)("div",{className:d().integrationGridItem,children:[(0,i.jsx)("img",{src:"/images/integrations/tiktok-logo.png",alt:"TikTok Shop Logo"}),(0,i.jsx)("h3",{children:"TikTok Shop"}),(0,i.jsx)("p",{children:"Integrate TikTok Shop with MDTS"}),(0,i.jsx)(a(),{href:"/account?tab=integrations",className:d().gridItemButton,children:"Connect"})]})]})]}),(0,i.jsxs)("section",{className:d().section,children:[(0,i.jsx)("h2",{children:"API Integration"}),(0,i.jsx)("p",{children:"For custom integrations, MDTS provides a comprehensive API that allows you to connect your systems directly with our platform. Our API documentation provides all the details you need to build custom integrations."}),(0,i.jsxs)("div",{className:d().apiBox,children:[(0,i.jsx)("h3",{children:"API Documentation"}),(0,i.jsx)("p",{children:"Our API documentation includes detailed information about endpoints, authentication, request/response formats, and example code in various programming languages."}),(0,i.jsx)(a(),{href:"/docs/api",className:d().primaryButton,children:"View API Documentation"})]})]}),(0,i.jsxs)("section",{className:d().section,children:[(0,i.jsx)("h2",{children:"Need Help?"}),(0,i.jsx)("p",{children:"If you need assistance setting up any integration or have questions about how to connect your existing systems with MDTS, our support team is here to help."}),(0,i.jsxs)("div",{className:d().supportBox,children:[(0,i.jsxs)("div",{className:d().supportOption,children:[(0,i.jsx)("h3",{children:"Contact Support"}),(0,i.jsxs)("p",{children:["Email us at ",(0,i.jsx)("a",{href:"mailto:<EMAIL>",children:"<EMAIL>"})]}),(0,i.jsxs)("p",{children:["Call us at ",(0,i.jsx)("a",{href:"tel:+12403510511",children:"+****************"})]})]}),(0,i.jsxs)("div",{className:d().supportOption,children:[(0,i.jsx)("h3",{children:"Schedule a Consultation"}),(0,i.jsx)("p",{children:"Book a call with our integration specialists to discuss your specific needs."}),(0,i.jsx)("a",{href:"https://calendly.com/mdtstech",target:"_blank",rel:"noopener noreferrer",className:d().secondaryButton,children:"Schedule Now"})]})]})]})]})]})})})]})}},16701:function(e){e.exports={docsContainer:"Docs_docsContainer__cmoVA",docsSidebar:"Docs_docsSidebar__II5PC",active:"Docs_active__aBdAU",docsContent:"Docs_docsContent__GRjps",section:"Docs_section__pl4__",integrationCard:"Docs_integrationCard__ceUOY",integrationLogo:"Docs_integrationLogo__A4kt0",integrationContent:"Docs_integrationContent__GirlM",integrationActions:"Docs_integrationActions__rl74L",primaryButton:"Docs_primaryButton__OCL3y",secondaryButton:"Docs_secondaryButton__QEDeq",integrationGrid:"Docs_integrationGrid__pO2NH",integrationGridItem:"Docs_integrationGridItem__Dysoe",gridItemButton:"Docs_gridItemButton__7X5oj",apiBox:"Docs_apiBox__bGxbY",supportBox:"Docs_supportBox__69HCK",supportOption:"Docs_supportOption__2XNOv"}},11163:function(e,n,s){e.exports=s(43079)}},function(e){e.O(0,[1664,5675,6029,2888,9774,179],function(){return e(e.s=35868)}),_N_E=e.O()}]);