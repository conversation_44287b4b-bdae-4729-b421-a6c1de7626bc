(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6535],{47338:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/subscription",function(){return s(58619)}])},58619:function(e,t,s){"use strict";s.r(t);var n=s(85893),i=s(67294),r=s(33299),a=s(11163),l=s(56029),o=s(2859);let c=(0,s(48967).eI)("https://your-supabase-project-url.supabase.co","your-supabase-anon-key"),d=(0,o.J)("pk_test_your_publishable_key"),u=[{id:"price_basic",name:"Basic",description:"Perfect for small repair shops",price:29.99,interval:"month",features:["Access to all basic parts","Standard shipping","Email support","10% discount on tools"]},{id:"price_pro",name:"Professional",description:"Ideal for growing businesses",price:79.99,interval:"month",features:["Access to all parts including premium","Priority shipping","Phone and email support","15% discount on tools","Early access to new products"],popular:!0},{id:"price_enterprise",name:"Enterprise",description:"For large repair operations",price:199.99,interval:"month",features:["Access to all parts including premium and exclusive","Free expedited shipping","Dedicated account manager","25% discount on tools","Early access to new products","Custom bulk pricing","API access"]}];t.default=function(){var e,t,s,o,p,h,m;let{data:x,status:f}=(0,r.useSession)(),g=(0,a.useRouter)(),[b,v]=(0,i.useState)(!0),[j,y]=(0,i.useState)(null),[w,N]=(0,i.useState)("month"),[S,k]=(0,i.useState)(!1),[_,E]=(0,i.useState)(!1);(0,i.useEffect)(()=>{"unauthenticated"===f&&g.push("/auth/signin?callbackUrl=/subscription")},[f,g]),(0,i.useEffect)(()=>{var e;async function t(){var e;if(null==x?void 0:null===(e=x.user)||void 0===e?void 0:e.id)try{let{data:e,error:t}=await c.from("subscriptions").select("*, prices(*, products(*))").eq("user_id",x.user.id).in("status",["trialing","active"]).single();if(t&&"PGRST116"!==t.code)throw t;y(e)}catch(e){console.error("Error fetching subscription:",e)}finally{v(!1)}}(null==x?void 0:null===(e=x.user)||void 0===e?void 0:e.id)?t():v(!1)},[x]);let P=async e=>{k(e);try{let t=await fetch("/api/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({price:e,quantity:1,metadata:{userId:x.user.id}})}),{sessionId:s}=await t.json(),n=await d,{error:i}=await n.redirectToCheckout({sessionId:s});if(i)throw i}catch(e){console.error("Error during checkout:",e),alert("An error occurred during checkout. Please try again.")}finally{k(!1)}},C=async()=>{E(!0);try{let e=await fetch("/api/create-portal-link",{method:"POST"}),{url:t}=await e.json();window.location.href=t}catch(e){console.error("Error creating portal link:",e),alert("An error occurred. Please try again.")}finally{E(!1)}};return"loading"===f||b?(0,n.jsx)(l.Z,{title:"Subscription Plans | MDTS",children:(0,n.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,n.jsxs)("div",{className:"flex justify-center items-center h-64",children:[(0,n.jsx)("div",{className:"loading-spinner"}),(0,n.jsx)("p",{className:"ml-4",children:"Loading subscription information..."})]})})}):"unauthenticated"===f?(0,n.jsx)(l.Z,{title:"Subscription Plans | MDTS",children:(0,n.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)("p",{children:"Please sign in to view subscription plans."})})})}):(0,n.jsx)(l.Z,{title:"Subscription Plans | MDTS",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,n.jsxs)("div",{className:"text-center mb-12",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"MDTS Subscription Plans"}),(0,n.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Choose the perfect plan for your repair business. Unlock premium parts, priority shipping, and exclusive discounts."}),(0,n.jsx)("div",{className:"flex justify-center mt-8 mb-12",children:(0,n.jsxs)("div",{className:"bg-gray-100 p-1 rounded-full",children:[(0,n.jsx)("button",{onClick:()=>N("month"),className:"px-6 py-2 rounded-full ".concat("month"===w?"bg-white shadow-md":"text-gray-700"),children:"Monthly"}),(0,n.jsxs)("button",{onClick:()=>N("year"),className:"px-6 py-2 rounded-full ".concat("year"===w?"bg-white shadow-md":"text-gray-700"),children:["Yearly ",(0,n.jsx)("span",{className:"text-green-500 font-semibold",children:"-20%"})]})]})})]}),j?(0,n.jsxs)("div",{className:"max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8 mb-12",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold",children:"Your Current Subscription"}),(0,n.jsx)("span",{className:"px-4 py-1 bg-green-100 text-green-800 rounded-full text-sm font-semibold",children:"trialing"===j.status?"Trial":"Active"})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold mb-2",children:(null===(t=j.prices)||void 0===t?void 0:null===(e=t.products)||void 0===e?void 0:e.name)||"Subscription Plan"}),(0,n.jsx)("p",{className:"text-gray-600",children:(null===(o=j.prices)||void 0===o?void 0:null===(s=o.products)||void 0===s?void 0:s.description)||"Your active subscription plan"})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("p",{className:"text-gray-600",children:[(0,n.jsx)("span",{className:"font-semibold",children:"Billing Period:"})," ",(null===(p=j.prices)||void 0===p?void 0:p.interval_count)===1?"Monthly":"Yearly"]}),(0,n.jsxs)("p",{className:"text-gray-600",children:[(0,n.jsx)("span",{className:"font-semibold",children:"Next billing date:"})," ",new Date(1e3*j.current_period_end).toLocaleDateString()]})]}),(0,n.jsx)("div",{className:"text-right",children:(0,n.jsxs)("p",{className:"text-2xl font-bold",children:["$",((null===(h=j.prices)||void 0===h?void 0:h.unit_amount)/100).toFixed(2),(0,n.jsxs)("span",{className:"text-sm text-gray-600 font-normal",children:["/",null===(m=j.prices)||void 0===m?void 0:m.interval]})]})})]}),(0,n.jsx)("button",{onClick:C,disabled:_,className:"w-full bg-blue-500 text-white py-3 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 transition",children:_?"Loading...":"Manage Billing"})]}):(0,n.jsx)("div",{className:"grid md:grid-cols-3 gap-8 max-w-6xl mx-auto",children:u.map(e=>(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden ".concat(e.popular?"ring-2 ring-blue-500 transform scale-105":""),children:[e.popular&&(0,n.jsx)("div",{className:"bg-blue-500 text-white text-center py-2 font-semibold",children:"Most Popular"}),(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsx)("h3",{className:"text-2xl font-bold mb-1",children:e.name}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsxs)("span",{className:"text-4xl font-bold",children:["$","month"===w?e.price:(.8*e.price*12).toFixed(2)]}),(0,n.jsxs)("span",{className:"text-gray-600",children:["/","month"===w?"month":"year"]})]}),(0,n.jsx)("button",{onClick:()=>P("month"===w?e.id:"".concat(e.id,"_yearly")),disabled:S===e.id,className:"w-full py-3 rounded-lg mb-6 ".concat(e.popular?"bg-blue-500 text-white hover:bg-blue-600":"bg-gray-100 text-gray-800 hover:bg-gray-200"," focus:outline-none focus:ring-2 focus:ring-blue-500 transition"),children:S===e.id?"Loading...":"Subscribe"}),(0,n.jsx)("ul",{className:"space-y-3",children:e.features.map((e,t)=>(0,n.jsxs)("li",{className:"flex items-start",children:[(0,n.jsx)("svg",{className:"h-5 w-5 text-green-500 mr-2 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})}),(0,n.jsx)("span",{children:e})]},t))})]})]},e.id))}),(0,n.jsxs)("div",{className:"max-w-3xl mx-auto mt-16",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Frequently Asked Questions"}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"What's included in each plan?"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Each plan offers different levels of access to our parts catalog, shipping benefits, and support options. The higher-tier plans include additional perks like discounts on tools, early access to new products, and dedicated support."})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Can I change my plan later?"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Yes, you can upgrade or downgrade your plan at any time. Changes will take effect at the start of your next billing cycle."})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Is there a free trial?"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Yes, all plans come with a 14-day free trial. You can cancel anytime during the trial period and won't be charged."})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"How do I cancel my subscription?"}),(0,n.jsx)("p",{className:"text-gray-600",children:'You can cancel your subscription at any time through the "Manage Billing" option. Your subscription will remain active until the end of your current billing period.'})]})]})]})]})})}},11163:function(e,t,s){e.exports=s(43079)},2859:function(e,t,s){"use strict";s.d(t,{J:function(){return b}});var n,i="basil",r="https://js.stripe.com",a="".concat(r,"/").concat(i,"/stripe.js"),l=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,o=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,c=function(){for(var e=document.querySelectorAll('script[src^="'.concat(r,'"]')),t=0;t<e.length;t++){var s,n=e[t];if(s=n.src,l.test(s)||o.test(s))return n}return null},d=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",s=document.createElement("script");s.src="".concat(a).concat(t);var n=document.head||document.body;if(!n)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(s),s},u=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"7.2.0",startTime:t})},p=null,h=null,m=null,x=function(e,t,s){if(null===e)return null;var n,r=t[0].match(/^pk_test/),a=3===(n=e.version)?"v3":n;r&&a!==i&&console.warn("Stripe.js@".concat(a," was loaded on the page, but @stripe/stripe-js@").concat("7.2.0"," expected Stripe.js@").concat(i,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var l=e.apply(void 0,t);return u(l,s),l},f=!1,g=function(){return n||(n=(null!==p?p:(p=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var s,n=c();n?n&&null!==m&&null!==h&&(n.removeEventListener("load",m),n.removeEventListener("error",h),null===(s=n.parentNode)||void 0===s||s.removeChild(n),n=d(null)):n=d(null),m=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},h=function(e){t(Error("Failed to load Stripe.js",{cause:e}))},n.addEventListener("load",m),n.addEventListener("error",h)}catch(e){t(e);return}})).catch(function(e){return p=null,Promise.reject(e)})).catch(function(e){return n=null,Promise.reject(e)}))};Promise.resolve().then(function(){return g()}).catch(function(e){f||console.warn(e)});var b=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];f=!0;var n=Date.now();return g().then(function(e){return x(e,t,n)})}}},function(e){e.O(0,[1664,5675,8764,8967,6029,2888,9774,179],function(){return e(e.s=47338)}),_N_E=e.O()}]);