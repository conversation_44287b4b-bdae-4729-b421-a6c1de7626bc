(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6449],{91536:function(e,s,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/supabase-test",function(){return n(52819)}])},52819:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return x}});var a=n(85893),t=n(9008),l=n.n(t),r=n(67294),i=n(48967),c=n(34155);let o=c.env.SUPABASE_DATABASE_URL||"https://your-supabase-project-url.supabase.co",d=c.env.PUBLIC_SUPABASE_ANON_KEY||"your-supabase-anon-key";o&&d||console.warn("Supabase URL or key is missing. Make sure to set SUPABASE_DATABASE_URL and PUBLIC_SUPABASE_ANON_KEY environment variables.");let u=(0,i.eI)(o,d,{auth:{persistSession:!0,autoRefreshToken:!0}});function m(){let[e,s]=(0,r.useState)("Checking connection..."),[n,t]=(0,r.useState)(null),[l,i]=(0,r.useState)(null);return(0,r.useEffect)(()=>{(async function(){try{let{data:e,error:n}=await u.rpc("version");if(n)throw n;s("Connected to Supabase"),i(e)}catch(e){console.error("Supabase client connection test failed:",e),s("Failed to connect to Supabase"),t(e.message)}})()},[]),(0,a.jsxs)("div",{className:"p-4 border rounded-lg bg-white shadow-sm",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Supabase Connection Test"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("p",{className:"font-medium",children:"Status:"}),(0,a.jsx)("p",{className:"".concat(e.includes("Failed")?"text-red-500":e.includes("Connected")?"text-green-500":"text-yellow-500"),children:e})]}),n&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("p",{className:"font-medium",children:"Error:"}),(0,a.jsx)("p",{className:"text-red-500",children:n})]}),l&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("p",{className:"font-medium",children:"Supabase Version:"}),(0,a.jsx)("pre",{className:"bg-gray-100 p-2 rounded overflow-x-auto",children:JSON.stringify(l,null,2)})]}),(0,a.jsx)("div",{className:"mt-4 text-sm text-gray-500",children:(0,a.jsx)("p",{children:"Note: Make sure your Supabase environment variables are properly set in Netlify."})})]})}function x(){return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsxs)(l(),{children:[(0,a.jsx)("title",{children:"Supabase Connection Test | MDTS"}),(0,a.jsx)("meta",{name:"description",content:"Testing Supabase connection for MDTS"})]}),(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Supabase Connection Test"}),(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Client-Side Test"}),(0,a.jsx)(m,{})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Server-Side Test"}),(0,a.jsx)(h,{})]})]}),(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Environment Variables"}),(0,a.jsx)(b,{})]})]})}function h(){let[e,s]=(0,r.useState)("Not tested"),[n,t]=(0,r.useState)(null),[l,i]=(0,r.useState)(null),[c,o]=(0,r.useState)(!1),d=async()=>{o(!0),s("Testing..."),t(null),i(null);try{let e=await fetch("/api/supabase/test-connection"),n=await e.json();n.success?(s("Success"),t(n)):(s("Failed"),i(n.error||"Unknown error"))}catch(e){s("Error"),i(e.message)}finally{o(!1)}};return(0,a.jsxs)("div",{className:"p-4 border rounded-lg bg-white shadow-sm",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("p",{className:"font-medium",children:"Status:"}),(0,a.jsx)("p",{className:"".concat("Success"===e?"text-green-500":"Failed"===e||"Error"===e?"text-red-500":"text-gray-500"),children:e})]}),(0,a.jsx)("button",{onClick:d,disabled:c,className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50",children:c?"Testing...":"Test Server Connection"}),l&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("p",{className:"font-medium",children:"Error:"}),(0,a.jsx)("p",{className:"text-red-500",children:l})]}),n&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("p",{className:"font-medium",children:"Result:"}),(0,a.jsx)("pre",{className:"bg-gray-100 p-2 rounded overflow-x-auto",children:JSON.stringify(n,null,2)})]})]})}function b(){return(0,a.jsxs)("div",{className:"p-4 border rounded-lg bg-white shadow-sm",children:[(0,a.jsx)("p",{className:"mb-2",children:"The following environment variables should be set in your Netlify dashboard:"}),(0,a.jsxs)("ul",{className:"list-disc pl-5 space-y-1",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("code",{className:"bg-gray-100 px-1",children:"NEXT_PUBLIC_SUPABASE_URL"})," - Your Supabase project URL"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("code",{className:"bg-gray-100 px-1",children:"NEXT_PUBLIC_SUPABASE_ANON_KEY"})," - Your Supabase anonymous key"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("code",{className:"bg-gray-100 px-1",children:"SUPABASE_DATABASE_URL"})," - Your Supabase database URL (for server-side)"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("code",{className:"bg-gray-100 px-1",children:"SUPABASE_ANON_KEY"})," - Your Supabase anonymous key (for server-side)"]})]}),(0,a.jsx)("p",{className:"mt-4 text-sm text-gray-500",children:"Note: When connecting through the Netlify UI, these variables should be automatically set."})]})}}},function(e){e.O(0,[8764,8967,2888,9774,179],function(){return e(e.s=91536)}),_N_E=e.O()}]);