(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7891],{44621:function(e,r,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/firebase-auth",function(){return t(42638)}])},42638:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return y}});var a=t(85893),n=t(67294),s=t(9008),i=t.n(s),u=t(83977),o=t(2370),l=t(39828),c=t(86650);let d=(0,u.C6)().length?(0,u.Mq)():(0,u.ZF)({apiKey:"your-firebase-api-key",authDomain:"your-project-id.firebaseapp.com",projectId:"your-project-id",storageBucket:"your-project-id.appspot.com",messagingSenderId:"your-messaging-sender-id",appId:"your-app-id",measurementId:"your-measurement-id"}),h=(0,o.v0)(d),m=(0,l.ad)(d);(0,c.cF)(d);let p=async(e,r)=>{try{return{user:(await (0,o.e5)(h,e,r)).user,error:null}}catch(e){return{user:null,error:e.message}}},g=async(e,r,t)=>{try{let a=await (0,o.Xb)(h,e,r);return await (0,l.pl)((0,l.JU)(m,"users",a.user.uid),{...t,email:e,createdAt:(0,l.Bt)(),updatedAt:(0,l.Bt)()}),{user:a.user,error:null}}catch(e){return{user:null,error:e.message}}},_=async()=>{try{return await (0,o.w7)(h),{success:!0,error:null}}catch(e){return{success:!1,error:e.message}}};var b=t(70928),x=t.n(b),j=()=>{let[e,r]=(0,n.useState)(""),[t,s]=(0,n.useState)(""),[i,u]=(0,n.useState)(""),[o,l]=(0,n.useState)(!1),[c,d]=(0,n.useState)(null),[h,m]=(0,n.useState)(""),[b,j]=(0,n.useState)(!1),f=async a=>{a.preventDefault(),m(""),j(!0);try{if(o){let{user:r,error:a}=await g(e,t,{displayName:i});if(a)throw Error(a);d(r)}else{let{user:r,error:a}=await p(e,t);if(a)throw Error(a);d(r)}r(""),s(""),u("")}catch(e){m(e.message)}finally{j(!1)}},y=async()=>{j(!0);try{let{success:e,error:r}=await _();if(r)throw Error(r);e&&d(null)}catch(e){m(e.message)}finally{j(!1)}};return(0,a.jsx)("div",{className:x().container,children:c?(0,a.jsxs)("div",{className:x().userInfo,children:[(0,a.jsxs)("h2",{children:["Welcome, ",c.displayName||c.email]}),(0,a.jsxs)("p",{children:["Email: ",c.email]}),(0,a.jsx)("button",{className:x().button,onClick:y,disabled:b,children:b?"Signing Out...":"Sign Out"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h2",{children:o?"Create an Account":"Sign In"}),h&&(0,a.jsx)("p",{className:x().error,children:h}),(0,a.jsxs)("form",{onSubmit:f,className:x().form,children:[o&&(0,a.jsxs)("div",{className:x().formGroup,children:[(0,a.jsx)("label",{htmlFor:"name",children:"Name"}),(0,a.jsx)("input",{type:"text",id:"name",value:i,onChange:e=>u(e.target.value),required:o})]}),(0,a.jsxs)("div",{className:x().formGroup,children:[(0,a.jsx)("label",{htmlFor:"email",children:"Email"}),(0,a.jsx)("input",{type:"email",id:"email",value:e,onChange:e=>r(e.target.value),required:!0})]}),(0,a.jsxs)("div",{className:x().formGroup,children:[(0,a.jsx)("label",{htmlFor:"password",children:"Password"}),(0,a.jsx)("input",{type:"password",id:"password",value:t,onChange:e=>s(e.target.value),required:!0,minLength:6})]}),(0,a.jsx)("button",{type:"submit",className:x().button,disabled:b,children:b?"Processing...":o?"Sign Up":"Sign In"})]}),(0,a.jsxs)("p",{className:x().toggle,children:[o?"Already have an account?":"Need an account?",(0,a.jsx)("button",{type:"button",className:x().toggleButton,onClick:()=>l(!o),children:o?"Sign In":"Sign Up"})]})]})})},f=t(56029),y=()=>(0,a.jsxs)("div",{children:[(0,a.jsxs)(i(),{children:[(0,a.jsx)("title",{children:"Firebase Authentication | MDTS Tech"}),(0,a.jsx)("meta",{name:"description",content:"Sign in or create an account using Firebase Authentication"})]}),(0,a.jsx)(f.Z,{title:"Firebase Authentication | MDTS Tech",description:"Sign in or create an account using Firebase Authentication",children:(0,a.jsxs)("div",{style:{maxWidth:"1200px",margin:"0 auto"},children:[(0,a.jsx)("h1",{style:{textAlign:"center",marginBottom:"30px"},children:"Firebase Authentication"}),(0,a.jsx)(j,{})]})})]})},70928:function(e){e.exports={container:"FirebaseAuth_container__aXr_j",form:"FirebaseAuth_form__8r32S",formGroup:"FirebaseAuth_formGroup__lnYeD",button:"FirebaseAuth_button__gnTzF",error:"FirebaseAuth_error__nMP07",toggle:"FirebaseAuth_toggle__l18rb",toggleButton:"FirebaseAuth_toggleButton__XUq28",userInfo:"FirebaseAuth_userInfo__D1RzY"}}},function(e){e.O(0,[5612,2016,1664,5675,8764,8196,6029,2888,9774,179],function(){return e(e.s=44621)}),_N_E=e.O()}]);