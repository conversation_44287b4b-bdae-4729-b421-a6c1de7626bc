(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5122],{53243:function(e,r,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/user/orders",function(){return s(41051)}])},41051:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return o}});var a=s(85893),n=s(67294),d=s(11163),t=s(33299),c=s(41664),i=s.n(c);function o(){let{data:e,status:r}=(0,t.useSession)(),s=(0,d.useRouter)(),[c,o]=(0,n.useState)([]),[l,h]=(0,n.useState)(!0),[u,m]=(0,n.useState)(null),x=async()=>{try{h(!0);let e=await fetch("/api/orders");if(!e.ok)throw Error("Failed to fetch orders");let r=await e.json();if(r.success)o(r.orders);else throw Error(r.message||"Failed to fetch orders")}catch(e){console.error("Error fetching orders:",e),m(e.message)}finally{h(!1)}};(0,n.useEffect)(()=>{"authenticated"===r&&x()},[r]),(0,n.useEffect)(()=>{"unauthenticated"===r&&s.push("/auth/signin?callbackUrl=".concat(encodeURIComponent("/user/orders")))},[r,s]);let j=e=>new Date(e).toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric"});return"loading"===r||l?(0,a.jsxs)("div",{className:"container",children:[(0,a.jsx)("h1",{children:"My Orders"}),(0,a.jsx)("p",{children:"Loading orders..."})]}):u?(0,a.jsxs)("div",{className:"container",children:[(0,a.jsx)("h1",{children:"My Orders"}),(0,a.jsxs)("div",{className:"error-message",children:[(0,a.jsx)("p",{children:u}),(0,a.jsx)("button",{onClick:x,children:"Try Again"})]})]}):(0,a.jsxs)("div",{className:"container",children:[(0,a.jsx)("h1",{children:"My Orders"}),0===c.length?(0,a.jsxs)("div",{className:"empty-orders",children:[(0,a.jsx)("p",{children:"You haven't placed any orders yet."}),(0,a.jsx)(i(),{href:"/products",className:"btn btn-primary",children:"Start Shopping"})]}):(0,a.jsx)("div",{className:"orders-list",children:c.map(e=>(0,a.jsxs)("div",{className:"order-card",children:[(0,a.jsxs)("div",{className:"order-card-header",children:[(0,a.jsxs)("div",{className:"order-card-info",children:[(0,a.jsxs)("h3",{children:["Order #",e.order_number]}),(0,a.jsxs)("p",{children:["Placed on ",j(e.created_at)]})]}),(0,a.jsx)("div",{className:"order-card-status",children:(0,a.jsx)("span",{className:"status-badge status-".concat(e.status),children:e.status.toUpperCase()})})]}),(0,a.jsx)("div",{className:"order-card-body",children:(0,a.jsxs)("div",{className:"order-card-total",children:[(0,a.jsx)("span",{children:"Total:"}),(0,a.jsxs)("span",{children:["$",parseFloat(e.total_amount).toFixed(2)]})]})}),(0,a.jsx)("div",{className:"order-card-footer",children:(0,a.jsx)(i(),{href:"/orders/".concat(e.order_number),className:"btn btn-secondary",children:"View Order"})})]},e.id))})]})}},11163:function(e,r,s){e.exports=s(43079)}},function(e){e.O(0,[1664,2888,9774,179],function(){return e(e.s=53243)}),_N_E=e.O()}]);