(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8872],{99657:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/user/profile",function(){return a(89436)}])},89436:function(e,s,a){"use strict";a.r(s),a.d(s,{__N_SSP:function(){return f},default:function(){return x}});var t=a(85893),r=a(67294);a(25675);var n=a(33299),o=a(11163),i=a(9008),d=a.n(i),l=a(41664),c=a.n(l),u=a(91435),h=a.n(u),_=()=>{var e;let{data:s}=(0,n.useSession)(),[a,o]=(0,r.useState)({enabled:!1,preferred_method:null,email_enabled:!1,sms_enabled:!1,duo_enabled:!1}),[i,d]=(0,r.useState)(!0),[l,u]=(0,r.useState)(""),[_,m]=(0,r.useState)(""),[p,f]=(0,r.useState)(""),[x,j]=(0,r.useState)([]),[w,v]=(0,r.useState)(!1);(0,r.useEffect)(()=>{var e;(null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.id)&&(g(),b())},[s]);let g=async()=>{try{d(!0);let e=await fetch("/api/user/2fa/settings");if(e.ok){let s=await e.json();o(s.settings)}else{let s=await e.json();u(s.message||"Failed to fetch 2FA settings")}}catch(e){u("An error occurred while fetching 2FA settings"),console.error(e)}finally{d(!1)}},b=async()=>{try{let e=await fetch("/api/user/profile");if(e.ok){let s=await e.json();f(s.user.phone_number||"")}}catch(e){console.error("Error fetching user phone number:",e)}},N=async()=>{try{if(u(""),m(""),!a.enabled&&!a.email_enabled&&!a.sms_enabled&&!a.duo_enabled){u("Please enable at least one verification method");return}let e=await fetch("/api/user/2fa/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...a,enabled:!a.enabled})});if(e.ok){let s=await e.json();o(s.settings),m(a.enabled?"2FA has been disabled":"2FA has been enabled"),a.enabled||F()}else{let s=await e.json();u(s.message||"Failed to update 2FA settings")}}catch(e){u("An error occurred while updating 2FA settings"),console.error(e)}},y=async e=>{try{if(u(""),m(""),"sms"===e&&!a.sms_enabled&&!p){u("Please add a phone number in your profile before enabling SMS verification");return}let s={...a,["".concat(e,"_enabled")]:!a["".concat(e,"_enabled")]};a.preferred_method!==e||s["".concat(e,"_enabled")]||(s.preferred_method=null),a.email_enabled||a.sms_enabled||a.duo_enabled||!s["".concat(e,"_enabled")]||(s.preferred_method=e);let t=await fetch("/api/user/2fa/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(t.ok){let a=await t.json();o(a.settings),m("".concat(e.toUpperCase()," verification has been ").concat(s["".concat(e,"_enabled")]?"enabled":"disabled"))}else{let s=await t.json();u(s.message||"Failed to update ".concat(e," settings"))}}catch(s){u("An error occurred while updating ".concat(e," settings")),console.error(s)}},C=async e=>{try{if(u(""),m(""),!a["".concat(e,"_enabled")]){u("".concat(e.toUpperCase()," verification is not enabled"));return}let s=await fetch("/api/user/2fa/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...a,preferred_method:e})});if(s.ok){let a=await s.json();o(a.settings),m("".concat(e.toUpperCase()," is now your preferred verification method"))}else{let e=await s.json();u(e.message||"Failed to update preferred method")}}catch(e){u("An error occurred while updating preferred method"),console.error(e)}},F=async()=>{try{u(""),m("");let e=await fetch("/api/user/2fa/backup-codes",{method:"POST"});if(e.ok){let s=await e.json();j(s.codes),v(!0),m("Backup codes generated successfully")}else{let s=await e.json();u(s.message||"Failed to generate backup codes")}}catch(e){u("An error occurred while generating backup codes"),console.error(e)}},A=async e=>{try{u(""),m("");let s=await fetch("/api/user/2fa/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({method:e})});if(s.ok)m("Test verification sent to your ".concat("email"===e?"email":"phone"));else{let a=await s.json();u(a.message||"Failed to send test ".concat(e," verification"))}}catch(s){u("An error occurred while sending test ".concat(e," verification")),console.error(s)}};return i?(0,t.jsxs)("div",{className:h().loading,children:[(0,t.jsx)("div",{className:h().spinner}),(0,t.jsx)("p",{children:"Loading 2FA settings..."})]}):(0,t.jsxs)("div",{className:h().twoFactorSetup,children:[(0,t.jsx)("h2",{children:"Two-Factor Authentication"}),(0,t.jsx)("p",{className:h().description,children:"Two-factor authentication adds an extra layer of security to your account by requiring a verification code in addition to your password."}),l&&(0,t.jsx)("div",{className:h().error,children:l}),_&&(0,t.jsx)("div",{className:h().success,children:_}),(0,t.jsx)("div",{className:h().toggleContainer,children:(0,t.jsxs)("label",{className:h().toggle,children:[(0,t.jsx)("span",{children:"Enable Two-Factor Authentication"}),(0,t.jsxs)("div",{className:h().toggleSwitch,children:[(0,t.jsx)("input",{type:"checkbox",checked:a.enabled,onChange:N}),(0,t.jsx)("span",{className:h().slider})]})]})}),(0,t.jsxs)("div",{className:h().methodsContainer,children:[(0,t.jsx)("h3",{children:"Verification Methods"}),(0,t.jsx)("p",{children:"Choose one or more verification methods:"}),(0,t.jsxs)("div",{className:h().method,children:[(0,t.jsxs)("label",{className:h().toggle,children:[(0,t.jsx)("span",{children:"Email Verification"}),(0,t.jsxs)("div",{className:h().toggleSwitch,children:[(0,t.jsx)("input",{type:"checkbox",checked:a.email_enabled,onChange:()=>y("email")}),(0,t.jsx)("span",{className:h().slider})]})]}),(0,t.jsxs)("p",{className:h().methodDescription,children:["Receive verification codes via email at ",null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.email]}),a.email_enabled&&(0,t.jsx)("button",{className:h().testButton,onClick:()=>A("email"),children:"Send Test Email"})]}),(0,t.jsxs)("div",{className:h().method,children:[(0,t.jsxs)("label",{className:h().toggle,children:[(0,t.jsx)("span",{children:"SMS Verification"}),(0,t.jsxs)("div",{className:h().toggleSwitch,children:[(0,t.jsx)("input",{type:"checkbox",checked:a.sms_enabled,onChange:()=>y("sms")}),(0,t.jsx)("span",{className:h().slider})]})]}),(0,t.jsxs)("p",{className:h().methodDescription,children:["Receive verification codes via SMS at ",p||"No phone number added",!p&&(0,t.jsx)(c(),{href:"/user/profile",className:h().addPhoneLink,children:"Add phone number"})]}),a.sms_enabled&&(0,t.jsx)("button",{className:h().testButton,onClick:()=>A("sms"),children:"Send Test SMS"})]}),(0,t.jsxs)("div",{className:h().method,children:[(0,t.jsxs)("label",{className:h().toggle,children:[(0,t.jsx)("span",{children:"DUO Security"}),(0,t.jsxs)("div",{className:h().toggleSwitch,children:[(0,t.jsx)("input",{type:"checkbox",checked:a.duo_enabled,onChange:()=>y("duo")}),(0,t.jsx)("span",{className:h().slider})]})]}),(0,t.jsx)("p",{className:h().methodDescription,children:"Use DUO Security mobile app for push notifications"}),a.duo_enabled&&(0,t.jsx)("a",{href:"https://duo.com/product/multi-factor-authentication-mfa/duo-mobile-app",target:"_blank",rel:"noopener noreferrer",className:h().duoLink,children:"Download DUO Mobile App"})]})]}),(a.email_enabled||a.sms_enabled||a.duo_enabled)&&(0,t.jsxs)("div",{className:h().preferredMethod,children:[(0,t.jsx)("h3",{children:"Preferred Method"}),(0,t.jsx)("p",{children:"Select your preferred verification method:"}),(0,t.jsxs)("div",{className:h().radioGroup,children:[a.email_enabled&&(0,t.jsxs)("label",{className:h().radio,children:[(0,t.jsx)("input",{type:"radio",name:"preferred_method",value:"email",checked:"email"===a.preferred_method,onChange:()=>C("email")}),(0,t.jsx)("span",{children:"Email"})]}),a.sms_enabled&&(0,t.jsxs)("label",{className:h().radio,children:[(0,t.jsx)("input",{type:"radio",name:"preferred_method",value:"sms",checked:"sms"===a.preferred_method,onChange:()=>C("sms")}),(0,t.jsx)("span",{children:"SMS"})]}),a.duo_enabled&&(0,t.jsxs)("label",{className:h().radio,children:[(0,t.jsx)("input",{type:"radio",name:"preferred_method",value:"duo",checked:"duo"===a.preferred_method,onChange:()=>C("duo")}),(0,t.jsx)("span",{children:"DUO Security"})]})]})]}),a.enabled&&(0,t.jsxs)("div",{className:h().backupCodes,children:[(0,t.jsx)("h3",{children:"Backup Codes"}),(0,t.jsx)("p",{children:"Backup codes can be used to access your account if you cannot receive verification codes. Each code can only be used once."}),(0,t.jsx)("button",{className:h().generateButton,onClick:F,children:"Generate New Backup Codes"}),w&&x.length>0&&(0,t.jsxs)("div",{className:h().codesContainer,children:[(0,t.jsx)("p",{className:h().warning,children:"Save these backup codes in a secure location. They will not be shown again!"}),(0,t.jsx)("div",{className:h().codes,children:x.map((e,s)=>(0,t.jsx)("div",{className:h().code,children:e},s))}),(0,t.jsx)("button",{className:h().printButton,onClick:()=>window.print(),children:"Print Backup Codes"}),(0,t.jsx)("button",{className:h().closeButton,onClick:()=>v(!1),children:"I've Saved These Codes"})]})]})]})},m=a(97961),p=a.n(m),f=!0;function x(){let{data:e,status:s}=(0,n.useSession)(),[a,i]=(0,r.useState)("profile"),[l,u]=(0,r.useState)(!0),[h,m]=(0,r.useState)(!1),[f,x]=(0,r.useState)(""),[j,w]=(0,r.useState)(""),[v,g]=(0,r.useState)({first_name:"",last_name:"",email:"",phone_number:"",image:""}),[b,N]=(0,r.useState)({current_password:"",new_password:"",confirm_password:""}),[y,C]=(0,r.useState)([]),[F,A]=(0,r.useState)({address_line1:"",address_line2:"",city:"",state:"",postal_code:"",country:"US",is_default:!1}),[k,P]=(0,r.useState)(null),S=(0,o.useRouter)();async function T(){try{if(u(!0),"profile"===a)try{let e=await fetch("/api/user/profile");if(!e.ok)throw Error("Failed to fetch user profile");let s=await e.json();g(s.user)}catch(a){console.warn("Falling back to mock profile data:",a);let e=await fetch("/api/user/mock-profile"),s=await e.json();g(s.user)}else if("addresses"===a)try{let e=await fetch("/api/user/addresses");if(!e.ok)throw Error("Failed to fetch user addresses");let s=await e.json();C(s.addresses)}catch(e){console.warn("Using mock address data:",e),C([{id:1,address_line1:"123 Main St",address_line2:"Apt 4B",city:"Vienna",state:"VA",postal_code:"22182",country:"US",is_default:!0},{id:2,address_line1:"456 Oak Ave",address_line2:"",city:"Arlington",state:"VA",postal_code:"22201",country:"US",is_default:!1}])}u(!1)}catch(e){console.error("Error fetching user data:",e),w("Failed to load user data. Please try again."),u(!1)}}(0,r.useEffect)(()=>{"unauthenticated"===s?S.replace("/auth/signin?callbackUrl=/user/profile"):"authenticated"===s&&T()},[s,S,a]);let U=e=>{let{name:s,value:a}=e.target;g({...v,[s]:a})},B=e=>{let{name:s,value:a}=e.target;N({...b,[s]:a})},E=e=>{let{name:s,value:a,type:t,checked:r}=e.target;A({...F,[s]:"checkbox"===t?r:a})},L=async e=>{e.preventDefault();try{if(m(!0),x(""),w(""),!(await fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(v)})).ok)throw Error("Failed to update profile");x("Profile updated successfully!"),m(!1)}catch(e){console.error("Error updating profile:",e),w("Failed to update profile. Please try again."),m(!1)}},G=async e=>{e.preventDefault();try{if(b.new_password!==b.confirm_password){w("New passwords do not match");return}m(!0),x(""),w("");let e=await fetch("/api/user/change-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({currentPassword:b.current_password,newPassword:b.new_password})});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to change password")}x("Password changed successfully!"),N({current_password:"",new_password:"",confirm_password:""}),m(!1)}catch(e){console.error("Error changing password:",e),w(e.message||"Failed to change password. Please try again."),m(!1)}},O=async e=>{e.preventDefault();try{if(m(!0),x(""),w(""),!(await fetch(k?"/api/user/addresses/".concat(k):"/api/user/addresses",{method:k?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(F)})).ok)throw Error("Failed to save address");x("Address saved successfully!"),A({address_line1:"",address_line2:"",city:"",state:"",postal_code:"",country:"US",is_default:!1}),P(null),m(!1),T()}catch(e){console.error("Error saving address:",e),w("Failed to save address. Please try again."),m(!1)}},D=e=>{A({address_line1:e.address_line1,address_line2:e.address_line2||"",city:e.city,state:e.state,postal_code:e.postal_code,country:e.country,is_default:e.is_default}),P(e.id)},M=async e=>{if(window.confirm("Are you sure you want to delete this address?"))try{if(!(await fetch("/api/user/addresses/".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete address");x("Address deleted successfully!"),T()}catch(e){console.error("Error deleting address:",e),w("Failed to delete address. Please try again.")}},I=async e=>{try{if(!(await fetch("/api/user/addresses/".concat(e,"/default"),{method:"PUT"})).ok)throw Error("Failed to set default address");x("Default address updated successfully!"),T()}catch(e){console.error("Error setting default address:",e),w("Failed to set default address. Please try again.")}};return"loading"===s||l?(0,t.jsxs)("div",{className:p().container,children:[(0,t.jsx)(d(),{children:(0,t.jsx)("title",{children:"Your Profile | MDTS Tech"})}),(0,t.jsxs)("div",{className:p().loading,children:[(0,t.jsx)("div",{className:p().spinner}),(0,t.jsx)("p",{children:"Loading your profile..."})]})]}):(0,t.jsxs)("div",{className:p().container,children:[(0,t.jsx)(d(),{children:(0,t.jsx)("title",{children:"Your Profile | MDTS Tech"})}),(0,t.jsxs)("div",{className:p().header,children:[(0,t.jsx)("h1",{children:"Your Account"}),(0,t.jsx)(c(),{href:"/",className:p().backLink,children:"Back to Store"})]}),f&&(0,t.jsx)("div",{className:p().successMessage,children:f}),j&&(0,t.jsx)("div",{className:p().errorMessage,children:j}),(0,t.jsxs)("div",{className:p().content,children:[(0,t.jsxs)("div",{className:p().sidebar,children:[(0,t.jsxs)("div",{className:p().userInfo,children:[(0,t.jsx)("div",{className:p().avatar,children:v.image?(0,t.jsx)("img",{src:v.image,alt:"".concat(v.first_name," ").concat(v.last_name)}):(0,t.jsxs)("div",{className:p().initials,children:[v.first_name&&v.first_name[0],v.last_name&&v.last_name[0]]})}),(0,t.jsxs)("div",{className:p().userName,children:[v.first_name," ",v.last_name]}),(0,t.jsx)("div",{className:p().userEmail,children:v.email})]}),(0,t.jsxs)("nav",{className:p().nav,children:[(0,t.jsx)("button",{className:"".concat(p().navItem," ").concat("profile"===a?p().active:""),onClick:()=>i("profile"),children:"Profile Information"}),(0,t.jsx)("button",{className:"".concat(p().navItem," ").concat("addresses"===a?p().active:""),onClick:()=>i("addresses"),children:"Addresses"}),(0,t.jsx)("button",{className:"".concat(p().navItem," ").concat("security"===a?p().active:""),onClick:()=>i("security"),children:"Security"}),(0,t.jsx)("button",{className:"".concat(p().navItem," ").concat("orders"===a?p().active:""),onClick:()=>i("orders"),children:"Orders"})]})]}),(0,t.jsxs)("div",{className:p().mainContent,children:["profile"===a&&(0,t.jsxs)("div",{className:p().tabContent,children:[(0,t.jsx)("h2",{children:"Profile Information"}),(0,t.jsxs)("form",{onSubmit:L,className:p().form,children:[(0,t.jsxs)("div",{className:p().formRow,children:[(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"first_name",children:"First Name"}),(0,t.jsx)("input",{type:"text",id:"first_name",name:"first_name",value:v.first_name||"",onChange:U,className:p().input,required:!0})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"last_name",children:"Last Name"}),(0,t.jsx)("input",{type:"text",id:"last_name",name:"last_name",value:v.last_name||"",onChange:U,className:p().input,required:!0})]})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"email",children:"Email Address"}),(0,t.jsx)("input",{type:"email",id:"email",name:"email",value:v.email||"",onChange:U,className:p().input,required:!0,disabled:!0}),(0,t.jsx)("p",{className:p().helpText,children:"Email address cannot be changed"})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"phone_number",children:"Phone Number"}),(0,t.jsx)("input",{type:"tel",id:"phone_number",name:"phone_number",value:v.phone_number||"",onChange:U,className:p().input,placeholder:"e.g. +****************"})]}),(0,t.jsx)("div",{className:p().formActions,children:(0,t.jsx)("button",{type:"submit",className:p().saveButton,disabled:h,children:h?"Saving...":"Save Changes"})})]}),(0,t.jsx)("div",{className:p().divider}),(0,t.jsx)("h2",{children:"Change Password"}),(0,t.jsxs)("form",{onSubmit:G,className:p().form,children:[(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"current_password",children:"Current Password"}),(0,t.jsx)("input",{type:"password",id:"current_password",name:"current_password",value:b.current_password,onChange:B,className:p().input,required:!0})]}),(0,t.jsxs)("div",{className:p().formRow,children:[(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"new_password",children:"New Password"}),(0,t.jsx)("input",{type:"password",id:"new_password",name:"new_password",value:b.new_password,onChange:B,className:p().input,required:!0,minLength:8})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"confirm_password",children:"Confirm New Password"}),(0,t.jsx)("input",{type:"password",id:"confirm_password",name:"confirm_password",value:b.confirm_password,onChange:B,className:p().input,required:!0,minLength:8})]})]}),(0,t.jsx)("div",{className:p().formActions,children:(0,t.jsx)("button",{type:"submit",className:p().saveButton,disabled:h,children:h?"Changing...":"Change Password"})})]})]}),"addresses"===a&&(0,t.jsxs)("div",{className:p().tabContent,children:[(0,t.jsx)("h2",{children:"Your Addresses"}),(0,t.jsx)("div",{className:p().addressList,children:y.length>0?y.map(e=>(0,t.jsxs)("div",{className:p().addressCard,children:[e.is_default&&(0,t.jsx)("div",{className:p().defaultBadge,children:"Default"}),(0,t.jsxs)("div",{className:p().addressDetails,children:[(0,t.jsxs)("p",{className:p().addressName,children:[v.first_name," ",v.last_name]}),(0,t.jsx)("p",{children:e.address_line1}),e.address_line2&&(0,t.jsx)("p",{children:e.address_line2}),(0,t.jsxs)("p",{children:[e.city,", ",e.state," ",e.postal_code]}),(0,t.jsx)("p",{children:e.country})]}),(0,t.jsxs)("div",{className:p().addressActions,children:[(0,t.jsx)("button",{onClick:()=>D(e),className:p().editButton,children:"Edit"}),(0,t.jsx)("button",{onClick:()=>M(e.id),className:p().deleteButton,children:"Delete"}),!e.is_default&&(0,t.jsx)("button",{onClick:()=>I(e.id),className:p().defaultButton,children:"Set as Default"})]})]},e.id)):(0,t.jsx)("p",{className:p().noAddresses,children:"You haven't added any addresses yet."})}),(0,t.jsx)("div",{className:p().divider}),(0,t.jsx)("h2",{children:k?"Edit Address":"Add New Address"}),(0,t.jsxs)("form",{onSubmit:O,className:p().form,children:[(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"address_line1",children:"Address Line 1"}),(0,t.jsx)("input",{type:"text",id:"address_line1",name:"address_line1",value:F.address_line1,onChange:E,className:p().input,required:!0,placeholder:"Street address, P.O. box, company name"})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"address_line2",children:"Address Line 2 (Optional)"}),(0,t.jsx)("input",{type:"text",id:"address_line2",name:"address_line2",value:F.address_line2,onChange:E,className:p().input,placeholder:"Apartment, suite, unit, building, floor, etc."})]}),(0,t.jsxs)("div",{className:p().formRow,children:[(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"city",children:"City"}),(0,t.jsx)("input",{type:"text",id:"city",name:"city",value:F.city,onChange:E,className:p().input,required:!0})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"state",children:"State / Province"}),(0,t.jsx)("input",{type:"text",id:"state",name:"state",value:F.state,onChange:E,className:p().input,required:!0})]})]}),(0,t.jsxs)("div",{className:p().formRow,children:[(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"postal_code",children:"Postal Code"}),(0,t.jsx)("input",{type:"text",id:"postal_code",name:"postal_code",value:F.postal_code,onChange:E,className:p().input,required:!0})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"country",children:"Country"}),(0,t.jsxs)("select",{id:"country",name:"country",value:F.country,onChange:E,className:p().select,required:!0,children:[(0,t.jsx)("option",{value:"US",children:"United States"}),(0,t.jsx)("option",{value:"CA",children:"Canada"}),(0,t.jsx)("option",{value:"GB",children:"United Kingdom"}),(0,t.jsx)("option",{value:"AU",children:"Australia"}),(0,t.jsx)("option",{value:"DE",children:"Germany"}),(0,t.jsx)("option",{value:"FR",children:"France"}),(0,t.jsx)("option",{value:"JP",children:"Japan"}),(0,t.jsx)("option",{value:"CN",children:"China"}),(0,t.jsx)("option",{value:"IN",children:"India"}),(0,t.jsx)("option",{value:"BR",children:"Brazil"}),(0,t.jsx)("option",{value:"MX",children:"Mexico"})]})]})]}),(0,t.jsx)("div",{className:p().formGroup,children:(0,t.jsxs)("label",{className:p().checkboxLabel,children:[(0,t.jsx)("input",{type:"checkbox",name:"is_default",checked:F.is_default,onChange:E}),"Set as default address"]})}),(0,t.jsxs)("div",{className:p().formActions,children:[k&&(0,t.jsx)("button",{type:"button",className:p().cancelButton,onClick:()=>{A({address_line1:"",address_line2:"",city:"",state:"",postal_code:"",country:"US",is_default:!1}),P(null)},children:"Cancel"}),(0,t.jsx)("button",{type:"submit",className:p().saveButton,disabled:h,children:h?"Saving...":k?"Update Address":"Add Address"})]})]})]}),"security"===a&&(0,t.jsxs)("div",{className:p().tabContent,children:[(0,t.jsx)("h2",{children:"Security Settings"}),(0,t.jsxs)("div",{className:p().securitySection,children:[(0,t.jsx)("h3",{children:"Two-Factor Authentication"}),(0,t.jsx)(_,{})]})]}),"orders"===a&&(0,t.jsxs)("div",{className:p().tabContent,children:[(0,t.jsx)("h2",{children:"Your Orders"}),(0,t.jsx)("div",{className:p().ordersList,children:(0,t.jsx)("p",{className:p().comingSoon,children:"Order history coming soon!"})})]})]})]})]})}},91435:function(e){e.exports={twoFactorSetup:"TwoFactorAuth_twoFactorSetup__HyIsT",description:"TwoFactorAuth_description__VX2X1",error:"TwoFactorAuth_error__6om0q",success:"TwoFactorAuth_success__cEzBw",toggleContainer:"TwoFactorAuth_toggleContainer__ETt4Z",toggle:"TwoFactorAuth_toggle__hU6AN",toggleSwitch:"TwoFactorAuth_toggleSwitch___3OIO",slider:"TwoFactorAuth_slider__a02gE",methodsContainer:"TwoFactorAuth_methodsContainer__rYE69",method:"TwoFactorAuth_method__8B8c7",methodDescription:"TwoFactorAuth_methodDescription__V0UFV",testButton:"TwoFactorAuth_testButton__0BKy3",addPhoneLink:"TwoFactorAuth_addPhoneLink__ECIyQ",duoLink:"TwoFactorAuth_duoLink__Kh5_z",preferredMethod:"TwoFactorAuth_preferredMethod__vRlJf",radioGroup:"TwoFactorAuth_radioGroup__qCwKi",radio:"TwoFactorAuth_radio__0jQbC",backupCodes:"TwoFactorAuth_backupCodes__sg8lg",generateButton:"TwoFactorAuth_generateButton__wb6uy",codesContainer:"TwoFactorAuth_codesContainer__USZ60",warning:"TwoFactorAuth_warning___0wlx",codes:"TwoFactorAuth_codes__VHDTn",code:"TwoFactorAuth_code__uC_b2",closeButton:"TwoFactorAuth_closeButton__SGbty",printButton:"TwoFactorAuth_printButton__FU79P",loading:"TwoFactorAuth_loading__932Lk",spinner:"TwoFactorAuth_spinner__mo5NC",spin:"TwoFactorAuth_spin__Ud7VS",verificationContainer:"TwoFactorAuth_verificationContainer__tHKVG",verificationDescription:"TwoFactorAuth_verificationDescription__tQEXg",verificationForm:"TwoFactorAuth_verificationForm__mt4Tf",codeInput:"TwoFactorAuth_codeInput__Tt4JK",verifyButton:"TwoFactorAuth_verifyButton__wEAXB",resendContainer:"TwoFactorAuth_resendContainer__fs3LM",resendLink:"TwoFactorAuth_resendLink__PBmkh",countdown:"TwoFactorAuth_countdown__mL3Kb",alternativeMethod:"TwoFactorAuth_alternativeMethod__unCIl",methodButton:"TwoFactorAuth_methodButton__7g0wL",backupCodeLink:"TwoFactorAuth_backupCodeLink__hzy_k",backupCodeForm:"TwoFactorAuth_backupCodeForm__XbWxp",backupCodeInput:"TwoFactorAuth_backupCodeInput__iTQsW",duoContainer:"TwoFactorAuth_duoContainer__ezP8M",printHide:"TwoFactorAuth_printHide__856IU"}},97961:function(e){e.exports={container:"UserProfile_container__GzES2",header:"UserProfile_header__4QRX2",backLink:"UserProfile_backLink__JYvdH",content:"UserProfile_content__y8lhl",sidebar:"UserProfile_sidebar__a8wTF",userInfo:"UserProfile_userInfo__s_eXK",avatar:"UserProfile_avatar__KT0t2",initials:"UserProfile_initials__gptyZ",userName:"UserProfile_userName__Os3sW",userEmail:"UserProfile_userEmail__Fu1x9",nav:"UserProfile_nav__f_Y1A",navItem:"UserProfile_navItem__YGjiO",active:"UserProfile_active__KxEcX",mainContent:"UserProfile_mainContent__9vUkh",tabContent:"UserProfile_tabContent__0nM7l",form:"UserProfile_form__LgU_v",formRow:"UserProfile_formRow___9b4O",formGroup:"UserProfile_formGroup__yLTt1",input:"UserProfile_input__P2XfE",select:"UserProfile_select__BRl0V",textarea:"UserProfile_textarea___EE9m",helpText:"UserProfile_helpText__sOllq",checkboxLabel:"UserProfile_checkboxLabel__NK3Us",formActions:"UserProfile_formActions__PbV5t",cancelButton:"UserProfile_cancelButton__BbCFc",defaultButton:"UserProfile_defaultButton__lSvuV",deleteButton:"UserProfile_deleteButton__zijpi",editButton:"UserProfile_editButton__EGzqg",saveButton:"UserProfile_saveButton__1numi",divider:"UserProfile_divider__OqMwi",errorMessage:"UserProfile_errorMessage__09rYJ",successMessage:"UserProfile_successMessage__4lnRk",addressList:"UserProfile_addressList__31QLw",addressCard:"UserProfile_addressCard__6zkT8",defaultBadge:"UserProfile_defaultBadge__f7gYk",addressName:"UserProfile_addressName__okTfN",addressDetails:"UserProfile_addressDetails__G6PQJ",addressActions:"UserProfile_addressActions__jGFDx",noAddresses:"UserProfile_noAddresses__ajdCK",securitySection:"UserProfile_securitySection__dg90q",ordersList:"UserProfile_ordersList__LFLiF",comingSoon:"UserProfile_comingSoon__qPsg1",loading:"UserProfile_loading__GVy8l",spinner:"UserProfile_spinner__6Exdg",spin:"UserProfile_spin__ilI8E"}},11163:function(e,s,a){e.exports=a(43079)}},function(e){e.O(0,[1664,5675,2888,9774,179],function(){return e(e.s=99657)}),_N_E=e.O()}]);