(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2222],{55248:function(e,i,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/checkout",function(){return s(27431)}])},27431:function(e,i,s){"use strict";s.r(i),s.d(i,{default:function(){return b}});var a=s(85893);s(25675);var r=s(67294),n=s(11163),t=s(33299),c=s(41664),o=s.n(c);s(2859);var l=s(85472),p=s(34155);try{p.env.STRIPE_SECRET_KEY?new l.Z(p.env.STRIPE_SECRET_KEY,{apiVersion:"2023-10-16"}):console.warn("STRIPE_SECRET_KEY not found. Using mock Stripe instance.")}catch(e){console.error("Failed to initialize Stripe:",e)}var d=s(37842),h=s.n(d);function m(e){let{shippingAddress:i}=e,[s,t]=(0,r.useState)(!1),[c,o]=(0,r.useState)(null),[l,p]=(0,r.useState)(!1),[d,m]=(0,r.useState)({cardNumber:"",cardName:"",expiryDate:"",cvv:""}),u=(0,n.useRouter)(),_=async()=>{p(!0)},g=e=>{let{name:i,value:s}=e.target;m({...d,[i]:s})},y=async e=>{e.preventDefault(),o(""),t(!0);try{await new Promise(e=>setTimeout(e,2e3));let e=Math.floor(1e8+9e8*Math.random()).toString();u.push("/checkout/success?order_id=".concat(e))}catch(e){console.error("Stripe checkout error:",e),o("There was an error processing your payment. Please try again.")}finally{t(!1)}},x=e=>e.replace(/\D/g,"").replace(/(\d{4})(?=\d)/g,"$1 ").slice(0,19),j=e=>{let i=e.replace(/\D/g,"");return i.length>2?"".concat(i.slice(0,2),"/").concat(i.slice(2,4)):i};return(0,a.jsx)("div",{className:h().stripeCheckout,children:l?(0,a.jsxs)("div",{className:h().cardForm,children:[(0,a.jsxs)("div",{className:h().cardFormHeader,children:[(0,a.jsx)("h3",{children:"Enter Card Details"}),(0,a.jsxs)("div",{className:h().cardTypes,children:[(0,a.jsx)("img",{src:"/images/payments/visa.svg",alt:"Visa",height:"24"}),(0,a.jsx)("img",{src:"/images/payments/mastercard.svg",alt:"Mastercard",height:"24"}),(0,a.jsx)("img",{src:"/images/payments/amex.svg",alt:"American Express",height:"24"}),(0,a.jsx)("img",{src:"/images/payments/discover.svg",alt:"Discover",height:"24"})]})]}),(0,a.jsxs)("form",{onSubmit:y,children:[(0,a.jsxs)("div",{className:h().cardFormGroup,children:[(0,a.jsx)("label",{className:h().cardFormLabel,children:"Card Number"}),(0,a.jsx)("input",{type:"text",name:"cardNumber",className:h().cardFormInput,placeholder:"1234 5678 9012 3456",value:x(d.cardNumber),onChange:e=>m({...d,cardNumber:x(e.target.value)}),required:!0,maxLength:"19"})]}),(0,a.jsxs)("div",{className:h().cardFormGroup,children:[(0,a.jsx)("label",{className:h().cardFormLabel,children:"Cardholder Name"}),(0,a.jsx)("input",{type:"text",name:"cardName",className:h().cardFormInput,placeholder:"John Doe",value:d.cardName,onChange:g,required:!0})]}),(0,a.jsxs)("div",{className:h().cardFormRow,children:[(0,a.jsxs)("div",{className:h().cardFormGroup,children:[(0,a.jsx)("label",{className:h().cardFormLabel,children:"Expiry Date"}),(0,a.jsx)("input",{type:"text",name:"expiryDate",className:h().cardFormInput,placeholder:"MM/YY",value:j(d.expiryDate),onChange:e=>m({...d,expiryDate:j(e.target.value)}),required:!0,maxLength:"5"})]}),(0,a.jsxs)("div",{className:h().cardFormGroup,children:[(0,a.jsx)("label",{className:h().cardFormLabel,children:"CVV"}),(0,a.jsx)("input",{type:"text",name:"cvv",className:h().cardFormInput,placeholder:"123",value:d.cvv,onChange:g,required:!0,maxLength:"4"})]})]}),c&&(0,a.jsx)("div",{className:h().errorMessage,children:c}),(0,a.jsxs)("div",{style:{position:"relative"},children:[(0,a.jsx)("button",{type:"submit",className:h().cardFormButton,disabled:s,children:s?"Processing...":"Pay Now"}),s&&(0,a.jsxs)("div",{className:h().processingOverlay,children:[(0,a.jsx)("div",{className:h().spinner}),(0,a.jsx)("span",{children:"Processing payment..."})]})]}),(0,a.jsx)("div",{style:{marginTop:"16px",textAlign:"center"},children:(0,a.jsx)("button",{type:"button",onClick:()=>p(!1),style:{background:"none",border:"none",color:"#0070ba",cursor:"pointer"},children:"Back to payment options"})})]})]}):(0,a.jsxs)("button",{onClick:_,disabled:s,className:"btn btn-primary checkout-button",children:[(0,a.jsx)("img",{src:"/images/payments/stripe.svg",alt:"Stripe",width:"20",height:"20",style:{marginRight:"8px"}}),s?"Processing...":"Pay with Card"]})})}var u=s(93033),_=s.n(u);function g(e){let{shippingAddress:i,amount:s}=e,[t,c]=(0,r.useState)(!1),[o,l]=(0,r.useState)(null),[p,d]=(0,r.useState)(!1),[h,m]=(0,r.useState)(null),[u,g]=(0,r.useState)(""),[y,x]=(0,r.useState)(""),j=(0,n.useRouter)(),b=async e=>{try{c(!0),l(null),m(e),await new Promise(e=>setTimeout(e,1e3)),g({btc:"**********************************",eth:"******************************************",usdt:"TXFBqBbqJommqZf7BV8NNYzePh5zABnKM3",usdc:"******************************************",xrp:"rLW9gnQo7BQhU6igk5keqYnH3TVrCxGRzm"}[e.id]),x((s*({btc:25e-6,eth:42e-5,usdt:1,usdc:1,xrp:1.5})[e.id]).toFixed(8))}catch(e){console.error("Error generating crypto payment:",e),l(e.message||"Failed to generate crypto payment")}finally{c(!1)}},v=async()=>{try{c(!0),l(null),await new Promise(e=>setTimeout(e,2e3)),j.push("/checkout/success?payment_method=crypto")}catch(e){console.error("Error verifying payment:",e),l(e.message||"Failed to verify payment"),c(!1)}};return h?(0,a.jsxs)("div",{className:_().cryptoCheckout,children:[(0,a.jsxs)("h3",{children:["Pay with ",h.name]}),(0,a.jsxs)("div",{className:_().paymentDetails,children:[(0,a.jsx)("div",{className:_().qrCodeContainer,children:(0,a.jsx)("div",{className:_().mockQrCode,children:(0,a.jsx)("img",{src:"/images/crypto/qr-placeholder.png",alt:"QR Code for ".concat(h.name," payment"),className:_().qrCode})})}),(0,a.jsxs)("div",{className:_().paymentInstructions,children:[(0,a.jsxs)("p",{children:["Please send exactly ",(0,a.jsxs)("strong",{children:[y," ",h.id.toUpperCase()]})," to the following address:"]}),(0,a.jsxs)("div",{className:_().addressContainer,children:[(0,a.jsx)("code",{className:_().address,children:u}),(0,a.jsx)("button",{className:_().copyButton,onClick:()=>{navigator.clipboard.writeText(u),alert("Address copied to clipboard!")},children:"Copy"})]}),(0,a.jsxs)("p",{className:_().warning,children:["Important: Send only ",h.id.toUpperCase()," to this address. Sending any other cryptocurrency may result in permanent loss."]}),(0,a.jsxs)("div",{className:_().verifyContainer,children:[(0,a.jsx)("button",{className:_().verifyButton,onClick:v,disabled:t,children:t?"Verifying...":"I've Sent the Payment"}),(0,a.jsx)("button",{className:_().backButton,onClick:()=>m(null),disabled:t,children:"Back to Options"})]})]})]}),o&&(0,a.jsx)("div",{className:_().error,children:o})]}):p?(0,a.jsxs)("div",{className:_().cryptoOptions,children:[(0,a.jsx)("h3",{children:"Select a Cryptocurrency"}),(0,a.jsx)("div",{className:_().cryptoList,children:[{id:"btc",name:"Bitcoin (BTC)",icon:"/images/crypto/bitcoin.svg"},{id:"eth",name:"Ethereum (ETH)",icon:"/images/crypto/ethereum.svg"},{id:"usdt",name:"Tether (USDT)",icon:"/images/crypto/tether.svg"},{id:"usdc",name:"USD Coin (USDC)",icon:"/images/crypto/usdc.svg"},{id:"xrp",name:"Ripple (XRP)",icon:"/images/crypto/xrp.svg"}].map(e=>(0,a.jsxs)("button",{className:_().cryptoOption,onClick:()=>b(e),disabled:t,children:[(0,a.jsx)("img",{src:e.icon,alt:e.name,className:_().cryptoIcon}),(0,a.jsx)("span",{children:e.name})]},e.id))}),(0,a.jsx)("button",{className:_().backButton,onClick:()=>d(!1),disabled:t,children:"Back"}),t&&(0,a.jsx)("div",{className:_().loading,children:"Loading payment options..."}),o&&(0,a.jsx)("div",{className:_().error,children:o})]}):(0,a.jsx)("div",{className:_().cryptoCheckoutButton,children:(0,a.jsx)("button",{onClick:()=>{d(!0)},disabled:t,className:_().button,children:"Pay with Cryptocurrency"})})}var y=s(52576),x=s.n(y);function j(e){let{amount:i,shippingAddress:s,onSuccess:t,onError:c}=e,o=(0,r.useRef)(),l=(0,n.useRouter)();return(0,r.useEffect)(()=>{let e=document.createElement("script");return e.src="https://www.paypal.com/sdk/js?client-id=".concat("your_paypal_client_id","&currency=USD"),e.async=!0,e.onload=()=>{window.paypal&&window.paypal.Buttons({createOrder:(e,a)=>a.order.create({purchase_units:[{amount:{value:i.toFixed(2),currency_code:"USD"},shipping:{name:{full_name:s.name},address:{address_line_1:s.address,admin_area_2:s.city,admin_area_1:s.state,postal_code:s.zip,country_code:s.country}}}],application_context:{shipping_preference:"SET_PROVIDED_ADDRESS"}}),onApprove:async(e,i)=>{let a=await i.order.capture();try{let i=await fetch("/api/orders/paypal-success",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderID:e.orderID,paypalOrderData:a,shippingAddress:s})}),r=await i.json();if(r.success)t?t(r):l.push("/checkout/success?order_id=".concat(r.orderNumber));else throw Error(r.message||"Failed to process order")}catch(e){console.error("Error processing PayPal payment:",e),c?c(e):alert("There was an error processing your payment. Please try again.")}},onError:e=>{console.error("PayPal Checkout Error:",e),c?c(e):alert("There was an error with PayPal. Please try again later.")},style:{layout:"horizontal",color:"blue",shape:"rect",label:"paypal",height:40}}).render(o.current)},document.body.appendChild(e),()=>{document.body.contains(e)&&document.body.removeChild(e)}},[i,s,l,t,c]),(0,a.jsx)("div",{className:x().paypalButtonContainer,children:(0,a.jsx)("div",{ref:o,className:x().paypalButton})})}function b(){let{data:e,status:i}=(0,t.useSession)(),s=(0,n.useRouter)(),[c,l]=(0,r.useState)(null),[p,d]=(0,r.useState)(!0),[h,u]=(0,r.useState)(null),[_,y]=(0,r.useState)(!1),[x,b]=(0,r.useState)(.0625),[v,C]=(0,r.useState)({shipping_name:"",shipping_email:"",shipping_phone:"",shipping_address:"",shipping_city:"",shipping_state:"",shipping_zip:"",shipping_country:"US",same_as_shipping:!0,billing_name:"",billing_email:"",billing_phone:"",billing_address:"",billing_city:"",billing_state:"",billing_zip:"",billing_country:"US",payment_method:"credit_card",card_number:"",card_name:"",card_expiry:"",card_cvc:"",notes:""}),N=async()=>{try{d(!0);let e=await fetch("/api/cart");if(!e.ok)throw Error("Failed to fetch cart");let i=await e.json();if(i.success){if(0===i.cart.items.length){s.push("/cart");return}l(i.cart)}else throw Error(i.message||"Failed to fetch cart")}catch(e){console.error("Error fetching cart:",e),u(e.message)}finally{d(!1)}},k=e=>{let{name:i,value:s,type:a,checked:r}=e.target;"checkbox"===a?C(e=>({...e,[i]:r})):C(e=>({...e,[i]:s}))},f=async e=>{if(e.preventDefault(),!_)try{y(!0);let e={items:c.items.map(e=>({product_id:e.product_id,quantity:e.quantity,price:e.discounted_price||e.price,total:e.total})),shipping_address:{name:v.shipping_name,email:v.shipping_email,phone:v.shipping_phone,address:v.shipping_address,city:v.shipping_city,state:v.shipping_state,zip:v.shipping_zip,country:v.shipping_country},billing_address:v.same_as_shipping?{name:v.shipping_name,email:v.shipping_email,phone:v.shipping_phone,address:v.shipping_address,city:v.shipping_city,state:v.shipping_state,zip:v.shipping_zip,country:v.shipping_country}:{name:v.billing_name,email:v.billing_email,phone:v.billing_phone,address:v.billing_address,city:v.billing_city,state:v.billing_state,zip:v.billing_zip,country:v.billing_country},payment_method:{type:v.payment_method,card_number:v.card_number?v.card_number.replace(/\s+/g,""):null,card_name:v.card_name,card_expiry:v.card_expiry,card_cvc:v.card_cvc},notes:v.notes,total_amount:c.subtotal},i=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await i.json();if(!i.ok)throw Error(a.message||"Failed to place order");if(a.success)s.push("/orders/".concat(a.order.order_number));else throw Error(a.message||"Failed to place order")}catch(e){console.error("Error placing order:",e),u(e.message),y(!1)}};return((0,r.useEffect)(()=>{(null==e?void 0:e.user)&&C(i=>({...i,shipping_name:e.user.name||i.shipping_name,shipping_email:e.user.email||i.shipping_email}))},[e]),(0,r.useEffect)(()=>{N()},[]),(0,r.useEffect)(()=>{"unauthenticated"===i&&s.push("/auth/signin?callbackUrl=".concat(encodeURIComponent("/checkout")))},[i,s]),"loading"===i||p)?(0,a.jsxs)("div",{className:"container",children:[(0,a.jsx)("h1",{children:"Checkout"}),(0,a.jsx)("p",{children:"Loading..."})]}):h?(0,a.jsxs)("div",{className:"container",children:[(0,a.jsx)("h1",{children:"Checkout"}),(0,a.jsxs)("div",{className:"error-message",children:[(0,a.jsx)("p",{children:h}),(0,a.jsx)("button",{onClick:N,children:"Try Again"})]})]}):c?(0,a.jsxs)("div",{className:"container",children:[(0,a.jsx)("h1",{children:"Checkout"}),(0,a.jsxs)("div",{className:"checkout-container",children:[(0,a.jsx)("div",{className:"checkout-form-container",children:(0,a.jsxs)("form",{onSubmit:f,className:"checkout-form",children:[(0,a.jsxs)("div",{className:"form-section",children:[(0,a.jsx)("h2",{children:"Shipping Information"}),(0,a.jsx)("div",{className:"form-row",children:(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"shipping_name",children:"Full Name"}),(0,a.jsx)("input",{type:"text",id:"shipping_name",name:"shipping_name",value:v.shipping_name,onChange:k,required:!0})]})}),(0,a.jsxs)("div",{className:"form-row",children:[(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"shipping_email",children:"Email"}),(0,a.jsx)("input",{type:"email",id:"shipping_email",name:"shipping_email",value:v.shipping_email,onChange:k,required:!0})]}),(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"shipping_phone",children:"Phone"}),(0,a.jsx)("input",{type:"tel",id:"shipping_phone",name:"shipping_phone",value:v.shipping_phone,onChange:k,required:!0})]})]}),(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"shipping_address",children:"Address"}),(0,a.jsx)("input",{type:"text",id:"shipping_address",name:"shipping_address",value:v.shipping_address,onChange:k,required:!0})]}),(0,a.jsxs)("div",{className:"form-row",children:[(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"shipping_city",children:"City"}),(0,a.jsx)("input",{type:"text",id:"shipping_city",name:"shipping_city",value:v.shipping_city,onChange:k,required:!0})]}),(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"shipping_state",children:"State"}),(0,a.jsx)("input",{type:"text",id:"shipping_state",name:"shipping_state",value:v.shipping_state,onChange:k,required:!0})]})]}),(0,a.jsxs)("div",{className:"form-row",children:[(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"shipping_zip",children:"ZIP Code"}),(0,a.jsx)("input",{type:"text",id:"shipping_zip",name:"shipping_zip",value:v.shipping_zip,onChange:k,required:!0})]}),(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"shipping_country",children:"Country"}),(0,a.jsxs)("select",{id:"shipping_country",name:"shipping_country",value:v.shipping_country,onChange:k,required:!0,children:[(0,a.jsx)("option",{value:"US",children:"United States"}),(0,a.jsx)("option",{value:"CA",children:"Canada"}),(0,a.jsx)("option",{value:"UK",children:"United Kingdom"}),(0,a.jsx)("option",{value:"AU",children:"Australia"})]})]})]})]}),(0,a.jsxs)("div",{className:"form-section",children:[(0,a.jsxs)("div",{className:"form-group checkbox-group",children:[(0,a.jsx)("input",{type:"checkbox",id:"same_as_shipping",name:"same_as_shipping",checked:v.same_as_shipping,onChange:k}),(0,a.jsx)("label",{htmlFor:"same_as_shipping",children:"Billing address same as shipping"})]}),!v.same_as_shipping&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h2",{children:"Billing Information"}),(0,a.jsx)("div",{className:"form-row",children:(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"billing_name",children:"Full Name"}),(0,a.jsx)("input",{type:"text",id:"billing_name",name:"billing_name",value:v.billing_name,onChange:k,required:!v.same_as_shipping})]})}),(0,a.jsxs)("div",{className:"form-row",children:[(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"billing_email",children:"Email"}),(0,a.jsx)("input",{type:"email",id:"billing_email",name:"billing_email",value:v.billing_email,onChange:k,required:!v.same_as_shipping})]}),(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"billing_phone",children:"Phone"}),(0,a.jsx)("input",{type:"tel",id:"billing_phone",name:"billing_phone",value:v.billing_phone,onChange:k,required:!v.same_as_shipping})]})]}),(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"billing_address",children:"Address"}),(0,a.jsx)("input",{type:"text",id:"billing_address",name:"billing_address",value:v.billing_address,onChange:k,required:!v.same_as_shipping})]}),(0,a.jsxs)("div",{className:"form-row",children:[(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"billing_city",children:"City"}),(0,a.jsx)("input",{type:"text",id:"billing_city",name:"billing_city",value:v.billing_city,onChange:k,required:!v.same_as_shipping})]}),(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"billing_state",children:"State"}),(0,a.jsx)("input",{type:"text",id:"billing_state",name:"billing_state",value:v.billing_state,onChange:k,required:!v.same_as_shipping})]})]}),(0,a.jsxs)("div",{className:"form-row",children:[(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"billing_zip",children:"ZIP Code"}),(0,a.jsx)("input",{type:"text",id:"billing_zip",name:"billing_zip",value:v.billing_zip,onChange:k,required:!v.same_as_shipping})]}),(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"billing_country",children:"Country"}),(0,a.jsxs)("select",{id:"billing_country",name:"billing_country",value:v.billing_country,onChange:k,required:!v.same_as_shipping,children:[(0,a.jsx)("option",{value:"US",children:"United States"}),(0,a.jsx)("option",{value:"CA",children:"Canada"}),(0,a.jsx)("option",{value:"UK",children:"United Kingdom"}),(0,a.jsx)("option",{value:"AU",children:"Australia"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"form-section",children:[(0,a.jsx)("h2",{children:"Payment Information"}),(0,a.jsxs)("div",{className:"payment-options",children:[(0,a.jsxs)("div",{className:"payment-option",children:[(0,a.jsx)("h3",{children:"Credit Card Payment"}),(0,a.jsx)("p",{children:"We use Stripe for secure credit card processing. You will be redirected to Stripe to complete your payment."}),(0,a.jsxs)("div",{className:"stripe-info",children:[(0,a.jsx)("img",{src:"/stripe-logo.svg",alt:"Stripe",className:"stripe-logo"}),(0,a.jsx)("p",{children:"Your payment information is securely processed by Stripe. We never store your card details."})]})]}),(0,a.jsxs)("div",{className:"payment-option",children:[(0,a.jsx)("h3",{children:"PayPal Payment"}),(0,a.jsx)("p",{children:"Pay securely with PayPal. You can use your PayPal balance, bank account, or credit card."}),(0,a.jsxs)("div",{className:"paypal-info",children:[(0,a.jsx)("img",{src:"/images/payments/paypal.svg",alt:"PayPal",className:"paypal-logo"}),(0,a.jsx)("p",{children:"Fast, secure checkout with PayPal. No account required for credit card payments."})]})]}),(0,a.jsxs)("div",{className:"payment-option",children:[(0,a.jsx)("h3",{children:"Cryptocurrency Payment"}),(0,a.jsx)("p",{children:"We accept Bitcoin, Ethereum, and other cryptocurrencies. Select this option to pay with cryptocurrency."}),(0,a.jsxs)("div",{className:"crypto-info",children:[(0,a.jsxs)("div",{className:"crypto-icons",children:[(0,a.jsx)("img",{src:"/images/crypto/bitcoin.svg",alt:"Bitcoin",className:"crypto-icon"}),(0,a.jsx)("img",{src:"/images/crypto/ethereum.svg",alt:"Ethereum",className:"crypto-icon"}),(0,a.jsx)("img",{src:"/images/crypto/usdt.svg",alt:"Tether",className:"crypto-icon"})]}),(0,a.jsx)("p",{children:"Cryptocurrency payments are processed instantly. No account required."})]})]})]})]}),(0,a.jsxs)("div",{className:"form-section",children:[(0,a.jsx)("h2",{children:"Order Notes"}),(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("label",{htmlFor:"notes",children:"Notes"}),(0,a.jsx)("textarea",{id:"notes",name:"notes",value:v.notes,onChange:k,placeholder:"Special instructions for delivery",rows:"3"})]})]}),(0,a.jsxs)("div",{className:"checkout-actions",children:[(0,a.jsx)(o(),{href:"/cart",className:"btn btn-secondary",children:"Back to Cart"}),(0,a.jsxs)("div",{className:"payment-buttons",children:[(0,a.jsx)(m,{shippingAddress:v.same_as_shipping?{name:v.shipping_name,email:v.shipping_email,phone:v.shipping_phone,address:v.shipping_address,city:v.shipping_city,state:v.shipping_state,zip:v.shipping_zip,country:v.shipping_country}:{name:v.billing_name,email:v.billing_email,phone:v.billing_phone,address:v.billing_address,city:v.billing_city,state:v.billing_state,zip:v.billing_zip,country:v.billing_country}}),(0,a.jsx)(j,{amount:c.subtotal+c.subtotal*x,shippingAddress:v.same_as_shipping?{name:v.shipping_name,email:v.shipping_email,phone:v.shipping_phone,address:v.shipping_address,city:v.shipping_city,state:v.shipping_state,zip:v.shipping_zip,country:v.shipping_country}:{name:v.billing_name,email:v.billing_email,phone:v.billing_phone,address:v.billing_address,city:v.billing_city,state:v.billing_state,zip:v.billing_zip,country:v.billing_country},onSuccess:e=>{s.push("/checkout/success?order_id=".concat(e.orderNumber))},onError:e=>{u(e.message||"There was an error processing your PayPal payment")}}),(0,a.jsx)(g,{shippingAddress:v.same_as_shipping?{name:v.shipping_name,email:v.shipping_email,phone:v.shipping_phone,address:v.shipping_address,city:v.shipping_city,state:v.shipping_state,zip:v.shipping_zip,country:v.shipping_country}:{name:v.billing_name,email:v.billing_email,phone:v.billing_phone,address:v.billing_address,city:v.billing_city,state:v.billing_state,zip:v.billing_zip,country:v.billing_country},amount:c.subtotal+c.subtotal*x})]})]})]})}),(0,a.jsxs)("div",{className:"checkout-summary",children:[(0,a.jsx)("h2",{children:"Order Summary"}),(0,a.jsx)("div",{className:"checkout-items",children:c.items.map(e=>(0,a.jsxs)("div",{className:"checkout-item",children:[(0,a.jsx)("div",{className:"checkout-item-image",children:(0,a.jsx)("img",{src:e.image_url||"/placeholder.svg",alt:e.name})}),(0,a.jsxs)("div",{className:"checkout-item-details",children:[(0,a.jsx)("div",{className:"checkout-item-name",children:e.name}),(0,a.jsxs)("div",{className:"checkout-item-price",children:["$",(e.discounted_price||e.price).toFixed(2)," x ",e.quantity]})]}),(0,a.jsxs)("div",{className:"checkout-item-total",children:["$",e.total.toFixed(2)]})]},e.id))}),(0,a.jsxs)("div",{className:"checkout-totals",children:[(0,a.jsxs)("div",{className:"summary-row",children:[(0,a.jsx)("span",{children:"Subtotal:"}),(0,a.jsxs)("span",{children:["$",c.subtotal.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"summary-row",children:[(0,a.jsx)("span",{children:"Shipping:"}),(0,a.jsx)("span",{children:"Free"})]}),(0,a.jsxs)("div",{className:"summary-row",children:[(0,a.jsxs)("span",{children:["Tax (",(100*x).toFixed(2),"%):"]}),(0,a.jsxs)("span",{children:["$",(c.subtotal*x).toFixed(2)]})]}),(0,a.jsxs)("div",{className:"summary-row total",children:[(0,a.jsx)("span",{children:"Total:"}),(0,a.jsxs)("span",{children:["$",(c.subtotal+c.subtotal*x).toFixed(2)]})]})]})]})]})]}):(0,a.jsxs)("div",{className:"container",children:[(0,a.jsx)("h1",{children:"Checkout"}),(0,a.jsx)("p",{children:"Loading cart..."})]})}},93033:function(e){e.exports={cryptoCheckoutButton:"CryptoCheckout_cryptoCheckoutButton__EIpDF",button:"CryptoCheckout_button__o8UmZ",cryptoOptions:"CryptoCheckout_cryptoOptions__hlr3d",cryptoList:"CryptoCheckout_cryptoList__ycEaa",cryptoOption:"CryptoCheckout_cryptoOption__ExEcV",cryptoIcon:"CryptoCheckout_cryptoIcon__yMaQF",backButton:"CryptoCheckout_backButton__9r_Sh",loading:"CryptoCheckout_loading__enX4g",error:"CryptoCheckout_error__jVjXp",cryptoCheckout:"CryptoCheckout_cryptoCheckout__v8SSt",paymentDetails:"CryptoCheckout_paymentDetails__2WoiD",qrCodeContainer:"CryptoCheckout_qrCodeContainer__HK8fs",mockQrCode:"CryptoCheckout_mockQrCode__2IY__",qrCode:"CryptoCheckout_qrCode__MMgoT",paymentInstructions:"CryptoCheckout_paymentInstructions__XjhYK",addressContainer:"CryptoCheckout_addressContainer__6VKc5",address:"CryptoCheckout_address__JY0BC",copyButton:"CryptoCheckout_copyButton__9_dUB",warning:"CryptoCheckout_warning__Y3xCR",verifyContainer:"CryptoCheckout_verifyContainer__qj3e8",verifyButton:"CryptoCheckout_verifyButton__qRkPA"}},52576:function(e){e.exports={paypalButtonContainer:"PayPalCheckout_paypalButtonContainer__cf08y",paypalButton:"PayPalCheckout_paypalButton__2k1SX"}},37842:function(e){e.exports={stripeCheckout:"StripeCheckout_stripeCheckout__6Yblw",cardForm:"StripeCheckout_cardForm__TS_Xg",cardFormHeader:"StripeCheckout_cardFormHeader__nCRt_",cardTypes:"StripeCheckout_cardTypes__zaT7b",cardFormRow:"StripeCheckout_cardFormRow__RXwP4",cardFormGroup:"StripeCheckout_cardFormGroup__Ax4nB",cardFormLabel:"StripeCheckout_cardFormLabel__ZqelC",cardFormInput:"StripeCheckout_cardFormInput__hAYHA",cardFormButton:"StripeCheckout_cardFormButton__bcPEO",errorMessage:"StripeCheckout_errorMessage__V7nau",processingOverlay:"StripeCheckout_processingOverlay__NBxB_",spinner:"StripeCheckout_spinner__xt4W9",spin:"StripeCheckout_spin__tdVyh"}},24654:function(){}},function(e){e.O(0,[1664,5675,5374,2888,9774,179],function(){return e(e.s=55248)}),_N_E=e.O()}]);