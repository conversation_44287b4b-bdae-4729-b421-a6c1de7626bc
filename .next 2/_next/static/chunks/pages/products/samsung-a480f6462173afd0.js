(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7548,1658,3119],{25542:function(e,t,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/products/samsung",function(){return a(47270)}])},91658:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return m}});var r=a(85893),s=a(67294);a(25675);var i=a(41664),c=a.n(i),n=a(16013),o=a.n(n),l=e=>{let{initialValue:t=1,min:a=1,max:i=99,onChange:c}=e,[n,l]=(0,s.useState)(t);return(0,r.jsxs)("div",{className:o().quantitySelector,children:[(0,r.jsx)("button",{className:o().button,onClick:()=>{if(n>a){let e=n-1;l(e),c&&c(e)}},disabled:n<=a,children:"-"}),(0,r.jsx)("input",{type:"text",value:n,onChange:e=>{let t=parseInt(e.target.value,10);!isNaN(t)&&t>=a&&t<=i&&(l(t),c&&c(t))},className:o().input}),(0,r.jsx)("button",{className:o().button,onClick:()=>{if(n<i){let e=n+1;l(e),c&&c(e)}},disabled:n>=i,children:"+"})]})},d=a(44464),u=a.n(d),m=e=>{let{products:t,title:a="Popular Products"}=e,[i,n]=(0,s.useState)(t.reduce((e,t)=>(e[t.id]=1,e),{})),o=(e,t)=>{n(a=>({...a,[e]:t}))},d=async e=>{try{if(!(await fetch("/api/cart",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:e,quantity:i[e]})})).ok)throw Error("Failed to add to cart");alert("Successfully added ".concat(i[e]," of product ID ").concat(e," to cart"))}catch(e){console.error("Error adding to cart:",e),alert("Failed to add to cart. Please try again.")}};return(0,r.jsx)("section",{className:u().productList,children:(0,r.jsxs)("div",{className:u().container,children:[(0,r.jsx)("h2",{className:u().title,children:a}),(0,r.jsx)("p",{className:u().subtitle,children:"Browse our selection of high-quality repair parts and tools"}),(0,r.jsxs)("div",{className:u().categories,children:[(0,r.jsx)("button",{className:"".concat(u().categoryButton," ").concat(u().active),children:"All"}),(0,r.jsx)("button",{className:u().categoryButton,children:"iPhone Parts"}),(0,r.jsx)("button",{className:u().categoryButton,children:"Samsung Parts"}),(0,r.jsx)("button",{className:u().categoryButton,children:"iPad Parts"}),(0,r.jsx)("button",{className:u().categoryButton,children:"Tools"})]}),(0,r.jsx)("div",{className:u().products,children:t.map(e=>(0,r.jsxs)("div",{className:u().product,children:[(0,r.jsx)("div",{className:u().imageContainer,children:(0,r.jsx)("img",{src:e.imageUrl||"/images/placeholder.png",alt:e.name,className:u().image})}),(0,r.jsxs)("div",{className:u().content,children:[(0,r.jsx)("div",{className:u().category,children:e.category}),(0,r.jsx)("h3",{className:u().name,children:e.name}),(0,r.jsx)("div",{className:u().price,children:e.discount_percentage>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{className:u().originalPrice,children:["$",(e.price/(1-e.discount_percentage/100)).toFixed(2)]}),(0,r.jsxs)("span",{className:u().salePrice,children:["$",e.price.toFixed(2)]})]}):(0,r.jsxs)("span",{children:["$",e.price.toFixed(2)]})}),(0,r.jsx)(l,{initialValue:i[e.id],onChange:t=>o(e.id,t)}),(0,r.jsx)("button",{className:u().addToCart,onClick:()=>d(e.id),children:"Add to Cart"})]})]},e.id))}),(0,r.jsx)("div",{className:u().viewMore,children:(0,r.jsx)(c(),{href:"/products",className:u().viewMoreLink,children:"View More Products"})})]})})}},47270:function(e,t,a){"use strict";a.r(t);var r=a(85893),s=a(67294),i=a(56029),c=a(91658);let n=[{id:201,name:"Samsung Galaxy S22 OLED Screen",category:"Samsung Parts",price:149.99,discount_percentage:15,imageUrl:"/images/placeholder.svg",badge:"15% OFF"},{id:202,name:"Samsung Galaxy S21 Battery",category:"Samsung Parts",price:39.99,discount_percentage:0,imageUrl:"/images/placeholder.svg"},{id:203,name:"Samsung Galaxy Note 20 Charging Port",category:"Samsung Parts",price:24.99,discount_percentage:10,imageUrl:"/images/placeholder.svg",badge:"10% OFF"},{id:204,name:"Samsung Galaxy A53 Camera Module",category:"Samsung Parts",price:59.99,discount_percentage:0,imageUrl:"/images/placeholder.svg"}];t.default=s.memo(function(){return(0,r.jsx)(i.Z,{title:"Samsung Parts - Midas Technical Solutions",description:"High-quality Samsung replacement parts for all models. Screens, batteries, charging ports, and more.",children:(0,r.jsxs)("div",{className:"container",style:{padding:"40px 20px"},children:[(0,r.jsx)("h1",{style:{fontSize:"2rem",marginBottom:"1rem"},children:"Samsung Parts"}),(0,r.jsx)("p",{style:{marginBottom:"2rem"},children:"Find high-quality replacement parts for all Samsung Galaxy models. We offer screens, batteries, charging ports, cameras, and more to help you repair your Samsung device."}),(0,r.jsx)(c.default,{products:n,title:"Samsung Replacement Parts"})]})})})},44464:function(e){e.exports={productList:"ProductList_productList__e7F09",container:"ProductList_container__Y8Ikq",title:"ProductList_title__5nv2l",subtitle:"ProductList_subtitle__6qBiW",categories:"ProductList_categories__YKbrV",categoryButton:"ProductList_categoryButton__t5avI",active:"ProductList_active__lyTi_",products:"ProductList_products__COjlj",product:"ProductList_product__R4OD6",imageContainer:"ProductList_imageContainer__PW2P0",image:"ProductList_image__z3N2X",content:"ProductList_content__4Fca5",category:"ProductList_category__BcTtk",name:"ProductList_name__YIS3S",price:"ProductList_price__M48zy",addToCart:"ProductList_addToCart__JY4jE",viewMore:"ProductList_viewMore__qSnKY",viewMoreLink:"ProductList_viewMoreLink__0D9bK"}},16013:function(e){e.exports={quantitySelector:"QuantitySelector_quantitySelector__quwG9",button:"QuantitySelector_button__f7P5N",input:"QuantitySelector_input__za5C8"}},11163:function(e,t,a){e.exports=a(43079)}},function(e){e.O(0,[1664,5675,6029,2888,9774,179],function(){return e(e.s=25542)}),_N_E=e.O()}]);