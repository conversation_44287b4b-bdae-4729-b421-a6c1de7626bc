(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4832,1658,3119],{26665:function(t,e,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/products/macbook",function(){return a(92023)}])},91658:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return _}});var r=a(85893),c=a(67294);a(25675);var o=a(41664),i=a.n(o),s=a(16013),n=a.n(s),d=t=>{let{initialValue:e=1,min:a=1,max:o=99,onChange:i}=t,[s,d]=(0,c.useState)(e);return(0,r.jsxs)("div",{className:n().quantitySelector,children:[(0,r.jsx)("button",{className:n().button,onClick:()=>{if(s>a){let t=s-1;d(t),i&&i(t)}},disabled:s<=a,children:"-"}),(0,r.jsx)("input",{type:"text",value:s,onChange:t=>{let e=parseInt(t.target.value,10);!isNaN(e)&&e>=a&&e<=o&&(d(e),i&&i(e))},className:n().input}),(0,r.jsx)("button",{className:n().button,onClick:()=>{if(s<o){let t=s+1;d(t),i&&i(t)}},disabled:s>=o,children:"+"})]})},l=a(44464),u=a.n(l),_=t=>{let{products:e,title:a="Popular Products"}=t,[o,s]=(0,c.useState)(e.reduce((t,e)=>(t[e.id]=1,t),{})),n=(t,e)=>{s(a=>({...a,[t]:e}))},l=async t=>{try{if(!(await fetch("/api/cart",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:t,quantity:o[t]})})).ok)throw Error("Failed to add to cart");alert("Successfully added ".concat(o[t]," of product ID ").concat(t," to cart"))}catch(t){console.error("Error adding to cart:",t),alert("Failed to add to cart. Please try again.")}};return(0,r.jsx)("section",{className:u().productList,children:(0,r.jsxs)("div",{className:u().container,children:[(0,r.jsx)("h2",{className:u().title,children:a}),(0,r.jsx)("p",{className:u().subtitle,children:"Browse our selection of high-quality repair parts and tools"}),(0,r.jsxs)("div",{className:u().categories,children:[(0,r.jsx)("button",{className:"".concat(u().categoryButton," ").concat(u().active),children:"All"}),(0,r.jsx)("button",{className:u().categoryButton,children:"iPhone Parts"}),(0,r.jsx)("button",{className:u().categoryButton,children:"Samsung Parts"}),(0,r.jsx)("button",{className:u().categoryButton,children:"iPad Parts"}),(0,r.jsx)("button",{className:u().categoryButton,children:"Tools"})]}),(0,r.jsx)("div",{className:u().products,children:e.map(t=>(0,r.jsxs)("div",{className:u().product,children:[(0,r.jsx)("div",{className:u().imageContainer,children:(0,r.jsx)("img",{src:t.imageUrl||"/images/placeholder.png",alt:t.name,className:u().image})}),(0,r.jsxs)("div",{className:u().content,children:[(0,r.jsx)("div",{className:u().category,children:t.category}),(0,r.jsx)("h3",{className:u().name,children:t.name}),(0,r.jsx)("div",{className:u().price,children:t.discount_percentage>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{className:u().originalPrice,children:["$",(t.price/(1-t.discount_percentage/100)).toFixed(2)]}),(0,r.jsxs)("span",{className:u().salePrice,children:["$",t.price.toFixed(2)]})]}):(0,r.jsxs)("span",{children:["$",t.price.toFixed(2)]})}),(0,r.jsx)(d,{initialValue:o[t.id],onChange:e=>n(t.id,e)}),(0,r.jsx)("button",{className:u().addToCart,onClick:()=>l(t.id),children:"Add to Cart"})]})]},t.id))}),(0,r.jsx)("div",{className:u().viewMore,children:(0,r.jsx)(i(),{href:"/products",className:u().viewMoreLink,children:"View More Products"})})]})})}},92023:function(t,e,a){"use strict";a.r(e);var r=a(85893),c=a(67294);a(9008);var o=a(56029),i=a(91658);let s=[{id:401,name:"MacBook Pro Keyboard Replacement",category:"MacBook Parts",price:129.99,discount_percentage:10,imageUrl:"/images/placeholder.svg",badge:"10% OFF"},{id:402,name:"MacBook Air Battery",category:"MacBook Parts",price:89.99,discount_percentage:0,imageUrl:"/images/placeholder.svg"},{id:403,name:"MacBook Pro Retina Display",category:"MacBook Parts",price:299.99,discount_percentage:15,imageUrl:"/images/placeholder.svg",badge:"15% OFF"},{id:404,name:"MacBook Trackpad Replacement",category:"MacBook Parts",price:79.99,discount_percentage:0,imageUrl:"/images/placeholder.svg"}];e.default=c.memo(function(){return(0,r.jsx)(o.Z,{title:"MacBook Parts",description:"High-quality MacBook replacement parts for all models. Keyboards, batteries, displays, trackpads, and more.",children:(0,r.jsxs)("div",{className:"container",style:{padding:"40px 20px"},children:[(0,r.jsx)("h1",{style:{fontSize:"2rem",marginBottom:"1rem"},children:"MacBook Parts"}),(0,r.jsx)("p",{style:{marginBottom:"2rem"},children:"Find high-quality replacement parts for all MacBook models. We offer keyboards, batteries, displays, trackpads, and more to help you repair your MacBook."}),(0,r.jsx)(i.default,{products:s,title:"MacBook Replacement Parts"})]})})})},44464:function(t){t.exports={productList:"ProductList_productList__e7F09",container:"ProductList_container__Y8Ikq",title:"ProductList_title__5nv2l",subtitle:"ProductList_subtitle__6qBiW",categories:"ProductList_categories__YKbrV",categoryButton:"ProductList_categoryButton__t5avI",active:"ProductList_active__lyTi_",products:"ProductList_products__COjlj",product:"ProductList_product__R4OD6",imageContainer:"ProductList_imageContainer__PW2P0",image:"ProductList_image__z3N2X",content:"ProductList_content__4Fca5",category:"ProductList_category__BcTtk",name:"ProductList_name__YIS3S",price:"ProductList_price__M48zy",addToCart:"ProductList_addToCart__JY4jE",viewMore:"ProductList_viewMore__qSnKY",viewMoreLink:"ProductList_viewMoreLink__0D9bK"}},16013:function(t){t.exports={quantitySelector:"QuantitySelector_quantitySelector__quwG9",button:"QuantitySelector_button__f7P5N",input:"QuantitySelector_input__za5C8"}},11163:function(t,e,a){t.exports=a(43079)}},function(t){t.O(0,[1664,5675,6029,2888,9774,179],function(){return t(t.s=26665)}),_N_E=t.O()}]);