(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9190],{38086:function(e,t,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/cart",function(){return a(49108)}])},49108:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return m}});var r=a(85893);a(25675);var s=a(67294),i=a(41664),c=a.n(i),n=a(33299),o=a(11163),l=a(15643),d=a.n(l),_=a(56029);function m(){let{data:e}=(0,n.useSession)(),t=(0,o.useRouter)(),[a,i]=(0,s.useState)(null),[l,m]=(0,s.useState)(!0),[u,h]=(0,s.useState)(null),[p,g]=(0,s.useState)(!1),x=async()=>{try{var t;m(!0);let a={success:!0,cart:{id:1,user_id:(null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.id)||null,items:[{id:1,name:"iPhone 13 Pro LCD Screen",price:89.99,quantity:2,image_url:"/images/gapp/iphone-screen.jpg",slug:"iphone-13-pro-lcd-screen",discount_percentage:0,total:179.98},{id:2,name:"Samsung Galaxy S21 Battery",price:39.99,quantity:1,image_url:"/images/gapp/samsung-battery.jpg",slug:"samsung-galaxy-s21-battery",discount_percentage:10,discounted_price:35.99,total:35.99},{id:3,name:"Professional Repair Tool Kit",price:129.99,quantity:1,image_url:"/images/gapp/repair-tools.png",slug:"professional-repair-tool-kit",discount_percentage:0,total:129.99}],item_count:4,subtotal:345.96}};i(a.cart),setTimeout(()=>{m(!1)},500)}catch(e){console.error("Error fetching cart:",e),h(e.message),m(!1)}},j=async(e,t)=>{try{g(!0);let a=await fetch("/api/cart",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({itemId:e,quantity:t})});if(!a.ok)throw Error("Failed to update cart");let r=await a.json();if(r.success)i(r.cart);else throw Error(r.message||"Failed to update cart")}catch(e){console.error("Error updating cart:",e),h(e.message)}finally{g(!1)}},y=async e=>{try{g(!0);let t=await fetch("/api/cart",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({itemId:e})});if(!t.ok)throw Error("Failed to remove item from cart");let a=await t.json();if(a.success)i(a.cart);else throw Error(a.message||"Failed to remove item from cart")}catch(e){console.error("Error removing item from cart:",e),h(e.message)}finally{g(!1)}},C=async()=>{if(confirm("Are you sure you want to clear your cart?"))try{g(!0);let e=await fetch("/api/cart/clear",{method:"DELETE"});if(!e.ok)throw Error("Failed to clear cart");let t=await e.json();if(t.success)x();else throw Error(t.message||"Failed to clear cart")}catch(e){console.error("Error clearing cart:",e),h(e.message)}finally{g(!1)}};return((0,s.useEffect)(()=>{x()},[]),l)?(0,r.jsx)(_.Z,{title:"Your Cart",description:"View and manage your shopping cart",children:(0,r.jsx)("div",{className:"container",children:(0,r.jsxs)("div",{className:"loading-container",children:[(0,r.jsx)("div",{className:"loading-spinner"}),(0,r.jsx)("p",{children:"Loading your cart..."})]})})}):u?(0,r.jsx)(_.Z,{title:"Your Cart",description:"View and manage your shopping cart",children:(0,r.jsxs)("div",{className:"container",children:[(0,r.jsx)("h1",{children:"Your Cart"}),(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("p",{children:u}),(0,r.jsx)("button",{onClick:x,className:"btn btn-primary",children:"Try Again"})]})]})}):a&&0!==a.items.length?(0,r.jsx)(_.Z,{title:"Your Cart",description:"View and manage your shopping cart",children:(0,r.jsxs)("div",{className:"container",children:[(0,r.jsx)("h1",{children:"Your Cart"}),(0,r.jsxs)("div",{className:d().cart_container,children:[(0,r.jsxs)("div",{className:d().cart_items,children:[(0,r.jsxs)("table",{className:d().cart_table,children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"Product"}),(0,r.jsx)("th",{children:"Price"}),(0,r.jsx)("th",{children:"Quantity"}),(0,r.jsx)("th",{children:"Total"}),(0,r.jsx)("th",{children:"Actions"})]})}),(0,r.jsx)("tbody",{children:a.items.map(e=>(0,r.jsxs)("tr",{className:d().cart_item,children:[(0,r.jsxs)("td",{className:d().cart_item_product,children:[(0,r.jsx)("div",{className:d().cart_item_image,children:(0,r.jsx)("img",{src:e.image_url||"/images/products/0DA4ABBF-40A3-456A-8275-7A18F7831F17.JPG",alt:e.name})}),(0,r.jsx)("div",{className:d().cart_item_details,children:(0,r.jsx)(c(),{href:"/products/".concat(e.slug),className:d().cart_item_name,children:e.name})})]}),(0,r.jsx)("td",{className:d().cart_item_price,children:e.discount_percentage>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{className:d().original_price,children:["$",e.price.toFixed(2)]}),(0,r.jsxs)("span",{className:d().discounted_price,children:["$",e.discounted_price.toFixed(2)]})]}):(0,r.jsxs)("span",{children:["$",e.price.toFixed(2)]})}),(0,r.jsx)("td",{className:d().cart_item_quantity,children:(0,r.jsxs)("div",{className:d().quantity_control,children:[(0,r.jsx)("button",{onClick:()=>j(e.id,Math.max(1,e.quantity-1)),disabled:p||e.quantity<=1,children:"-"}),(0,r.jsx)("span",{children:e.quantity}),(0,r.jsx)("button",{onClick:()=>j(e.id,e.quantity+1),disabled:p,children:"+"})]})}),(0,r.jsxs)("td",{className:d().cart_item_total,children:["$",e.total.toFixed(2)]}),(0,r.jsx)("td",{className:d().cart_item_actions,children:(0,r.jsx)("button",{onClick:()=>y(e.id),disabled:p,className:d().remove_item_button,children:"Remove"})})]},e.id))})]}),(0,r.jsxs)("div",{className:d().cart_actions,children:[(0,r.jsx)("button",{onClick:C,disabled:p,className:"btn btn-secondary",children:"Clear Cart"}),(0,r.jsx)(c(),{href:"/products",className:"btn btn-secondary",children:"Continue Shopping"})]})]}),(0,r.jsxs)("div",{className:d().cart_summary,children:[(0,r.jsx)("h2",{children:"Order Summary"}),(0,r.jsxs)("div",{className:d().summary_row,children:[(0,r.jsxs)("span",{children:["Items (",a.item_count,"):"]}),(0,r.jsxs)("span",{children:["$",a.subtotal.toFixed(2)]})]}),(0,r.jsxs)("div",{className:d().summary_row,children:[(0,r.jsx)("span",{children:"Shipping:"}),(0,r.jsx)("span",{children:"Free"})]}),(0,r.jsxs)("div",{className:"".concat(d().summary_row," ").concat(d().total),children:[(0,r.jsx)("span",{children:"Total:"}),(0,r.jsxs)("span",{children:["$",a.subtotal.toFixed(2)]})]}),(0,r.jsx)("button",{onClick:()=>{t.push("/checkout")},disabled:p,className:"btn btn-primary checkout_button",children:"Proceed to Checkout"}),!e&&(0,r.jsx)("div",{className:d().guest_checkout_notice,children:(0,r.jsxs)("p",{children:[(0,r.jsx)(c(),{href:"/auth/signin?callbackUrl=".concat(encodeURIComponent("/cart")),children:"Sign in"})," to use saved addresses and payment methods."]})})]})]})]})}):(0,r.jsx)(_.Z,{title:"Your Cart",description:"View and manage your shopping cart",children:(0,r.jsxs)("div",{className:"container",children:[(0,r.jsx)("h1",{children:"Your Cart"}),(0,r.jsxs)("div",{className:"empty-cart",children:[(0,r.jsx)("div",{className:"empty-cart-image",children:(0,r.jsx)("img",{src:"/images/backgrounds/apple-devices-800x702.jpg",alt:"Empty Cart"})}),(0,r.jsx)("h2",{children:"Your cart is empty"}),(0,r.jsx)("p",{children:"Looks like you haven't added any products to your cart yet."}),(0,r.jsx)(c(),{href:"/products",className:"btn btn-primary",children:"Continue Shopping"})]})]})})}},15643:function(e){e.exports={cart_container:"CartPage_cart_container__PmVwz",cart_items:"CartPage_cart_items__5TNfY",cart_summary:"CartPage_cart_summary__wqCnS",cart_table:"CartPage_cart_table__G4aqW",cart_item_product:"CartPage_cart_item_product__VPpm_",cart_item_image:"CartPage_cart_item_image__dXdrp",cart_item_details:"CartPage_cart_item_details__6zcP_",cart_item_name:"CartPage_cart_item_name__U__JE",cart_item_price:"CartPage_cart_item_price__Rfpjf",original_price:"CartPage_original_price__70q_Z",discounted_price:"CartPage_discounted_price__E6Rhx",cart_item_quantity:"CartPage_cart_item_quantity__5eqTl",quantity_control:"CartPage_quantity_control__yBp7j",cart_item_total:"CartPage_cart_item_total__ZIVJp",cart_item_actions:"CartPage_cart_item_actions__F_B9r",remove_item_button:"CartPage_remove_item_button__weFfs",cart_actions:"CartPage_cart_actions__58KQL",summary_row:"CartPage_summary_row__3HRU5",total:"CartPage_total__HxQWG",checkout_button:"CartPage_checkout_button__iqdTk",guest_checkout_notice:"CartPage_guest_checkout_notice__VpAAb"}},11163:function(e,t,a){e.exports=a(43079)}},function(e){e.O(0,[1664,5675,6029,2888,9774,179],function(){return e(e.s=38086)}),_N_E=e.O()}]);