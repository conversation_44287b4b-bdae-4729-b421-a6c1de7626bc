(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1426],{76376:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/orders",function(){return s(69715)}])},69715:function(e,t,s){"use strict";s.r(t),s.d(t,{__N_SSP:function(){return g},default:function(){return p}});var a=s(85893),n=s(67294),i=s(33299),o=s(11163),r=s(9008),c=s.n(r),d=s(41664),l=s.n(d),_=s(2575),u=s(60506),m=s.n(u),g=!0;function p(){let{data:e}=(0,i.useSession)(),[t,s]=(0,n.useState)([]),[r,d]=(0,n.useState)(!0),[u,g]=(0,n.useState)("all"),[p,h]=(0,n.useState)(""),[P,x]=(0,n.useState)("created_at"),[A,j]=(0,n.useState)("desc"),[v,C]=(0,n.useState)(1),[b,f]=(0,n.useState)(1);async function N(){try{d(!0);let e=new URLSearchParams;e.append("filter",u),e.append("search",p),e.append("sortBy",P),e.append("sortOrder",A),e.append("page",v),e.append("limit",10);let t=await fetch("/api/admin/orders?".concat(e.toString()));if(!t.ok)throw Error("Failed to fetch orders");let a=await t.json();s(a.orders),f(a.totalPages),d(!1)}catch(e){console.error("Error fetching orders:",e),d(!1)}}(0,o.useRouter)(),(0,n.useEffect)(()=>{N()},[u,p,P,A,v]);let S=e=>{P===e?j("asc"===A?"desc":"asc"):(x(e),j("desc")),C(1)},B=e=>{e>=1&&e<=b&&C(e)},k=async(e,t)=>{try{if(!(await fetch("/api/admin/orders/".concat(e,"/status"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:t})})).ok)throw Error("Failed to update order status");N()}catch(e){console.error("Error updating order status:",e),alert("Failed to update order status. Please try again.")}},w=e=>new Date(e).toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"}),y=e=>{switch(e.toLowerCase()){case"pending":return m().statusPending;case"processing":return m().statusProcessing;case"shipped":return m().statusShipped;case"delivered":return m().statusDelivered;case"cancelled":return m().statusCancelled;default:return""}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c(),{children:(0,a.jsx)("title",{children:"Orders | Admin Dashboard"})}),(0,a.jsxs)(_.Z,{children:[(0,a.jsx)("div",{className:m().pageHeader,children:(0,a.jsx)("h1",{children:"Orders"})}),(0,a.jsxs)("div",{className:m().filterBar,children:[(0,a.jsxs)("div",{className:m().filterGroup,children:[(0,a.jsx)("label",{htmlFor:"filter",children:"Filter:"}),(0,a.jsxs)("select",{id:"filter",value:u,onChange:e=>{g(e.target.value),C(1)},className:m().select,children:[(0,a.jsx)("option",{value:"all",children:"All Orders"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"processing",children:"Processing"}),(0,a.jsx)("option",{value:"shipped",children:"Shipped"}),(0,a.jsx)("option",{value:"delivered",children:"Delivered"}),(0,a.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),C(1),N()},className:m().searchForm,children:[(0,a.jsx)("input",{type:"text",placeholder:"Search by order # or customer...",value:p,onChange:e=>{h(e.target.value)},className:m().searchInput}),(0,a.jsx)("button",{type:"submit",className:m().searchButton,children:"Search"})]})]}),r?(0,a.jsxs)("div",{className:m().loading,children:[(0,a.jsx)("div",{className:m().spinner}),(0,a.jsx)("p",{children:"Loading orders..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:m().tableContainer,children:(0,a.jsxs)("table",{className:m().table,children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{children:[(0,a.jsxs)("th",{className:"".concat(m().sortableColumn," ").concat("order_number"===P?m().activeSortColumn:""),onClick:()=>S("order_number"),children:["Order #","order_number"===P&&(0,a.jsx)("span",{className:m().sortIcon,children:"asc"===A?"↑":"↓"})]}),(0,a.jsxs)("th",{className:"".concat(m().sortableColumn," ").concat("customer_name"===P?m().activeSortColumn:""),onClick:()=>S("customer_name"),children:["Customer","customer_name"===P&&(0,a.jsx)("span",{className:m().sortIcon,children:"asc"===A?"↑":"↓"})]}),(0,a.jsxs)("th",{className:"".concat(m().sortableColumn," ").concat("created_at"===P?m().activeSortColumn:""),onClick:()=>S("created_at"),children:["Date","created_at"===P&&(0,a.jsx)("span",{className:m().sortIcon,children:"asc"===A?"↑":"↓"})]}),(0,a.jsxs)("th",{className:"".concat(m().sortableColumn," ").concat("total_amount"===P?m().activeSortColumn:""),onClick:()=>S("total_amount"),children:["Total","total_amount"===P&&(0,a.jsx)("span",{className:m().sortIcon,children:"asc"===A?"↑":"↓"})]}),(0,a.jsxs)("th",{className:"".concat(m().sortableColumn," ").concat("status"===P?m().activeSortColumn:""),onClick:()=>S("status"),children:["Status","status"===P&&(0,a.jsx)("span",{className:m().sortIcon,children:"asc"===A?"↑":"↓"})]}),(0,a.jsx)("th",{children:"Actions"})]})}),(0,a.jsx)("tbody",{children:t.length>0?t.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{children:e.order_number}),(0,a.jsx)("td",{children:e.customer_name}),(0,a.jsx)("td",{children:w(e.created_at)}),(0,a.jsxs)("td",{children:["$",e.total_amount.toFixed(2)]}),(0,a.jsx)("td",{children:(0,a.jsx)("span",{className:"".concat(m().statusBadge," ").concat(y(e.status)),children:e.status})}),(0,a.jsxs)("td",{className:m().actions,children:[(0,a.jsx)(l(),{href:"/admin/orders/".concat(e.id),className:m().actionButton,children:"View"}),(0,a.jsxs)("div",{className:m().statusDropdown,children:[(0,a.jsx)("button",{className:"".concat(m().actionButton," ").concat(m().updateButton),children:"Update Status"}),(0,a.jsxs)("div",{className:m().dropdownContent,children:[(0,a.jsx)("button",{onClick:()=>k(e.id,"pending"),className:"pending"===e.status?m().activeStatus:"",children:"Pending"}),(0,a.jsx)("button",{onClick:()=>k(e.id,"processing"),className:"processing"===e.status?m().activeStatus:"",children:"Processing"}),(0,a.jsx)("button",{onClick:()=>k(e.id,"shipped"),className:"shipped"===e.status?m().activeStatus:"",children:"Shipped"}),(0,a.jsx)("button",{onClick:()=>k(e.id,"delivered"),className:"delivered"===e.status?m().activeStatus:"",children:"Delivered"}),(0,a.jsx)("button",{onClick:()=>k(e.id,"cancelled"),className:"cancelled"===e.status?m().activeStatus:"",children:"Cancelled"})]})]})]})]},e.id)):(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:"6",className:m().noData,children:"No orders found"})})})]})}),(0,a.jsxs)("div",{className:m().pagination,children:[(0,a.jsx)("button",{onClick:()=>B(1),disabled:1===v,className:m().paginationButton,children:"First"}),(0,a.jsx)("button",{onClick:()=>B(v-1),disabled:1===v,className:m().paginationButton,children:"Previous"}),(0,a.jsxs)("span",{className:m().pageInfo,children:["Page ",v," of ",b]}),(0,a.jsx)("button",{onClick:()=>B(v+1),disabled:v===b,className:m().paginationButton,children:"Next"}),(0,a.jsx)("button",{onClick:()=>B(b),disabled:v===b,className:m().paginationButton,children:"Last"})]})]})]})]})}},60506:function(e){e.exports={container:"AdminPages_container__nj6XB",pageHeader:"AdminPages_pageHeader__LHSBG",addButton:"AdminPages_addButton__3YV2v",filterBar:"AdminPages_filterBar__fJxVO",filterGroup:"AdminPages_filterGroup__CvemU",select:"AdminPages_select__5v_Ue",searchForm:"AdminPages_searchForm__DxHvp",searchInput:"AdminPages_searchInput__czn7m",searchButton:"AdminPages_searchButton__HrKq3",tableContainer:"AdminPages_tableContainer__EZ3Ac",table:"AdminPages_table__KVh8N",sortableColumn:"AdminPages_sortableColumn___99vU",activeSortColumn:"AdminPages_activeSortColumn__pPIua",sortIcon:"AdminPages_sortIcon__tiBuk",imageCell:"AdminPages_imageCell__ahCj4",imageColumn:"AdminPages_imageColumn__m3_De",productImage:"AdminPages_productImage__8KMQy",stockBadge:"AdminPages_stockBadge__Uquo4",inStock:"AdminPages_inStock__4D2jV",lowStock:"AdminPages_lowStock__JQmaX",outOfStock:"AdminPages_outOfStock__PVOkV",stockCount:"AdminPages_stockCount__CJVuo",actions:"AdminPages_actions__5NUWv",actionButton:"AdminPages_actionButton__A25EK",editButton:"AdminPages_editButton__xhIdN",deleteButton:"AdminPages_deleteButton__wFOYO",ordersButton:"AdminPages_ordersButton__nuIHY",updateButton:"AdminPages_updateButton__TKzfU",statusBadge:"AdminPages_statusBadge__vf_aR",statusPending:"AdminPages_statusPending__uo129",statusProcessing:"AdminPages_statusProcessing__mabVA",statusShipped:"AdminPages_statusShipped__Jc_6I",statusDelivered:"AdminPages_statusDelivered__falDE",statusCancelled:"AdminPages_statusCancelled__fxl2i",statusDropdown:"AdminPages_statusDropdown__FTpQA",dropdownContent:"AdminPages_dropdownContent__4sAyN",activeStatus:"AdminPages_activeStatus__rh9Cs",noData:"AdminPages_noData__Mc9eU",pagination:"AdminPages_pagination__1uxxQ",paginationButton:"AdminPages_paginationButton__kiY8c",pageInfo:"AdminPages_pageInfo__XD0jp",loading:"AdminPages_loading__zLU2R",spinner:"AdminPages_spinner__lVOQO",spin:"AdminPages_spin__s6LXq",settingsContainer:"AdminPages_settingsContainer__yidTp",settingsTabs:"AdminPages_settingsTabs__Iw9Zr",tabButton:"AdminPages_tabButton__Swz33",activeTab:"AdminPages_activeTab___YGm8",settingsContent:"AdminPages_settingsContent__5zi5i",settingsForm:"AdminPages_settingsForm__6w8pE",formGroup:"AdminPages_formGroup__MbgWF",input:"AdminPages_input__NjzSf",textarea:"AdminPages_textarea__zm7h5",checkboxLabel:"AdminPages_checkboxLabel__QnbQ4",helpText:"AdminPages_helpText__6QXDW",formRow:"AdminPages_formRow__vrK66",formActions:"AdminPages_formActions__6_1in",saveButton:"AdminPages_saveButton__aoM8K",errorMessage:"AdminPages_errorMessage__CBbWX",successMessage:"AdminPages_successMessage__42J_F",shippingRate:"AdminPages_shippingRate__4pZsF",removeButton:"AdminPages_removeButton__k0hN8",paymentSection:"AdminPages_paymentSection___O13P",securitySection:"AdminPages_securitySection__lvIFb",testButton:"AdminPages_testButton__Xov_6"}},11163:function(e,t,s){e.exports=s(43079)}},function(e){e.O(0,[1664,2575,2888,9774,179],function(){return e(e.s=76376)}),_N_E=e.O()}]);