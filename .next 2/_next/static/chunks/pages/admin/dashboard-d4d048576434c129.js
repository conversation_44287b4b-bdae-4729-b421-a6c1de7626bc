(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9189,2098],{72833:function(e,s,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/dashboard",function(){return n(42985)}])},42985:function(e,s,n){"use strict";n.r(s),n.d(s,{__N_SSP:function(){return u},default:function(){return j}});var t=n(85893),i=n(67294),a=n(33299),r=n(11163),o=n(9008),c=n.n(o),l=n(41664),d=n.n(l),m=n(72755),h=n(3623),p=n.n(h),_=()=>{let[e,s]=(0,i.useState)(null),[n,a]=(0,i.useState)(!1),[r,o]=(0,i.useState)(0),[c,l]=(0,i.useState)(null),[d,m]=(0,i.useState)("4seller"),[h,_]=(0,i.useState)({sku:"sku",name:"title",description:"description",price:"price",stock:"quantity",category:"category",brand:"brand",images:"image_url"}),[v,x]=(0,i.useState)(null),[u,j]=(0,i.useState)(1),g=e=>{let s=e.split("\n");return s.length<2?null:{headers:s[0].split(",").map(e=>e.trim()),sample:s[1].split(",").map(e=>e.trim()),totalRows:s.length-1}},I=(e,s)=>{_({...h,[e]:s})},y=async()=>{if(!e)return;a(!0),o(0);let s=new FormData;s.append("file",e),s.append("source",d),s.append("mapping",JSON.stringify(h));try{let e=setInterval(()=>{o(s=>{let n=s+10;return n>=90?(clearInterval(e),90):n})},300),n=await fetch("/api/admin/import-inventory",{method:"POST",body:s});if(clearInterval(e),o(100),!n.ok)throw Error("Upload failed");let t=await n.json();l(t),j(3)}catch(e){console.error("Error uploading inventory:",e),l({success:!1,message:e.message||"Failed to upload inventory data"})}finally{a(!1)}};return(0,t.jsxs)("div",{className:p().importContainer,children:[(0,t.jsxs)("div",{className:p().importHeader,children:[(0,t.jsx)("h2",{children:"Import Inventory"}),(0,t.jsx)("p",{children:"Import your inventory data from 4seller.com or other sources"})]}),(0,t.jsxs)("div",{className:p().stepIndicator,children:[(0,t.jsxs)("div",{className:"".concat(p().step," ").concat(u>=1?p().active:""),children:[(0,t.jsx)("div",{className:p().stepNumber,children:"1"}),(0,t.jsx)("div",{className:p().stepLabel,children:"Upload File"})]}),(0,t.jsx)("div",{className:p().stepConnector}),(0,t.jsxs)("div",{className:"".concat(p().step," ").concat(u>=2?p().active:""),children:[(0,t.jsx)("div",{className:p().stepNumber,children:"2"}),(0,t.jsx)("div",{className:p().stepLabel,children:"Map Fields"})]}),(0,t.jsx)("div",{className:p().stepConnector}),(0,t.jsxs)("div",{className:"".concat(p().step," ").concat(u>=3?p().active:""),children:[(0,t.jsx)("div",{className:p().stepNumber,children:"3"}),(0,t.jsx)("div",{className:p().stepLabel,children:"Confirmation"})]})]}),(0,t.jsxs)("div",{className:p().importContent,children:[1===u&&(0,t.jsxs)("div",{className:p().uploadStep,children:[(0,t.jsxs)("div",{className:p().sourceSelector,children:[(0,t.jsx)("h3",{children:"Select Import Source"}),(0,t.jsxs)("div",{className:p().sourceOptions,children:[(0,t.jsxs)("label",{className:"".concat(p().sourceOption," ").concat("4seller"===d?p().selected:""),children:[(0,t.jsx)("input",{type:"radio",name:"importSource",value:"4seller",checked:"4seller"===d,onChange:()=>m("4seller")}),(0,t.jsx)("div",{className:p().sourceIcon,children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,t.jsx)("path",{d:"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"})})}),(0,t.jsxs)("div",{className:p().sourceInfo,children:[(0,t.jsx)("h4",{children:"4seller.com"}),(0,t.jsx)("p",{children:"Import inventory from 4seller.com exported CSV"})]})]}),(0,t.jsxs)("label",{className:"".concat(p().sourceOption," ").concat("csv"===d?p().selected:""),children:[(0,t.jsx)("input",{type:"radio",name:"importSource",value:"csv",checked:"csv"===d,onChange:()=>m("csv")}),(0,t.jsx)("div",{className:p().sourceIcon,children:(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),(0,t.jsx)("polyline",{points:"14 2 14 8 20 8"}),(0,t.jsx)("line",{x1:"16",y1:"13",x2:"8",y2:"13"}),(0,t.jsx)("line",{x1:"16",y1:"17",x2:"8",y2:"17"}),(0,t.jsx)("polyline",{points:"10 9 9 9 8 9"})]})}),(0,t.jsxs)("div",{className:p().sourceInfo,children:[(0,t.jsx)("h4",{children:"Generic CSV"}),(0,t.jsx)("p",{children:"Import from any CSV file with custom field mapping"})]})]}),(0,t.jsxs)("label",{className:"".concat(p().sourceOption," ").concat("excel"===d?p().selected:""),children:[(0,t.jsx)("input",{type:"radio",name:"importSource",value:"excel",checked:"excel"===d,onChange:()=>m("excel")}),(0,t.jsx)("div",{className:p().sourceIcon,children:(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),(0,t.jsx)("polyline",{points:"14 2 14 8 20 8"}),(0,t.jsx)("line",{x1:"16",y1:"13",x2:"8",y2:"13"}),(0,t.jsx)("line",{x1:"16",y1:"17",x2:"8",y2:"17"}),(0,t.jsx)("polyline",{points:"10 9 9 9 8 9"})]})}),(0,t.jsxs)("div",{className:p().sourceInfo,children:[(0,t.jsx)("h4",{children:"Excel Spreadsheet"}),(0,t.jsx)("p",{children:"Import from Excel (.xlsx) file with custom field mapping"})]})]})]})]}),(0,t.jsxs)("div",{className:p().fileUpload,children:[(0,t.jsx)("h3",{children:"Upload File"}),(0,t.jsxs)("div",{className:p().uploadArea,children:[(0,t.jsx)("input",{type:"file",id:"inventory-file",accept:".csv,.xlsx,.xls",onChange:e=>{let n=e.target.files[0];if(s(n),l(null),n&&"text/csv"===n.type){let e=new FileReader;e.onload=e=>{x(g(e.target.result))},e.readAsText(n)}},className:p().fileInput}),(0,t.jsxs)("label",{htmlFor:"inventory-file",className:p().fileLabel,children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}),(0,t.jsx)("polyline",{points:"17 8 12 3 7 8"}),(0,t.jsx)("line",{x1:"12",y1:"3",x2:"12",y2:"15"})]}),(0,t.jsx)("span",{children:e?e.name:"Choose a file or drag it here"})]}),(0,t.jsx)("p",{className:p().fileHint,children:"Supported formats: CSV, Excel (.xlsx, .xls)"})]})]}),v&&(0,t.jsxs)("div",{className:p().filePreview,children:[(0,t.jsx)("h3",{children:"File Preview"}),(0,t.jsxs)("p",{children:["Found ",v.totalRows," products in the file"]}),(0,t.jsxs)("div",{className:p().previewTable,children:[(0,t.jsx)("div",{className:p().previewHeader,children:v.headers.map((e,s)=>(0,t.jsx)("div",{className:p().previewCell,children:e},s))}),(0,t.jsx)("div",{className:p().previewRow,children:v.sample.map((e,s)=>(0,t.jsx)("div",{className:p().previewCell,children:e},s))})]})]})]}),2===u&&(0,t.jsxs)("div",{className:p().mappingStep,children:[(0,t.jsx)("h3",{children:"Map Fields"}),(0,t.jsx)("p",{children:"Match the fields from your file to our system fields"}),v&&(0,t.jsxs)("div",{className:p().mappingTable,children:[(0,t.jsxs)("div",{className:p().mappingHeader,children:[(0,t.jsx)("div",{className:p().mappingField,children:"System Field"}),(0,t.jsx)("div",{className:p().mappingSource,children:"Source Field"}),(0,t.jsx)("div",{className:p().mappingPreview,children:"Preview"})]}),Object.entries(h).map((e,s)=>{let[n,i]=e,a=v.headers.findIndex(e=>e.toLowerCase()===i.toLowerCase()),r=-1!==a?v.sample[a]:"";return(0,t.jsxs)("div",{className:p().mappingRow,children:[(0,t.jsx)("div",{className:p().mappingField,children:(0,t.jsx)("label",{htmlFor:"field-".concat(n),children:n.charAt(0).toUpperCase()+n.slice(1)})}),(0,t.jsx)("div",{className:p().mappingSource,children:(0,t.jsxs)("select",{id:"field-".concat(n),value:i,onChange:e=>I(n,e.target.value),children:[(0,t.jsx)("option",{value:"",children:"-- Not Mapped --"}),v.headers.map((e,s)=>(0,t.jsx)("option",{value:e,children:e},s))]})}),(0,t.jsx)("div",{className:p().mappingPreview,children:r||"-"})]},n)})]})]}),3===u&&(0,t.jsx)("div",{className:p().confirmationStep,children:n?(0,t.jsxs)("div",{className:p().uploadProgress,children:[(0,t.jsx)("div",{className:p().progressBar,children:(0,t.jsx)("div",{className:p().progressFill,style:{width:"".concat(r,"%")}})}),(0,t.jsxs)("div",{className:p().progressText,children:["Uploading... ",r,"%"]})]}):c?(0,t.jsxs)("div",{className:"".concat(p().uploadResult," ").concat(c.success?p().success:p().error),children:[(0,t.jsx)("div",{className:p().resultIcon,children:c.success?(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}),(0,t.jsx)("polyline",{points:"22 4 12 14.01 9 11.01"})]}):(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,t.jsx)("line",{x1:"15",y1:"9",x2:"9",y2:"15"}),(0,t.jsx)("line",{x1:"9",y1:"9",x2:"15",y2:"15"})]})}),(0,t.jsx)("h3",{children:c.success?"Import Successful":"Import Failed"}),(0,t.jsx)("p",{children:c.message}),c.success&&c.stats&&(0,t.jsxs)("div",{className:p().importStats,children:[(0,t.jsxs)("div",{className:p().statItem,children:[(0,t.jsx)("div",{className:p().statLabel,children:"Products Imported"}),(0,t.jsx)("div",{className:p().statValue,children:c.stats.imported})]}),(0,t.jsxs)("div",{className:p().statItem,children:[(0,t.jsx)("div",{className:p().statLabel,children:"Products Updated"}),(0,t.jsx)("div",{className:p().statValue,children:c.stats.updated})]}),(0,t.jsxs)("div",{className:p().statItem,children:[(0,t.jsx)("div",{className:p().statLabel,children:"Products Skipped"}),(0,t.jsx)("div",{className:p().statValue,children:c.stats.skipped})]}),(0,t.jsxs)("div",{className:p().statItem,children:[(0,t.jsx)("div",{className:p().statLabel,children:"Errors"}),(0,t.jsx)("div",{className:p().statValue,children:c.stats.errors})]})]})]}):(0,t.jsxs)("div",{className:p().confirmationMessage,children:[(0,t.jsx)("h3",{children:"Ready to Import"}),(0,t.jsx)("p",{children:"Please confirm to start importing your inventory data"})]})})]}),(0,t.jsxs)("div",{className:p().importActions,children:[u>1&&(0,t.jsx)("button",{className:p().backButton,onClick:()=>{u>1&&j(u-1)},disabled:n,children:"Back"}),(0,t.jsx)("button",{className:p().nextButton,onClick:()=>{1===u&&e?j(2):2===u?y():3===u&&(s(null),x(null),l(null),j(1))},disabled:1===u&&!e||n||3===u&&!c,children:1===u?"Next":2===u?"Import":"Done"})]})]})},v=n(15748),x=n.n(v),u=!0;function j(){let{data:e,status:s}=(0,a.useSession)(),n="loading"===s,o=(0,r.useRouter)(),[l,h]=(0,i.useState)("overview");(0,i.useEffect)(()=>{n||e&&e.user.isAdmin||o.push("/auth/signin?callbackUrl=/admin")},[e,n,o]);let p=e=>{h(e)};return n||!e?(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"loading-container",children:[(0,t.jsx)("div",{className:"loading-spinner"}),(0,t.jsx)("p",{children:"Loading dashboard..."})]})}):e.user.isAdmin?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(c(),{children:[(0,t.jsx)("title",{children:"Admin Dashboard | MDTS - Midas Technical Solutions"}),(0,t.jsx)("meta",{name:"description",content:"Admin dashboard for MDTS - Midas Technical Solutions"})]}),(0,t.jsxs)("div",{className:"container",children:[(0,t.jsxs)("div",{className:x().adminHeader,children:[(0,t.jsx)("h1",{children:"Admin Dashboard"}),(0,t.jsxs)("p",{children:["Welcome, ",e.user.name||e.user.email,"!"]})]}),(0,t.jsxs)("div",{className:x().adminDashboard,children:[(0,t.jsx)("div",{className:x().adminSidebar,children:(0,t.jsxs)("nav",{className:x().adminNav,children:[(0,t.jsxs)("div",{className:"".concat(x().navItem," ").concat("overview"===l?x().active:""),onClick:()=>p("overview"),children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("rect",{x:"3",y:"3",width:"7",height:"7"}),(0,t.jsx)("rect",{x:"14",y:"3",width:"7",height:"7"}),(0,t.jsx)("rect",{x:"14",y:"14",width:"7",height:"7"}),(0,t.jsx)("rect",{x:"3",y:"14",width:"7",height:"7"})]}),"Dashboard"]}),(0,t.jsxs)("div",{className:"".concat(x().navItem," ").concat("products"===l?x().active:""),onClick:()=>p("products"),children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"}),(0,t.jsx)("line",{x1:"3",y1:"6",x2:"21",y2:"6"}),(0,t.jsx)("path",{d:"M16 10a4 4 0 0 1-8 0"})]}),"Products"]}),(0,t.jsxs)("div",{className:"".concat(x().navItem," ").concat("inventory"===l?x().active:""),onClick:()=>p("inventory"),children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"}),(0,t.jsx)("polyline",{points:"3.27 6.96 12 12.01 20.73 6.96"}),(0,t.jsx)("line",{x1:"12",y1:"22.08",x2:"12",y2:"12"})]}),"Inventory"]}),(0,t.jsxs)("div",{className:"".concat(x().navItem," ").concat("orders"===l?x().active:""),onClick:()=>p("orders"),children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),(0,t.jsx)("polyline",{points:"14 2 14 8 20 8"}),(0,t.jsx)("line",{x1:"16",y1:"13",x2:"8",y2:"13"}),(0,t.jsx)("line",{x1:"16",y1:"17",x2:"8",y2:"17"}),(0,t.jsx)("polyline",{points:"10 9 9 9 8 9"})]}),"Orders"]}),(0,t.jsxs)("div",{className:"".concat(x().navItem," ").concat("customers"===l?x().active:""),onClick:()=>p("customers"),children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),(0,t.jsx)("circle",{cx:"12",cy:"7",r:"4"})]}),"Customers"]}),(0,t.jsxs)("div",{className:"".concat(x().navItem," ").concat("marketplaces"===l?x().active:""),onClick:()=>p("marketplaces"),children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("rect",{x:"2",y:"3",width:"20",height:"14",rx:"2",ry:"2"}),(0,t.jsx)("line",{x1:"8",y1:"21",x2:"16",y2:"21"}),(0,t.jsx)("line",{x1:"12",y1:"17",x2:"12",y2:"21"})]}),"Marketplaces"]}),(0,t.jsxs)("div",{className:"".concat(x().navItem," ").concat("settings"===l?x().active:""),onClick:()=>p("settings"),children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("circle",{cx:"12",cy:"12",r:"3"}),(0,t.jsx)("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"})]}),"Settings"]})]})}),(0,t.jsxs)("div",{className:x().adminContent,children:["overview"===l&&(0,t.jsxs)("div",{className:x().adminOverview,children:[(0,t.jsx)("h2",{children:"Dashboard Overview"}),(0,t.jsxs)("div",{className:x().statsGrid,children:[(0,t.jsxs)("div",{className:x().statCard,children:[(0,t.jsx)("h3",{children:"Total Orders"}),(0,t.jsx)("div",{className:x().statValue,children:"128"}),(0,t.jsx)("div",{className:"".concat(x().statChange," ").concat(x().positive),children:"+12% from last month"})]}),(0,t.jsxs)("div",{className:x().statCard,children:[(0,t.jsx)("h3",{children:"Total Revenue"}),(0,t.jsx)("div",{className:x().statValue,children:"$12,458.75"}),(0,t.jsx)("div",{className:"".concat(x().statChange," ").concat(x().positive),children:"+8% from last month"})]}),(0,t.jsxs)("div",{className:x().statCard,children:[(0,t.jsx)("h3",{children:"Total Customers"}),(0,t.jsx)("div",{className:x().statValue,children:"543"}),(0,t.jsx)("div",{className:"".concat(x().statChange," ").concat(x().positive),children:"+5% from last month"})]}),(0,t.jsxs)("div",{className:x().statCard,children:[(0,t.jsx)("h3",{children:"Conversion Rate"}),(0,t.jsx)("div",{className:x().statValue,children:"3.2%"}),(0,t.jsx)("div",{className:"".concat(x().statChange," ").concat(x().negative),children:"-0.5% from last month"})]})]}),(0,t.jsxs)("div",{className:x().recentActivity,children:[(0,t.jsx)("h3",{children:"Recent Activity"}),(0,t.jsxs)("div",{className:x().activityList,children:[(0,t.jsxs)("div",{className:x().activityItem,children:[(0,t.jsx)("div",{className:x().activityIcon,children:(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"}),(0,t.jsx)("line",{x1:"3",y1:"6",x2:"21",y2:"6"}),(0,t.jsx)("path",{d:"M16 10a4 4 0 0 1-8 0"})]})}),(0,t.jsxs)("div",{className:x().activityContent,children:[(0,t.jsx)("h4",{children:"New Order #1234"}),(0,t.jsx)("p",{children:"John Doe purchased iPhone 13 Pro Screen"}),(0,t.jsx)("div",{className:x().activityTime,children:"2 hours ago"})]})]}),(0,t.jsxs)("div",{className:x().activityItem,children:[(0,t.jsx)("div",{className:x().activityIcon,children:(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),(0,t.jsx)("circle",{cx:"12",cy:"7",r:"4"})]})}),(0,t.jsxs)("div",{className:x().activityContent,children:[(0,t.jsx)("h4",{children:"New Customer"}),(0,t.jsx)("p",{children:"Jane Smith created an account"}),(0,t.jsx)("div",{className:x().activityTime,children:"3 hours ago"})]})]}),(0,t.jsxs)("div",{className:x().activityItem,children:[(0,t.jsx)("div",{className:x().activityIcon,children:(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,t.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),(0,t.jsx)("polyline",{points:"14 2 14 8 20 8"}),(0,t.jsx)("line",{x1:"16",y1:"13",x2:"8",y2:"13"}),(0,t.jsx)("line",{x1:"16",y1:"17",x2:"8",y2:"17"}),(0,t.jsx)("polyline",{points:"10 9 9 9 8 9"})]})}),(0,t.jsxs)("div",{className:x().activityContent,children:[(0,t.jsx)("h4",{children:"Order Status Updated"}),(0,t.jsx)("p",{children:"Order #1230 changed from Processing to Shipped"}),(0,t.jsx)("div",{className:x().activityTime,children:"5 hours ago"})]})]})]})]})]}),"marketplaces"===l&&(0,t.jsxs)("div",{className:x().marketplacesTab,children:[(0,t.jsx)("h2",{children:"Marketplace Integrations"}),(0,t.jsx)("p",{className:x().tabDescription,children:"Connect and manage your marketplace accounts to synchronize inventory, orders, and listings."}),(0,t.jsx)(m.MarketplaceIntegration,{})]}),"products"===l&&(0,t.jsxs)("div",{className:x().productsTab,children:[(0,t.jsx)("h2",{children:"Products Management"}),(0,t.jsx)("p",{className:x().tabDescription,children:"This section is under development. Check back soon for product management features."})]}),"inventory"===l&&(0,t.jsxs)("div",{className:x().inventoryTab,children:[(0,t.jsx)("h2",{children:"Inventory Management"}),(0,t.jsx)("p",{className:x().tabDescription,children:"Import inventory data from 4seller.com or other sources, and manage your stock levels."}),(0,t.jsx)(_,{})]}),"orders"===l&&(0,t.jsxs)("div",{className:x().ordersTab,children:[(0,t.jsx)("h2",{children:"Orders Management"}),(0,t.jsx)("p",{className:x().tabDescription,children:"This section is under development. Check back soon for order management features."})]}),"customers"===l&&(0,t.jsxs)("div",{className:x().customersTab,children:[(0,t.jsx)("h2",{children:"Customers Management"}),(0,t.jsx)("p",{className:x().tabDescription,children:"This section is under development. Check back soon for customer management features."})]}),"settings"===l&&(0,t.jsxs)("div",{className:x().settingsTab,children:[(0,t.jsx)("h2",{children:"Admin Settings"}),(0,t.jsx)("p",{className:x().tabDescription,children:"This section is under development. Check back soon for admin settings features."})]})]})]})]})]}):(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"error-container",children:[(0,t.jsx)("h1",{children:"Access Denied"}),(0,t.jsx)("p",{children:"You do not have permission to access this page."}),(0,t.jsx)(d(),{href:"/",children:"Return to Home"})]})})}},3623:function(e){e.exports={importContainer:"InventoryImport_importContainer__KA5tx",importHeader:"InventoryImport_importHeader__ClNcA",stepIndicator:"InventoryImport_stepIndicator__Y_QXB",step:"InventoryImport_step__voP_K",stepNumber:"InventoryImport_stepNumber__oXR47",active:"InventoryImport_active__U_hl3",stepLabel:"InventoryImport_stepLabel__tvE60",stepConnector:"InventoryImport_stepConnector__QsxLw",importContent:"InventoryImport_importContent__tOEjX",confirmationStep:"InventoryImport_confirmationStep__jmmfI",mappingStep:"InventoryImport_mappingStep__2zOx_",uploadStep:"InventoryImport_uploadStep__4U61u",fadeIn:"InventoryImport_fadeIn__WLHv9",sourceSelector:"InventoryImport_sourceSelector__a9J1C",sourceOptions:"InventoryImport_sourceOptions__tcnub",sourceOption:"InventoryImport_sourceOption__F837m",selected:"InventoryImport_selected__JOX5m",sourceIcon:"InventoryImport_sourceIcon___kV5C",sourceInfo:"InventoryImport_sourceInfo__WkSCe",fileUpload:"InventoryImport_fileUpload__aKsxy",uploadArea:"InventoryImport_uploadArea__WdSF5",fileInput:"InventoryImport_fileInput__QQJC_",fileLabel:"InventoryImport_fileLabel__va_V4",fileHint:"InventoryImport_fileHint__HaK1f",filePreview:"InventoryImport_filePreview__Q8bBF",previewTable:"InventoryImport_previewTable__ylT7R",previewHeader:"InventoryImport_previewHeader__5MQlF",previewRow:"InventoryImport_previewRow__kFks1",previewCell:"InventoryImport_previewCell__xF6dQ",mappingTable:"InventoryImport_mappingTable__f5_Xt",mappingHeader:"InventoryImport_mappingHeader__0j5YD",mappingRow:"InventoryImport_mappingRow__hcdYx",mappingField:"InventoryImport_mappingField__RYYN8",mappingPreview:"InventoryImport_mappingPreview__cISP_",mappingSource:"InventoryImport_mappingSource__634pl",uploadProgress:"InventoryImport_uploadProgress__gLFRx",progressBar:"InventoryImport_progressBar__bfpKT",progressFill:"InventoryImport_progressFill__eLG6R",progressText:"InventoryImport_progressText__op9Bp",uploadResult:"InventoryImport_uploadResult__nRPaZ",success:"InventoryImport_success__Ub_0r",error:"InventoryImport_error__Wy1Of",resultIcon:"InventoryImport_resultIcon__xg1b1",importStats:"InventoryImport_importStats__of_Zz",statItem:"InventoryImport_statItem__tzKSA",statLabel:"InventoryImport_statLabel__x_nWr",statValue:"InventoryImport_statValue__kJplA",importActions:"InventoryImport_importActions__VXjVt",backButton:"InventoryImport_backButton__4mp7j",nextButton:"InventoryImport_nextButton__lya4c"}},15748:function(e){e.exports={adminContainer:"Admin_adminContainer__fnIiy",adminSidebar:"Admin_adminSidebar__ECUVT",adminLogo:"Admin_adminLogo__KRj8V",adminNav:"Admin_adminNav__6VV7e",adminNavItem:"Admin_adminNavItem__vGP0q",active:"Admin_active__BhXLC",adminContent:"Admin_adminContent__la1bh",adminHeader:"Admin_adminHeader__D8Awi",adminUser:"Admin_adminUser__9UhpZ",adminTabs:"Admin_adminTabs__cqvDR",adminTab:"Admin_adminTab__W_1Cu",activeTab:"Admin_activeTab__fvtdm",adminTabContent:"Admin_adminTabContent__2Pyru",loadingContainer:"Admin_loadingContainer__co6Jm",spinner:"Admin_spinner__UFXHG",spin:"Admin_spin__Ah3vg",statsGrid:"Admin_statsGrid__TB_yj",statCard:"Admin_statCard__TRCV8",statValue:"Admin_statValue__7vDB_",overviewCharts:"Admin_overviewCharts__CbCXN",chartCard:"Admin_chartCard__nhG5d",barChart:"Admin_barChart__P7yDX",barChartItem:"Admin_barChartItem__upy5l",barLabel:"Admin_barLabel__zg5pf",barContainer:"Admin_barContainer__rV5lJ",bar:"Admin_bar__KT2HJ",redBar:"Admin_redBar__Ux63t",barValue:"Admin_barValue__by0tg",tableContainer:"Admin_tableContainer__jS7Fw",dataTable:"Admin_dataTable__HJk6Z",actionButton:"Admin_actionButton__UMz0P",zeroResultsActions:"Admin_zeroResultsActions__a8LFP",recommendationsList:"Admin_recommendationsList__kt0vF",marketplacesTab:"Admin_marketplacesTab__F1B_G",tabDescription:"Admin_tabDescription__4232R",customersTab:"Admin_customersTab__K7_If",ordersTab:"Admin_ordersTab__nYVxi",productsTab:"Admin_productsTab__c7E0b",settingsTab:"Admin_settingsTab__uKxYF",adminSection:"Admin_adminSection__vTklz",sectionDescription:"Admin_sectionDescription__fkI2f",errorMessage:"Admin_errorMessage__X7vgq",successMessage:"Admin_successMessage__TfbaZ",integrationCards:"Admin_integrationCards__ahfcD",integrationCard:"Admin_integrationCard__UDVVJ",integrationHeader:"Admin_integrationHeader__ZpOSz",integrationLogo:"Admin_integrationLogo__L6Jp1",integrationInfo:"Admin_integrationInfo__xh2eJ",integrationStatus:"Admin_integrationStatus___Iy_i",statusIndicator:"Admin_statusIndicator__71aId",connected:"Admin_connected__7QFvd",disconnected:"Admin_disconnected__Awmdk",integrationBody:"Admin_integrationBody__DHwlP",integrationField:"Admin_integrationField__vCIAg",apiKeyField:"Admin_apiKeyField__Flra0",copyButton:"Admin_copyButton__jtW5y",databasesList:"Admin_databasesList__ANXzk",webhooksList:"Admin_webhooksList__4hKEU",databaseItem:"Admin_databaseItem__By6lR",webhookItem:"Admin_webhookItem__TtL6j",databaseInfo:"Admin_databaseInfo__5cusK",webhookInfo:"Admin_webhookInfo__rvvlE",databaseName:"Admin_databaseName__mbr33",webhookName:"Admin_webhookName__fRaXr",databaseId:"Admin_databaseId__QqduL",webhookUrl:"Admin_webhookUrl__6HqB3",switch:"Admin_switch__9nB1M",slider:"Admin_slider__GAnsH",connectContainer:"Admin_connectContainer__U5lly",connectButton:"Admin_connectButton__x3D79",disconnectButton:"Admin_disconnectButton__NA_DW",integrationHelp:"Admin_integrationHelp__oqKc1"}},11163:function(e,s,n){e.exports=n(43079)}},function(e){e.O(0,[1664,5675,2755,2888,9774,179],function(){return e(e.s=72833)}),_N_E=e.O()}]);