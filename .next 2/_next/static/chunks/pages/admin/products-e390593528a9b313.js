(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8209],{42678:function(e,t,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/products",function(){return a(67469)}])},67469:function(e,t,a){"use strict";a.r(t),a.d(t,{__N_SSP:function(){return g},default:function(){return p}});var s=a(85893),n=a(67294);a(25675);var o=a(33299),i=a(11163),c=a(9008),r=a.n(c),d=a(41664),l=a.n(d),_=a(2575),u=a(60506),m=a.n(u),g=!0;function p(){let{data:e}=(0,o.useSession)(),[t,a]=(0,n.useState)([]),[c,d]=(0,n.useState)(!0),[u,g]=(0,n.useState)("all"),[p,h]=(0,n.useState)(""),[P,A]=(0,n.useState)("name"),[x,j]=(0,n.useState)("asc"),[f,k]=(0,n.useState)(1),[N,C]=(0,n.useState)(1);async function S(){try{d(!0);let e=new URLSearchParams;e.append("filter",u),e.append("search",p),e.append("sortBy",P),e.append("sortOrder",x),e.append("page",f),e.append("limit",10);let t=await fetch("/api/admin/products?".concat(e.toString()));if(!t.ok)throw Error("Failed to fetch products");let s=await t.json();a(s.products),C(s.totalPages),d(!1)}catch(e){console.error("Error fetching products:",e),d(!1)}}(0,i.useRouter)(),(0,n.useEffect)(()=>{S()},[u,p,P,x,f]);let v=e=>{P===e?j("asc"===x?"desc":"asc"):(A(e),j("asc")),k(1)},B=e=>{e>=1&&e<=N&&k(e)},b=async e=>{if(window.confirm("Are you sure you want to delete this product?"))try{if(!(await fetch("/api/admin/products/".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete product");S()}catch(e){console.error("Error deleting product:",e),alert("Failed to delete product. Please try again.")}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(r(),{children:(0,s.jsx)("title",{children:"Products | Admin Dashboard"})}),(0,s.jsxs)(_.Z,{children:[(0,s.jsxs)("div",{className:m().pageHeader,children:[(0,s.jsx)("h1",{children:"Products"}),(0,s.jsx)(l(),{href:"/admin/products/new",className:m().addButton,children:"Add New Product"})]}),(0,s.jsxs)("div",{className:m().filterBar,children:[(0,s.jsxs)("div",{className:m().filterGroup,children:[(0,s.jsx)("label",{htmlFor:"filter",children:"Filter:"}),(0,s.jsxs)("select",{id:"filter",value:u,onChange:e=>{g(e.target.value),k(1)},className:m().select,children:[(0,s.jsx)("option",{value:"all",children:"All Products"}),(0,s.jsx)("option",{value:"in-stock",children:"In Stock"}),(0,s.jsx)("option",{value:"low-stock",children:"Low Stock"}),(0,s.jsx)("option",{value:"out-of-stock",children:"Out of Stock"}),(0,s.jsx)("option",{value:"featured",children:"Featured"}),(0,s.jsx)("option",{value:"new",children:"New Arrivals"})]})]}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),k(1),S()},className:m().searchForm,children:[(0,s.jsx)("input",{type:"text",placeholder:"Search products...",value:p,onChange:e=>{h(e.target.value)},className:m().searchInput}),(0,s.jsx)("button",{type:"submit",className:m().searchButton,children:"Search"})]})]}),c?(0,s.jsxs)("div",{className:m().loading,children:[(0,s.jsx)("div",{className:m().spinner}),(0,s.jsx)("p",{children:"Loading products..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:m().tableContainer,children:(0,s.jsxs)("table",{className:m().table,children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:m().imageColumn,children:"Image"}),(0,s.jsxs)("th",{className:"".concat(m().sortableColumn," ").concat("name"===P?m().activeSortColumn:""),onClick:()=>v("name"),children:["Product Name","name"===P&&(0,s.jsx)("span",{className:m().sortIcon,children:"asc"===x?"↑":"↓"})]}),(0,s.jsxs)("th",{className:"".concat(m().sortableColumn," ").concat("price"===P?m().activeSortColumn:""),onClick:()=>v("price"),children:["Price","price"===P&&(0,s.jsx)("span",{className:m().sortIcon,children:"asc"===x?"↑":"↓"})]}),(0,s.jsxs)("th",{className:"".concat(m().sortableColumn," ").concat("category"===P?m().activeSortColumn:""),onClick:()=>v("category"),children:["Category","category"===P&&(0,s.jsx)("span",{className:m().sortIcon,children:"asc"===x?"↑":"↓"})]}),(0,s.jsxs)("th",{className:"".concat(m().sortableColumn," ").concat("stock"===P?m().activeSortColumn:""),onClick:()=>v("stock"),children:["Stock","stock"===P&&(0,s.jsx)("span",{className:m().sortIcon,children:"asc"===x?"↑":"↓"})]}),(0,s.jsx)("th",{children:"Actions"})]})}),(0,s.jsx)("tbody",{children:t.length>0?t.map(e=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:m().imageCell,children:(0,s.jsx)("img",{src:e.image_url||"/images/placeholder.jpg",alt:e.name,className:m().productImage})}),(0,s.jsx)("td",{children:e.name}),(0,s.jsxs)("td",{children:["$",e.price.toFixed(2)]}),(0,s.jsx)("td",{children:e.category_name}),(0,s.jsxs)("td",{children:[(0,s.jsx)("span",{className:"".concat(m().stockBadge," ").concat(e.stock_quantity<=0?m().outOfStock:e.stock_quantity<=5?m().lowStock:m().inStock),children:e.stock_quantity<=0?"Out of Stock":e.stock_quantity<=5?"Low Stock":"In Stock"}),(0,s.jsxs)("span",{className:m().stockCount,children:["(",e.stock_quantity,")"]})]}),(0,s.jsxs)("td",{className:m().actions,children:[(0,s.jsx)(l(),{href:"/admin/products/".concat(e.id),className:m().actionButton,children:"View"}),(0,s.jsx)(l(),{href:"/admin/products/".concat(e.id,"/edit"),className:"".concat(m().actionButton," ").concat(m().editButton),children:"Edit"}),(0,s.jsx)("button",{onClick:()=>b(e.id),className:"".concat(m().actionButton," ").concat(m().deleteButton),children:"Delete"})]})]},e.id)):(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:"6",className:m().noData,children:"No products found"})})})]})}),(0,s.jsxs)("div",{className:m().pagination,children:[(0,s.jsx)("button",{onClick:()=>B(1),disabled:1===f,className:m().paginationButton,children:"First"}),(0,s.jsx)("button",{onClick:()=>B(f-1),disabled:1===f,className:m().paginationButton,children:"Previous"}),(0,s.jsxs)("span",{className:m().pageInfo,children:["Page ",f," of ",N]}),(0,s.jsx)("button",{onClick:()=>B(f+1),disabled:f===N,className:m().paginationButton,children:"Next"}),(0,s.jsx)("button",{onClick:()=>B(N),disabled:f===N,className:m().paginationButton,children:"Last"})]})]})]})]})}},60506:function(e){e.exports={container:"AdminPages_container__nj6XB",pageHeader:"AdminPages_pageHeader__LHSBG",addButton:"AdminPages_addButton__3YV2v",filterBar:"AdminPages_filterBar__fJxVO",filterGroup:"AdminPages_filterGroup__CvemU",select:"AdminPages_select__5v_Ue",searchForm:"AdminPages_searchForm__DxHvp",searchInput:"AdminPages_searchInput__czn7m",searchButton:"AdminPages_searchButton__HrKq3",tableContainer:"AdminPages_tableContainer__EZ3Ac",table:"AdminPages_table__KVh8N",sortableColumn:"AdminPages_sortableColumn___99vU",activeSortColumn:"AdminPages_activeSortColumn__pPIua",sortIcon:"AdminPages_sortIcon__tiBuk",imageCell:"AdminPages_imageCell__ahCj4",imageColumn:"AdminPages_imageColumn__m3_De",productImage:"AdminPages_productImage__8KMQy",stockBadge:"AdminPages_stockBadge__Uquo4",inStock:"AdminPages_inStock__4D2jV",lowStock:"AdminPages_lowStock__JQmaX",outOfStock:"AdminPages_outOfStock__PVOkV",stockCount:"AdminPages_stockCount__CJVuo",actions:"AdminPages_actions__5NUWv",actionButton:"AdminPages_actionButton__A25EK",editButton:"AdminPages_editButton__xhIdN",deleteButton:"AdminPages_deleteButton__wFOYO",ordersButton:"AdminPages_ordersButton__nuIHY",updateButton:"AdminPages_updateButton__TKzfU",statusBadge:"AdminPages_statusBadge__vf_aR",statusPending:"AdminPages_statusPending__uo129",statusProcessing:"AdminPages_statusProcessing__mabVA",statusShipped:"AdminPages_statusShipped__Jc_6I",statusDelivered:"AdminPages_statusDelivered__falDE",statusCancelled:"AdminPages_statusCancelled__fxl2i",statusDropdown:"AdminPages_statusDropdown__FTpQA",dropdownContent:"AdminPages_dropdownContent__4sAyN",activeStatus:"AdminPages_activeStatus__rh9Cs",noData:"AdminPages_noData__Mc9eU",pagination:"AdminPages_pagination__1uxxQ",paginationButton:"AdminPages_paginationButton__kiY8c",pageInfo:"AdminPages_pageInfo__XD0jp",loading:"AdminPages_loading__zLU2R",spinner:"AdminPages_spinner__lVOQO",spin:"AdminPages_spin__s6LXq",settingsContainer:"AdminPages_settingsContainer__yidTp",settingsTabs:"AdminPages_settingsTabs__Iw9Zr",tabButton:"AdminPages_tabButton__Swz33",activeTab:"AdminPages_activeTab___YGm8",settingsContent:"AdminPages_settingsContent__5zi5i",settingsForm:"AdminPages_settingsForm__6w8pE",formGroup:"AdminPages_formGroup__MbgWF",input:"AdminPages_input__NjzSf",textarea:"AdminPages_textarea__zm7h5",checkboxLabel:"AdminPages_checkboxLabel__QnbQ4",helpText:"AdminPages_helpText__6QXDW",formRow:"AdminPages_formRow__vrK66",formActions:"AdminPages_formActions__6_1in",saveButton:"AdminPages_saveButton__aoM8K",errorMessage:"AdminPages_errorMessage__CBbWX",successMessage:"AdminPages_successMessage__42J_F",shippingRate:"AdminPages_shippingRate__4pZsF",removeButton:"AdminPages_removeButton__k0hN8",paymentSection:"AdminPages_paymentSection___O13P",securitySection:"AdminPages_securitySection__lvIFb",testButton:"AdminPages_testButton__Xov_6"}},11163:function(e,t,a){e.exports=a(43079)}},function(e){e.O(0,[1664,5675,2575,2888,9774,179],function(){return e(e.s=42678)}),_N_E=e.O()}]);