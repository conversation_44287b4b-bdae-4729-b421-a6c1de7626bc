(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6359],{34444:function(e,t,o){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/zapier",function(){return o(6780)}])},6780:function(e,t,o){"use strict";o.r(t);var d=o(85893),r=o(67294),n=o(9008),i=o.n(n),s=o(2575),l=o(34155);t.default=()=>{let[e,t]=(0,r.useState)("not_configured"),[o,n]=(0,r.useState)("webhooks"),[a,p]=(0,r.useState)(!1),[c,x]=(0,r.useState)(""),[g,h]=(0,r.useState)([{id:"new_order",name:"New Order",url:"",enabled:!1,lastTriggered:null},{id:"low_inventory",name:"Low Inventory Alert",url:"",enabled:!1,lastTriggered:null},{id:"new_customer",name:"New Customer",url:"",enabled:!1,lastTriggered:null},{id:"abandoned_cart",name:"Abandoned Cart",url:"",enabled:!1,lastTriggered:null},{id:"product_review",name:"Product Review",url:"",enabled:!1,lastTriggered:null},{id:"support_request",name:"Support Request",url:"",enabled:!1,lastTriggered:null}]),[u,m]=(0,r.useState)([{id:"auto-1",name:"Order Confirmation Email",description:"Send an email when a new order is placed",trigger:"New Order",action:"Send Email",enabled:!0,lastRun:"2023-05-15 14:32:45"},{id:"auto-2",name:"Low Stock Notification",description:"Notify team when product stock is low",trigger:"Low Inventory Alert",action:"Send Slack Message",enabled:!0,lastRun:"2023-05-14 09:15:22"},{id:"auto-3",name:"Welcome Email Sequence",description:"Send welcome emails to new customers",trigger:"New Customer",action:"Add to Email Sequence",enabled:!1,lastRun:null},{id:"auto-4",name:"Abandoned Cart Recovery",description:"Send reminder email for abandoned carts",trigger:"Abandoned Cart",action:"Send Email",enabled:!0,lastRun:"2023-05-15 10:45:18"}]),[b,y]=(0,r.useState)([{id:"log-1",event:"New Order",status:"success",message:"Order #ORD-5289 processed successfully",timestamp:"2023-05-15 14:32:45"},{id:"log-2",event:"Low Inventory Alert",status:"success",message:'Low stock alert for "iPhone 13 Pro Screen" sent to Slack',timestamp:"2023-05-14 09:15:22"},{id:"log-3",event:"Abandoned Cart",status:"success",message:"Reminder email sent for cart #CART-1234",timestamp:"2023-05-15 10:45:18"},{id:"log-4",event:"Product Review",status:"error",message:"Failed to process review: Webhook returned 404",timestamp:"2023-05-13 16:22:10"}]);(0,r.useEffect)(()=>{(async()=>{let e=l.env.ZAPIER_WEBHOOK_NEW_ORDER&&l.env.ZAPIER_WEBHOOK_LOW_INVENTORY;t(e?"configured":"not_configured"),e&&h(e=>e.map(e=>({...e,url:l.env["ZAPIER_WEBHOOK_".concat(e.id.toUpperCase())]||"",enabled:!!l.env["ZAPIER_WEBHOOK_".concat(e.id.toUpperCase())]})))})()},[]);let j=(e,t,o)=>{h(d=>d.map(d=>d.id===e?{...d,[t]:o}:d))},w=async()=>{p(!0),x("");try{await new Promise(e=>setTimeout(e,1e3)),t("configured"),alert("Zapier webhooks saved successfully!")}catch(e){console.error("Error saving Zapier webhooks:",e),x("Failed to save Zapier webhooks")}finally{p(!1)}},f=async e=>{let t=g.find(t=>t.id===e);if(!t||!t.url){alert("Please enter a webhook URL first");return}p(!0);try{await new Promise(e=>setTimeout(e,1e3)),h(t=>t.map(t=>t.id===e?{...t,lastTriggered:new Date().toISOString()}:t));let o={id:"log-".concat(Date.now()),event:t.name,status:"success",message:"Test payload sent to ".concat(t.name," webhook"),timestamp:new Date().toLocaleString()};y(e=>[o,...e]),alert("Test payload sent to ".concat(t.name," webhook successfully!"))}catch(o){console.error("Error testing webhook:",o);let e={id:"log-".concat(Date.now()),event:t.name,status:"error",message:"Failed to send test payload: ".concat(o.message),timestamp:new Date().toLocaleString()};y(t=>[e,...t]),alert("Failed to test webhook: ".concat(o.message))}finally{p(!1)}},k=e=>{m(t=>t.map(t=>t.id===e?{...t,enabled:!t.enabled}:t))},v=()=>(0,d.jsxs)("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},children:[(0,d.jsx)("h2",{children:"Zapier Webhooks"}),(0,d.jsx)("p",{style:{marginBottom:"20px"},children:"Configure webhook URLs for different events to trigger Zapier automations."}),c&&(0,d.jsx)("div",{style:{backgroundColor:"#f8d7da",color:"#721c24",padding:"10px",borderRadius:"4px",marginBottom:"20px"},children:c}),(0,d.jsxs)("table",{style:{width:"100%",borderCollapse:"collapse",marginBottom:"20px"},children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Event"}),(0,d.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Webhook URL"}),(0,d.jsx)("th",{style:{textAlign:"center",padding:"10px",borderBottom:"1px solid #ddd"},children:"Enabled"}),(0,d.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Last Triggered"}),(0,d.jsx)("th",{style:{textAlign:"center",padding:"10px",borderBottom:"1px solid #ddd"},children:"Test"})]})}),(0,d.jsx)("tbody",{children:g.map(e=>(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:e.name}),(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:(0,d.jsx)("input",{type:"text",value:e.url,onChange:t=>j(e.id,"url",t.target.value),placeholder:"https://hooks.zapier.com/hooks/catch/...",style:{width:"100%",padding:"8px",border:"1px solid #ddd",borderRadius:"4px"}})}),(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd",textAlign:"center"},children:(0,d.jsx)("input",{type:"checkbox",checked:e.enabled,onChange:t=>j(e.id,"enabled",t.target.checked),style:{width:"18px",height:"18px"}})}),(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:e.lastTriggered?new Date(e.lastTriggered).toLocaleString():"Never"}),(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd",textAlign:"center"},children:(0,d.jsx)("button",{onClick:()=>f(e.id),disabled:!e.url||a,style:{backgroundColor:e.url?"#0066cc":"#f0f0f0",color:e.url?"white":"#999",border:"none",padding:"5px 10px",borderRadius:"4px",cursor:e.url&&!a?"pointer":"not-allowed"},children:"Test"})})]},e.id))})]}),(0,d.jsx)("div",{style:{display:"flex",justifyContent:"flex-end"},children:(0,d.jsx)("button",{onClick:w,disabled:a,style:{backgroundColor:"#0066cc",color:"white",border:"none",padding:"10px 20px",borderRadius:"4px",cursor:a?"not-allowed":"pointer",opacity:a?.7:1},children:a?"Saving...":"Save Webhooks"})})]});return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(i(),{children:[(0,d.jsx)("title",{children:"Zapier Automation | MDTS Tech Admin"}),(0,d.jsx)("meta",{name:"description",content:"Manage Zapier automations for MDTS Tech Store"})]}),(0,d.jsxs)(s.Z,{children:[(0,d.jsx)("h1",{children:"Zapier Automation"}),(0,d.jsx)("p",{style:{marginBottom:"30px"},children:"Connect your store to Zapier to automate workflows and integrate with other apps."}),"not_configured"===e?v():(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{style:{marginBottom:"30px"},children:(0,d.jsxs)("div",{style:{display:"flex",gap:"10px",borderBottom:"1px solid #ddd"},children:[(0,d.jsx)("button",{onClick:()=>n("webhooks"),style:{padding:"10px 20px",backgroundColor:"transparent",color:"#333",border:"none",borderBottom:"webhooks"===o?"2px solid #0066cc":"2px solid transparent",cursor:"pointer",fontWeight:"webhooks"===o?"500":"normal"},children:"Webhooks"}),(0,d.jsx)("button",{onClick:()=>n("automations"),style:{padding:"10px 20px",backgroundColor:"transparent",color:"#333",border:"none",borderBottom:"automations"===o?"2px solid #0066cc":"2px solid transparent",cursor:"pointer",fontWeight:"automations"===o?"500":"normal"},children:"Automations"}),(0,d.jsx)("button",{onClick:()=>n("logs"),style:{padding:"10px 20px",backgroundColor:"transparent",color:"#333",border:"none",borderBottom:"logs"===o?"2px solid #0066cc":"2px solid transparent",cursor:"pointer",fontWeight:"logs"===o?"500":"normal"},children:"Activity Logs"})]})}),"webhooks"===o&&v(),"automations"===o&&(0,d.jsxs)("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},children:[(0,d.jsx)("h2",{children:"Zapier Automations"}),(0,d.jsx)("p",{style:{marginBottom:"20px"},children:"Manage your Zapier automations that are connected to your store."}),(0,d.jsxs)("table",{style:{width:"100%",borderCollapse:"collapse",marginBottom:"20px"},children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Automation"}),(0,d.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Trigger"}),(0,d.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Action"}),(0,d.jsx)("th",{style:{textAlign:"center",padding:"10px",borderBottom:"1px solid #ddd"},children:"Status"}),(0,d.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Last Run"})]})}),(0,d.jsx)("tbody",{children:u.map(e=>(0,d.jsxs)("tr",{children:[(0,d.jsxs)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:[(0,d.jsx)("div",{style:{fontWeight:"500"},children:e.name}),(0,d.jsx)("div",{style:{fontSize:"0.85rem",color:"#666",marginTop:"3px"},children:e.description})]}),(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:e.trigger}),(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:e.action}),(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd",textAlign:"center"},children:(0,d.jsxs)("label",{className:"switch",style:{position:"relative",display:"inline-block",width:"50px",height:"24px"},children:[(0,d.jsx)("input",{type:"checkbox",checked:e.enabled,onChange:()=>k(e.id),style:{opacity:0,width:0,height:0}}),(0,d.jsx)("span",{style:{position:"absolute",cursor:"pointer",top:0,left:0,right:0,bottom:0,backgroundColor:e.enabled?"#2196F3":"#ccc",transition:".4s",borderRadius:"34px"},children:(0,d.jsx)("span",{style:{position:"absolute",content:'""',height:"16px",width:"16px",left:e.enabled?"30px":"4px",bottom:"4px",backgroundColor:"white",transition:".4s",borderRadius:"50%"}})})]})}),(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:e.lastRun||"Never"})]},e.id))})]}),(0,d.jsxs)("div",{style:{textAlign:"center",padding:"20px",backgroundColor:"#f9f9f9",borderRadius:"4px"},children:[(0,d.jsx)("p",{style:{marginBottom:"15px"},children:"Need to create a new automation?"}),(0,d.jsx)("a",{href:"https://zapier.com/app/editor/",target:"_blank",rel:"noopener noreferrer",style:{backgroundColor:"#0066cc",color:"white",border:"none",padding:"10px 20px",borderRadius:"4px",textDecoration:"none",display:"inline-block"},children:"Create Zap in Zapier"})]})]}),"logs"===o&&(0,d.jsxs)("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},children:[(0,d.jsx)("h2",{children:"Zapier Activity Logs"}),(0,d.jsx)("p",{style:{marginBottom:"20px"},children:"View recent activity and events triggered by your Zapier integrations."}),(0,d.jsxs)("table",{style:{width:"100%",borderCollapse:"collapse"},children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Timestamp"}),(0,d.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Event"}),(0,d.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Message"}),(0,d.jsx)("th",{style:{textAlign:"center",padding:"10px",borderBottom:"1px solid #ddd"},children:"Status"})]})}),(0,d.jsx)("tbody",{children:b.map(e=>(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd",whiteSpace:"nowrap"},children:e.timestamp}),(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:e.event}),(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:e.message}),(0,d.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd",textAlign:"center"},children:(0,d.jsx)("span",{style:{backgroundColor:"success"===e.status?"#d4edda":"#f8d7da",color:"success"===e.status?"#155724":"#721c24",padding:"3px 8px",borderRadius:"4px",fontSize:"0.85rem"},children:"success"===e.status?"Success":"Error"})})]},e.id))})]})]})]})]})]})}},11163:function(e,t,o){e.exports=o(43079)}},function(e){e.O(0,[1664,2575,2888,9774,179],function(){return e(e.s=34444)}),_N_E=e.O()}]);