(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6696],{37514:function(e,t,d){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/notion",function(){return d(57974)}])},57974:function(e,t,d){"use strict";d.r(t);var o=d(85893),r=d(67294),n=d(9008),i=d.n(n),x=d(2575),s=d(34155);t.default=()=>{let[e,t]=(0,r.useState)("products"),[d,n]=(0,r.useState)("not_configured"),[l,a]=(0,r.useState)([]),[p,c]=(0,r.useState)(!1),[g,h]=(0,r.useState)(""),[u,b]=(0,r.useState)({apiKey:"",productsDbId:"",ordersDbId:"",customersDbId:"",contentDbId:""});(0,r.useEffect)(()=>{(async()=>{let t=s.env.NOTION_API_KEY&&s.env.NOTION_PRODUCTS_DATABASE_ID;n(t?"configured":"not_configured"),t&&await m(e)})()},[e]);let m=async e=>{c(!0),h("");try{await new Promise(e=>setTimeout(e,1e3));let t=[];"products"===e?t=[{id:"prod-1",name:"iPhone 13 Pro Screen",price:149.99,category:"iPhone Parts",inStock:!0},{id:"prod-2",name:"Samsung Galaxy S21 Battery",price:49.99,category:"Samsung Parts",inStock:!0},{id:"prod-3",name:'iPad Pro 12.9" LCD',price:299.99,category:"iPad Parts",inStock:!1}]:"orders"===e?t=[{id:"ord-1",orderNumber:"ORD-5289",customer:"John Smith",total:245.99,status:"Delivered"},{id:"ord-2",orderNumber:"ORD-5288",customer:"Sarah Johnson",total:189.5,status:"Processing"}]:"content"===e&&(t=[{id:"blog-1",title:"How to Replace Your iPhone Screen",type:"Blog",publishDate:"2023-05-10"},{id:"faq-1",title:"Common Repair Questions",type:"FAQ",publishDate:"2023-05-05"}]),a(t)}catch(e){console.error("Error fetching Notion data:",e),h("Failed to fetch data from Notion")}finally{c(!1)}},y=e=>{let{name:t,value:d}=e.target;b(e=>({...e,[t]:d}))},f=async t=>{t.preventDefault(),c(!0),h("");try{await new Promise(e=>setTimeout(e,1e3)),n("configured"),await m(e),alert("Notion configuration saved successfully!")}catch(e){console.error("Error saving Notion configuration:",e),h("Failed to save Notion configuration")}finally{c(!1)}};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(i(),{children:[(0,o.jsx)("title",{children:"Notion Integration | MDTS Tech Admin"}),(0,o.jsx)("meta",{name:"description",content:"Manage Notion integration for MDTS Tech Store"})]}),(0,o.jsxs)(x.Z,{children:[(0,o.jsx)("h1",{children:"Notion Integration"}),(0,o.jsx)("p",{style:{marginBottom:"30px"},children:"Connect your Notion workspace to sync products, orders, and content."}),"not_configured"===d?(0,o.jsxs)("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},children:[(0,o.jsx)("h2",{children:"Configure Notion Integration"}),(0,o.jsx)("p",{style:{marginBottom:"20px"},children:"Enter your Notion API key and database IDs to connect your Notion workspace."}),g&&(0,o.jsx)("div",{style:{backgroundColor:"#f8d7da",color:"#721c24",padding:"10px",borderRadius:"4px",marginBottom:"20px"},children:g}),(0,o.jsxs)("form",{onSubmit:f,children:[(0,o.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,o.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Notion API Key"}),(0,o.jsx)("input",{type:"text",name:"apiKey",value:u.apiKey,onChange:y,required:!0,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"},placeholder:"secret_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"}),(0,o.jsxs)("p",{style:{fontSize:"0.8rem",color:"#666",marginTop:"5px"},children:["Get this from ",(0,o.jsx)("a",{href:"https://www.notion.so/my-integrations",target:"_blank",rel:"noopener noreferrer",children:"Notion Integrations"})]})]}),(0,o.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,o.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Products Database ID"}),(0,o.jsx)("input",{type:"text",name:"productsDbId",value:u.productsDbId,onChange:y,required:!0,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"},placeholder:"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"})]}),(0,o.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,o.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Orders Database ID"}),(0,o.jsx)("input",{type:"text",name:"ordersDbId",value:u.ordersDbId,onChange:y,required:!0,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"},placeholder:"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"})]}),(0,o.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,o.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Customers Database ID"}),(0,o.jsx)("input",{type:"text",name:"customersDbId",value:u.customersDbId,onChange:y,required:!0,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"},placeholder:"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"})]}),(0,o.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,o.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Content Database ID"}),(0,o.jsx)("input",{type:"text",name:"contentDbId",value:u.contentDbId,onChange:y,required:!0,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"},placeholder:"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"})]}),(0,o.jsx)("button",{type:"submit",disabled:p,style:{backgroundColor:"#0066cc",color:"white",border:"none",padding:"12px 20px",borderRadius:"4px",cursor:p?"not-allowed":"pointer",opacity:p?.7:1},children:p?"Saving...":"Save Configuration"})]})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[(0,o.jsxs)("div",{style:{display:"flex",gap:"10px"},children:[(0,o.jsx)("button",{onClick:()=>t("products"),style:{padding:"8px 16px",backgroundColor:"products"===e?"#0066cc":"#f0f0f0",color:"products"===e?"white":"#333",border:"none",borderRadius:"4px",cursor:"pointer"},children:"Products"}),(0,o.jsx)("button",{onClick:()=>t("orders"),style:{padding:"8px 16px",backgroundColor:"orders"===e?"#0066cc":"#f0f0f0",color:"orders"===e?"white":"#333",border:"none",borderRadius:"4px",cursor:"pointer"},children:"Orders"}),(0,o.jsx)("button",{onClick:()=>t("content"),style:{padding:"8px 16px",backgroundColor:"content"===e?"#0066cc":"#f0f0f0",color:"content"===e?"white":"#333",border:"none",borderRadius:"4px",cursor:"pointer"},children:"Content"})]}),(0,o.jsx)("button",{onClick:()=>n("not_configured"),style:{padding:"8px 16px",backgroundColor:"#f0f0f0",color:"#333",border:"none",borderRadius:"4px",cursor:"pointer"},children:"Reconfigure"})]}),p?(0,o.jsxs)("div",{style:{textAlign:"center",padding:"40px 0"},children:[(0,o.jsx)("div",{style:{width:"40px",height:"40px",border:"4px solid #f3f3f3",borderTop:"4px solid #0066cc",borderRadius:"50%",animation:"spin 1s linear infinite",margin:"0 auto 20px"}}),(0,o.jsx)("p",{children:"Loading data from Notion..."})]}):g?(0,o.jsx)("div",{style:{backgroundColor:"#f8d7da",color:"#721c24",padding:"15px",borderRadius:"4px",marginBottom:"20px"},children:g}):(0,o.jsxs)("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},children:[(0,o.jsxs)("h2",{children:["products"===e?"Products":"orders"===e?"Orders":"Content"," from Notion"]}),0===l.length?(0,o.jsx)("p",{style:{padding:"20px 0",textAlign:"center"},children:"No data found in Notion."}):(0,o.jsxs)("table",{style:{width:"100%",borderCollapse:"collapse",marginTop:"15px"},children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{children:["products"===e&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Name"}),(0,o.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Price"}),(0,o.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Category"}),(0,o.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"In Stock"})]}),"orders"===e&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Order #"}),(0,o.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Customer"}),(0,o.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Total"}),(0,o.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Status"})]}),"content"===e&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Title"}),(0,o.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Type"}),(0,o.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Published"})]}),(0,o.jsx)("th",{style:{textAlign:"left",padding:"10px",borderBottom:"1px solid #ddd"},children:"Actions"})]})}),(0,o.jsx)("tbody",{children:l.map(t=>(0,o.jsxs)("tr",{children:["products"===e&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:t.name}),(0,o.jsxs)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:["$",t.price.toFixed(2)]}),(0,o.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:t.category}),(0,o.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:(0,o.jsx)("span",{style:{backgroundColor:t.inStock?"#d4edda":"#f8d7da",color:t.inStock?"#155724":"#721c24",padding:"3px 8px",borderRadius:"4px",fontSize:"0.85rem"},children:t.inStock?"In Stock":"Out of Stock"})})]}),"orders"===e&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:t.orderNumber}),(0,o.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:t.customer}),(0,o.jsxs)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:["$",t.total.toFixed(2)]}),(0,o.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:(0,o.jsx)("span",{style:{backgroundColor:"Delivered"===t.status?"#d4edda":"Processing"===t.status?"#fff3cd":"Shipped"===t.status?"#cce5ff":"#f8d7da",color:"Delivered"===t.status?"#155724":"Processing"===t.status?"#856404":"Shipped"===t.status?"#004085":"#721c24",padding:"3px 8px",borderRadius:"4px",fontSize:"0.85rem"},children:t.status})})]}),"content"===e&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:t.title}),(0,o.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:t.type}),(0,o.jsx)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:new Date(t.publishDate).toLocaleDateString()})]}),(0,o.jsxs)("td",{style:{padding:"10px",borderBottom:"1px solid #ddd"},children:[(0,o.jsx)("button",{style:{backgroundColor:"#0066cc",color:"white",border:"none",padding:"5px 10px",borderRadius:"4px",cursor:"pointer",marginRight:"5px"},children:"View"}),(0,o.jsx)("button",{style:{backgroundColor:"#f0f0f0",color:"#333",border:"none",padding:"5px 10px",borderRadius:"4px",cursor:"pointer"},children:"Sync"})]})]},t.id))})]})]})]})]}),(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n        "}})]})}},11163:function(e,t,d){e.exports=d(43079)}},function(e){e.O(0,[1664,2575,2888,9774,179],function(){return e(e.s=37514)}),_N_E=e.O()}]);