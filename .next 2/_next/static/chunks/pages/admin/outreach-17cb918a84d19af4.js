(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1509],{6295:function(e,a,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/outreach",function(){return n(80045)}])},80045:function(e,a,n){"use strict";n.r(a),n.d(a,{default:function(){return q}});var s=n(85893),t=n(67294),i=n(11163),l=n(33299),r=n(2575),c=n(13536),d=n(84653),o=n(92137),h=n(50447),p=n(78738),x=n(65900),u=n(40176),m=n(43615),j=n(7620),Z=n(38174),g=n(44490),f=n(21449),b=n(76809),v=n(3742),y=n(85830),C=n(7638),w=n(99366),S=n(251),k=n(3239),_=n(39674),E=n(10788),N=n(63809),W=n(88563),O=n(65963),I=n(33476),P=n(35885),D=n(56796),T=n(40454),z=n(99983),L=n(89308),A=n(58548);let F={email:(0,s.jsx)(O.Z,{}),whatsapp:(0,s.jsx)(I.Z,{}),linkedin:(0,s.jsx)(P.Z,{}),facebook:(0,s.jsx)(D.Z,{}),instagram:(0,s.jsx)(T.Z,{}),telegram:(0,s.jsx)(z.Z,{})},R={draft:"default",scheduled:"primary",in_progress:"info",paused:"warning",stopped:"error",completed:"success"};function q(){let{data:e,status:a}=(0,l.useSession)(),n=(0,i.useRouter)(),[O,I]=(0,t.useState)([]),[P,D]=(0,t.useState)(!0),[T,z]=(0,t.useState)(1),[q,J]=(0,t.useState)(1),[U,V]=(0,t.useState)(""),[X,B]=(0,t.useState)(""),[G,H]=(0,t.useState)(!1),[K,M]=(0,t.useState)({name:"",description:"",channels:[]});(0,t.useEffect)(()=>{"authenticated"===a&&Q()},[a,T,U,X]),(0,t.useEffect)(()=>{"unauthenticated"===a&&n.push("/signin?callbackUrl=/admin/outreach")},[a,n]);let Q=async()=>{try{D(!0);let e=new URLSearchParams;e.append("page",T),e.append("limit",12),U&&e.append("search",U),X&&e.append("status",X);let a=await fetch("/api/outreach/campaigns?".concat(e.toString()));if(!a.ok)throw Error("Failed to fetch campaigns");let n=await a.json();I(n.campaigns),J(n.pagination.totalPages)}catch(e){console.error("Error fetching campaigns:",e)}finally{D(!1)}},Y=()=>{H(!1),M({name:"",description:"",channels:[]})},$=e=>{let{name:a,value:n}=e.target;M(e=>({...e,[a]:n}))},ee=async()=>{try{if(!K.name||0===K.channels.length)return;let e=await fetch("/api/outreach/campaigns",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(K)});if(!e.ok)throw Error("Failed to create campaign");let a=await e.json();Y(),Q(),n.push("/admin/outreach/campaigns/".concat(a.campaign.id))}catch(e){console.error("Error creating campaign:",e)}},ea=e=>{n.push("/admin/outreach/campaigns/".concat(e))};return(0,s.jsx)(r.Z,{title:"Outreach Dashboard",children:(0,s.jsxs)(c.Z,{maxWidth:"xl",children:[(0,s.jsx)(d.Z,{sx:{mb:4},children:(0,s.jsxs)(o.Z,{container:!0,spacing:2,alignItems:"center",justifyContent:"space-between",children:[(0,s.jsx)(o.Z,{item:!0,children:(0,s.jsx)(h.Z,{variant:"h4",component:"h1",gutterBottom:!0,children:"Outreach Campaigns"})}),(0,s.jsx)(o.Z,{item:!0,children:(0,s.jsx)(p.Z,{variant:"contained",color:"primary",startIcon:(0,s.jsx)(L.Z,{}),onClick:()=>{H(!0)},children:"Create Campaign"})})]})}),(0,s.jsx)(d.Z,{sx:{mb:4},children:(0,s.jsxs)(o.Z,{container:!0,spacing:2,children:[(0,s.jsx)(o.Z,{item:!0,xs:12,md:6,children:(0,s.jsx)(x.Z,{fullWidth:!0,placeholder:"Search campaigns...",value:U,onChange:e=>{V(e.target.value),z(1)},InputProps:{startAdornment:(0,s.jsx)(u.Z,{position:"start",children:(0,s.jsx)(A.Z,{})})}})}),(0,s.jsx)(o.Z,{item:!0,xs:12,md:6,children:(0,s.jsxs)(m.Z,{fullWidth:!0,children:[(0,s.jsx)(j.Z,{id:"status-filter-label",children:"Status"}),(0,s.jsxs)(Z.Z,{labelId:"status-filter-label",value:X,onChange:e=>{B(e.target.value),z(1)},label:"Status",displayEmpty:!0,children:[(0,s.jsx)(g.Z,{value:"",children:"All Statuses"}),(0,s.jsx)(g.Z,{value:"draft",children:"Draft"}),(0,s.jsx)(g.Z,{value:"scheduled",children:"Scheduled"}),(0,s.jsx)(g.Z,{value:"in_progress",children:"In Progress"}),(0,s.jsx)(g.Z,{value:"paused",children:"Paused"}),(0,s.jsx)(g.Z,{value:"stopped",children:"Stopped"}),(0,s.jsx)(g.Z,{value:"completed",children:"Completed"})]})]})})]})}),(0,s.jsx)(d.Z,{sx:{mb:4},children:(0,s.jsx)(o.Z,{container:!0,spacing:3,children:P?(0,s.jsx)(o.Z,{item:!0,xs:12,children:(0,s.jsx)(h.Z,{align:"center",children:"Loading campaigns..."})}):0===O.length?(0,s.jsx)(o.Z,{item:!0,xs:12,children:(0,s.jsx)(h.Z,{align:"center",children:U||X?"No campaigns match your filters":"No campaigns found. Create your first campaign!"})}):O.map(e=>(0,s.jsx)(o.Z,{item:!0,xs:12,sm:6,md:4,children:(0,s.jsxs)(f.Z,{children:[(0,s.jsxs)(b.Z,{children:[(0,s.jsxs)(d.Z,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[(0,s.jsx)(h.Z,{variant:"h6",component:"h2",noWrap:!0,children:e.name}),(0,s.jsx)(v.Z,{label:e.status,color:R[e.status]||"default",size:"small"})]}),(0,s.jsx)(h.Z,{variant:"body2",color:"text.secondary",sx:{mb:2,height:40,overflow:"hidden",textOverflow:"ellipsis"},children:e.description||"No description"}),(0,s.jsx)(d.Z,{sx:{display:"flex",gap:1,mb:2},children:e.channels&&JSON.parse(e.channels).map(e=>(0,s.jsx)(v.Z,{icon:F[e],label:e,size:"small",variant:"outlined"},e))}),(0,s.jsxs)(o.Z,{container:!0,spacing:1,children:[(0,s.jsxs)(o.Z,{item:!0,xs:4,children:[(0,s.jsx)(h.Z,{variant:"caption",color:"text.secondary",children:"Recipients"}),(0,s.jsx)(h.Z,{variant:"body2",children:e.total_recipients||0})]}),(0,s.jsxs)(o.Z,{item:!0,xs:4,children:[(0,s.jsx)(h.Z,{variant:"caption",color:"text.secondary",children:"Sent"}),(0,s.jsx)(h.Z,{variant:"body2",children:e.sent_count||0})]}),(0,s.jsxs)(o.Z,{item:!0,xs:4,children:[(0,s.jsx)(h.Z,{variant:"caption",color:"text.secondary",children:"Created"}),(0,s.jsx)(h.Z,{variant:"body2",children:new Date(e.created_at).toLocaleDateString()})]})]})]}),(0,s.jsx)(y.Z,{children:(0,s.jsx)(p.Z,{size:"small",onClick:()=>ea(e.id),children:"View Details"})})]})},e.id))})}),q>1&&(0,s.jsx)(d.Z,{sx:{display:"flex",justifyContent:"center",mt:4},children:(0,s.jsx)(C.Z,{count:q,page:T,onChange:(e,a)=>{z(a)},color:"primary"})}),(0,s.jsxs)(w.Z,{open:G,onClose:Y,maxWidth:"sm",fullWidth:!0,children:[(0,s.jsx)(S.Z,{children:"Create New Campaign"}),(0,s.jsx)(k.Z,{children:(0,s.jsxs)(d.Z,{sx:{pt:1},children:[(0,s.jsx)(x.Z,{fullWidth:!0,label:"Campaign Name",name:"name",value:K.name,onChange:$,margin:"normal",required:!0}),(0,s.jsx)(x.Z,{fullWidth:!0,label:"Description",name:"description",value:K.description,onChange:$,margin:"normal",multiline:!0,rows:3}),(0,s.jsxs)(m.Z,{fullWidth:!0,margin:"normal",required:!0,children:[(0,s.jsx)(j.Z,{id:"channels-label",children:"Channels"}),(0,s.jsx)(Z.Z,{labelId:"channels-label",multiple:!0,value:K.channels,onChange:e=>{let{value:a}=e.target;M(e=>({...e,channels:a}))},input:(0,s.jsx)(_.Z,{label:"Channels"}),renderValue:e=>(0,s.jsx)(d.Z,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:e.map(e=>(0,s.jsx)(v.Z,{label:e,icon:F[e],size:"small"},e))}),children:[{value:"email",label:"Email"},{value:"whatsapp",label:"WhatsApp"},{value:"linkedin",label:"LinkedIn"},{value:"facebook",label:"Facebook"},{value:"instagram",label:"Instagram"},{value:"telegram",label:"Telegram"}].map(e=>(0,s.jsxs)(g.Z,{value:e.value,children:[(0,s.jsx)(E.Z,{checked:K.channels.indexOf(e.value)>-1}),(0,s.jsx)(N.Z,{primary:e.label})]},e.value))})]})]})}),(0,s.jsxs)(W.Z,{children:[(0,s.jsx)(p.Z,{onClick:Y,children:"Cancel"}),(0,s.jsx)(p.Z,{onClick:ee,variant:"contained",color:"primary",disabled:!K.name||0===K.channels.length,children:"Create"})]})]})]})})}}},function(e){e.O(0,[1664,986,4053,7659,2575,2888,9774,179],function(){return e(e.s=6295)}),_N_E=e.O()}]);