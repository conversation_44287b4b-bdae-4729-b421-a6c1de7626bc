(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[577],{79014:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/settings",function(){return a(86464)}])},86464:function(e,s,a){"use strict";a.r(s),a.d(s,{__N_SSP:function(){return d},default:function(){return m}});var t=a(85893),n=a(67294),i=a(33299),r=a(11163),c=a(9008),l=a.n(c),o=a(2575),p=a(60506),_=a.n(p),d=!0;function m(){let{data:e}=(0,i.useSession)(),[s,a]=(0,n.useState)("general"),[c,p]=(0,n.useState)(!0),[d,m]=(0,n.useState)(!1),[h,u]=(0,n.useState)(""),[g,x]=(0,n.useState)(""),[b,j]=(0,n.useState)({store_name:"",store_email:"",store_phone:"",store_address:"",currency:"USD",tax_rate:0}),[y,v]=(0,n.useState)({enable_shipping:!0,free_shipping_threshold:500,shipping_rates:[{name:"Standard Shipping",price:5.99,days:"3-5"},{name:"Express Shipping",price:12.99,days:"1-2"}]}),[N,P]=(0,n.useState)({enable_stripe:!0,stripe_publishable_key:"",stripe_secret_key:"",enable_paypal:!1,paypal_client_id:"",paypal_secret:""}),[f,A]=(0,n.useState)({email_from:"",smtp_host:"",smtp_port:"",smtp_user:"",smtp_password:"",smtp_secure:!0}),[k,C]=(0,n.useState)({enable_2fa:!1,enable_captcha:!1,captcha_site_key:"",captcha_secret_key:"",password_min_length:8,password_require_uppercase:!0,password_require_lowercase:!0,password_require_number:!0,password_require_special:!0});async function S(){try{p(!0);let e=await fetch("/api/admin/settings/".concat(s));if(!e.ok)throw Error("Failed to fetch ".concat(s," settings"));let a=await e.json();switch(s){case"general":j(a.settings);break;case"shipping":v(a.settings);break;case"payment":P(a.settings);break;case"email":A(a.settings);break;case"security":C(a.settings)}p(!1)}catch(e){console.error("Error fetching ".concat(s," settings:"),e),x("Failed to load ".concat(s," settings. Please try again.")),p(!1)}}(0,r.useRouter)(),(0,n.useEffect)(()=>{S()},[s]);let w=async()=>{try{let e;switch(m(!0),u(""),x(""),s){case"general":e=b;break;case"shipping":e=y;break;case"payment":e=N;break;case"email":e=f;break;case"security":e=k}if(!(await fetch("/api/admin/settings/".concat(s),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({settings:e})})).ok)throw Error("Failed to save ".concat(s," settings"));u("".concat(s.charAt(0).toUpperCase()+s.slice(1)," settings saved successfully!")),m(!1),S()}catch(e){console.error("Error saving ".concat(s," settings:"),e),x("Failed to save ".concat(s," settings. Please try again.")),m(!1)}},F=e=>{let{name:s,value:a}=e.target;j({...b,[s]:"tax_rate"===s?parseFloat(a):a})},B=e=>{let{name:s,value:a,type:t,checked:n}=e.target;v({...y,[s]:"checkbox"===t?n:"free_shipping_threshold"===s?parseFloat(a):a})},G=(e,s,a)=>{let t=[...y.shipping_rates];t[e]={...t[e],[s]:"price"===s?parseFloat(a):a},v({...y,shipping_rates:t})},T=e=>{let s=[...y.shipping_rates];s.splice(e,1),v({...y,shipping_rates:s})},E=e=>{let{name:s,value:a,type:t,checked:n}=e.target;P({...N,[s]:"checkbox"===t?n:a})},D=e=>{let{name:s,value:a,type:t,checked:n}=e.target;A({...f,[s]:"checkbox"===t?n:"smtp_port"===s?parseInt(a):a})},q=e=>{let{name:s,value:a,type:t,checked:n}=e.target;C({...k,[s]:"checkbox"===t?n:"password_min_length"===s?parseInt(a):a})};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l(),{children:(0,t.jsx)("title",{children:"Settings | Admin Dashboard"})}),(0,t.jsxs)(o.Z,{children:[(0,t.jsx)("div",{className:_().pageHeader,children:(0,t.jsx)("h1",{children:"Settings"})}),(0,t.jsxs)("div",{className:_().settingsContainer,children:[(0,t.jsxs)("div",{className:_().settingsTabs,children:[(0,t.jsx)("button",{className:"".concat(_().tabButton," ").concat("general"===s?_().activeTab:""),onClick:()=>a("general"),children:"General"}),(0,t.jsx)("button",{className:"".concat(_().tabButton," ").concat("shipping"===s?_().activeTab:""),onClick:()=>a("shipping"),children:"Shipping"}),(0,t.jsx)("button",{className:"".concat(_().tabButton," ").concat("payment"===s?_().activeTab:""),onClick:()=>a("payment"),children:"Payment"}),(0,t.jsx)("button",{className:"".concat(_().tabButton," ").concat("email"===s?_().activeTab:""),onClick:()=>a("email"),children:"Email"}),(0,t.jsx)("button",{className:"".concat(_().tabButton," ").concat("security"===s?_().activeTab:""),onClick:()=>a("security"),children:"Security"})]}),(0,t.jsxs)("div",{className:_().settingsContent,children:[h&&(0,t.jsx)("div",{className:_().successMessage,children:h}),g&&(0,t.jsx)("div",{className:_().errorMessage,children:g}),c?(0,t.jsxs)("div",{className:_().loading,children:[(0,t.jsx)("div",{className:_().spinner}),(0,t.jsx)("p",{children:"Loading settings..."})]}):(0,t.jsxs)(t.Fragment,{children:["general"===s&&(0,t.jsxs)("div",{className:_().settingsForm,children:[(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"store_name",children:"Store Name"}),(0,t.jsx)("input",{type:"text",id:"store_name",name:"store_name",value:b.store_name,onChange:F,className:_().input})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"store_email",children:"Store Email"}),(0,t.jsx)("input",{type:"email",id:"store_email",name:"store_email",value:b.store_email,onChange:F,className:_().input})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"store_phone",children:"Store Phone"}),(0,t.jsx)("input",{type:"text",id:"store_phone",name:"store_phone",value:b.store_phone,onChange:F,className:_().input})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"store_address",children:"Store Address"}),(0,t.jsx)("textarea",{id:"store_address",name:"store_address",value:b.store_address,onChange:F,className:_().textarea,rows:3})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"currency",children:"Currency"}),(0,t.jsxs)("select",{id:"currency",name:"currency",value:b.currency,onChange:F,className:_().select,children:[(0,t.jsx)("option",{value:"USD",children:"USD ($)"}),(0,t.jsx)("option",{value:"EUR",children:"EUR (€)"}),(0,t.jsx)("option",{value:"GBP",children:"GBP (\xa3)"}),(0,t.jsx)("option",{value:"CAD",children:"CAD (C$)"}),(0,t.jsx)("option",{value:"AUD",children:"AUD (A$)"})]})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"tax_rate",children:"Tax Rate (%)"}),(0,t.jsx)("input",{type:"number",id:"tax_rate",name:"tax_rate",value:b.tax_rate,onChange:F,className:_().input,min:"0",step:"0.01"})]})]}),"shipping"===s&&(0,t.jsxs)("div",{className:_().settingsForm,children:[(0,t.jsx)("div",{className:_().formGroup,children:(0,t.jsxs)("label",{className:_().checkboxLabel,children:[(0,t.jsx)("input",{type:"checkbox",name:"enable_shipping",checked:y.enable_shipping,onChange:B}),"Enable Shipping"]})}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"free_shipping_threshold",children:"Free Shipping Threshold ($)"}),(0,t.jsx)("input",{type:"number",id:"free_shipping_threshold",name:"free_shipping_threshold",value:y.free_shipping_threshold,onChange:B,className:_().input,min:"0",step:"0.01"}),(0,t.jsx)("p",{className:_().helpText,children:"Set to 0 to disable free shipping"})]}),(0,t.jsx)("h3",{children:"Shipping Rates"}),y.shipping_rates.map((e,s)=>(0,t.jsxs)("div",{className:_().shippingRate,children:[(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"rate_name_".concat(s),children:"Shipping Option Name"}),(0,t.jsx)("input",{type:"text",id:"rate_name_".concat(s),value:e.name,onChange:e=>G(s,"name",e.target.value),className:_().input})]}),(0,t.jsxs)("div",{className:_().formRow,children:[(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"rate_price_".concat(s),children:"Price ($)"}),(0,t.jsx)("input",{type:"number",id:"rate_price_".concat(s),value:e.price,onChange:e=>G(s,"price",e.target.value),className:_().input,min:"0",step:"0.01"})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"rate_days_".concat(s),children:"Delivery Time (days)"}),(0,t.jsx)("input",{type:"text",id:"rate_days_".concat(s),value:e.days,onChange:e=>G(s,"days",e.target.value),className:_().input,placeholder:"e.g. 3-5"})]})]}),(0,t.jsx)("button",{type:"button",onClick:()=>T(s),className:_().removeButton,children:"Remove"})]},s)),(0,t.jsx)("button",{type:"button",onClick:()=>{v({...y,shipping_rates:[...y.shipping_rates,{name:"New Shipping Option",price:0,days:"3-5"}]})},className:_().addButton,children:"Add Shipping Option"})]}),"payment"===s&&(0,t.jsxs)("div",{className:_().settingsForm,children:[(0,t.jsxs)("div",{className:_().paymentSection,children:[(0,t.jsx)("h3",{children:"Stripe"}),(0,t.jsx)("div",{className:_().formGroup,children:(0,t.jsxs)("label",{className:_().checkboxLabel,children:[(0,t.jsx)("input",{type:"checkbox",name:"enable_stripe",checked:N.enable_stripe,onChange:E}),"Enable Stripe Payments"]})}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"stripe_publishable_key",children:"Stripe Publishable Key"}),(0,t.jsx)("input",{type:"text",id:"stripe_publishable_key",name:"stripe_publishable_key",value:N.stripe_publishable_key,onChange:E,className:_().input,disabled:!N.enable_stripe})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"stripe_secret_key",children:"Stripe Secret Key"}),(0,t.jsx)("input",{type:"password",id:"stripe_secret_key",name:"stripe_secret_key",value:N.stripe_secret_key,onChange:E,className:_().input,disabled:!N.enable_stripe})]})]}),(0,t.jsxs)("div",{className:_().paymentSection,children:[(0,t.jsx)("h3",{children:"PayPal"}),(0,t.jsx)("div",{className:_().formGroup,children:(0,t.jsxs)("label",{className:_().checkboxLabel,children:[(0,t.jsx)("input",{type:"checkbox",name:"enable_paypal",checked:N.enable_paypal,onChange:E}),"Enable PayPal Payments"]})}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"paypal_client_id",children:"PayPal Client ID"}),(0,t.jsx)("input",{type:"text",id:"paypal_client_id",name:"paypal_client_id",value:N.paypal_client_id,onChange:E,className:_().input,disabled:!N.enable_paypal})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"paypal_secret",children:"PayPal Secret"}),(0,t.jsx)("input",{type:"password",id:"paypal_secret",name:"paypal_secret",value:N.paypal_secret,onChange:E,className:_().input,disabled:!N.enable_paypal})]})]})]}),"email"===s&&(0,t.jsxs)("div",{className:_().settingsForm,children:[(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"email_from",children:"From Email Address"}),(0,t.jsx)("input",{type:"email",id:"email_from",name:"email_from",value:f.email_from,onChange:D,className:_().input})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"smtp_host",children:"SMTP Host"}),(0,t.jsx)("input",{type:"text",id:"smtp_host",name:"smtp_host",value:f.smtp_host,onChange:D,className:_().input})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"smtp_port",children:"SMTP Port"}),(0,t.jsx)("input",{type:"number",id:"smtp_port",name:"smtp_port",value:f.smtp_port,onChange:D,className:_().input})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"smtp_user",children:"SMTP Username"}),(0,t.jsx)("input",{type:"text",id:"smtp_user",name:"smtp_user",value:f.smtp_user,onChange:D,className:_().input})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"smtp_password",children:"SMTP Password"}),(0,t.jsx)("input",{type:"password",id:"smtp_password",name:"smtp_password",value:f.smtp_password,onChange:D,className:_().input})]}),(0,t.jsx)("div",{className:_().formGroup,children:(0,t.jsxs)("label",{className:_().checkboxLabel,children:[(0,t.jsx)("input",{type:"checkbox",name:"smtp_secure",checked:f.smtp_secure,onChange:D}),"Use Secure Connection (TLS)"]})}),(0,t.jsx)("button",{type:"button",className:_().testButton,onClick:()=>alert("Test email functionality will be implemented soon."),children:"Send Test Email"})]}),"security"===s&&(0,t.jsxs)("div",{className:_().settingsForm,children:[(0,t.jsxs)("div",{className:_().securitySection,children:[(0,t.jsx)("h3",{children:"Two-Factor Authentication"}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsxs)("label",{className:_().checkboxLabel,children:[(0,t.jsx)("input",{type:"checkbox",name:"enable_2fa",checked:k.enable_2fa,onChange:q}),"Enable Two-Factor Authentication"]}),(0,t.jsx)("p",{className:_().helpText,children:"Require administrators to use two-factor authentication when logging in"})]})]}),(0,t.jsxs)("div",{className:_().securitySection,children:[(0,t.jsx)("h3",{children:"CAPTCHA Protection"}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsxs)("label",{className:_().checkboxLabel,children:[(0,t.jsx)("input",{type:"checkbox",name:"enable_captcha",checked:k.enable_captcha,onChange:q}),"Enable CAPTCHA on Forms"]}),(0,t.jsx)("p",{className:_().helpText,children:"Add CAPTCHA verification to login, registration, and contact forms"})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"captcha_site_key",children:"reCAPTCHA Site Key"}),(0,t.jsx)("input",{type:"text",id:"captcha_site_key",name:"captcha_site_key",value:k.captcha_site_key,onChange:q,className:_().input,disabled:!k.enable_captcha})]}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"captcha_secret_key",children:"reCAPTCHA Secret Key"}),(0,t.jsx)("input",{type:"password",id:"captcha_secret_key",name:"captcha_secret_key",value:k.captcha_secret_key,onChange:q,className:_().input,disabled:!k.enable_captcha})]})]}),(0,t.jsxs)("div",{className:_().securitySection,children:[(0,t.jsx)("h3",{children:"Password Requirements"}),(0,t.jsxs)("div",{className:_().formGroup,children:[(0,t.jsx)("label",{htmlFor:"password_min_length",children:"Minimum Password Length"}),(0,t.jsx)("input",{type:"number",id:"password_min_length",name:"password_min_length",value:k.password_min_length,onChange:q,className:_().input,min:"6"})]}),(0,t.jsx)("div",{className:_().formGroup,children:(0,t.jsxs)("label",{className:_().checkboxLabel,children:[(0,t.jsx)("input",{type:"checkbox",name:"password_require_uppercase",checked:k.password_require_uppercase,onChange:q}),"Require Uppercase Letter"]})}),(0,t.jsx)("div",{className:_().formGroup,children:(0,t.jsxs)("label",{className:_().checkboxLabel,children:[(0,t.jsx)("input",{type:"checkbox",name:"password_require_lowercase",checked:k.password_require_lowercase,onChange:q}),"Require Lowercase Letter"]})}),(0,t.jsx)("div",{className:_().formGroup,children:(0,t.jsxs)("label",{className:_().checkboxLabel,children:[(0,t.jsx)("input",{type:"checkbox",name:"password_require_number",checked:k.password_require_number,onChange:q}),"Require Number"]})}),(0,t.jsx)("div",{className:_().formGroup,children:(0,t.jsxs)("label",{className:_().checkboxLabel,children:[(0,t.jsx)("input",{type:"checkbox",name:"password_require_special",checked:k.password_require_special,onChange:q}),"Require Special Character"]})})]})]}),(0,t.jsx)("div",{className:_().formActions,children:(0,t.jsx)("button",{type:"button",onClick:w,className:_().saveButton,disabled:d,children:d?"Saving...":"Save Settings"})})]})]})]})]})]})}},60506:function(e){e.exports={container:"AdminPages_container__nj6XB",pageHeader:"AdminPages_pageHeader__LHSBG",addButton:"AdminPages_addButton__3YV2v",filterBar:"AdminPages_filterBar__fJxVO",filterGroup:"AdminPages_filterGroup__CvemU",select:"AdminPages_select__5v_Ue",searchForm:"AdminPages_searchForm__DxHvp",searchInput:"AdminPages_searchInput__czn7m",searchButton:"AdminPages_searchButton__HrKq3",tableContainer:"AdminPages_tableContainer__EZ3Ac",table:"AdminPages_table__KVh8N",sortableColumn:"AdminPages_sortableColumn___99vU",activeSortColumn:"AdminPages_activeSortColumn__pPIua",sortIcon:"AdminPages_sortIcon__tiBuk",imageCell:"AdminPages_imageCell__ahCj4",imageColumn:"AdminPages_imageColumn__m3_De",productImage:"AdminPages_productImage__8KMQy",stockBadge:"AdminPages_stockBadge__Uquo4",inStock:"AdminPages_inStock__4D2jV",lowStock:"AdminPages_lowStock__JQmaX",outOfStock:"AdminPages_outOfStock__PVOkV",stockCount:"AdminPages_stockCount__CJVuo",actions:"AdminPages_actions__5NUWv",actionButton:"AdminPages_actionButton__A25EK",editButton:"AdminPages_editButton__xhIdN",deleteButton:"AdminPages_deleteButton__wFOYO",ordersButton:"AdminPages_ordersButton__nuIHY",updateButton:"AdminPages_updateButton__TKzfU",statusBadge:"AdminPages_statusBadge__vf_aR",statusPending:"AdminPages_statusPending__uo129",statusProcessing:"AdminPages_statusProcessing__mabVA",statusShipped:"AdminPages_statusShipped__Jc_6I",statusDelivered:"AdminPages_statusDelivered__falDE",statusCancelled:"AdminPages_statusCancelled__fxl2i",statusDropdown:"AdminPages_statusDropdown__FTpQA",dropdownContent:"AdminPages_dropdownContent__4sAyN",activeStatus:"AdminPages_activeStatus__rh9Cs",noData:"AdminPages_noData__Mc9eU",pagination:"AdminPages_pagination__1uxxQ",paginationButton:"AdminPages_paginationButton__kiY8c",pageInfo:"AdminPages_pageInfo__XD0jp",loading:"AdminPages_loading__zLU2R",spinner:"AdminPages_spinner__lVOQO",spin:"AdminPages_spin__s6LXq",settingsContainer:"AdminPages_settingsContainer__yidTp",settingsTabs:"AdminPages_settingsTabs__Iw9Zr",tabButton:"AdminPages_tabButton__Swz33",activeTab:"AdminPages_activeTab___YGm8",settingsContent:"AdminPages_settingsContent__5zi5i",settingsForm:"AdminPages_settingsForm__6w8pE",formGroup:"AdminPages_formGroup__MbgWF",input:"AdminPages_input__NjzSf",textarea:"AdminPages_textarea__zm7h5",checkboxLabel:"AdminPages_checkboxLabel__QnbQ4",helpText:"AdminPages_helpText__6QXDW",formRow:"AdminPages_formRow__vrK66",formActions:"AdminPages_formActions__6_1in",saveButton:"AdminPages_saveButton__aoM8K",errorMessage:"AdminPages_errorMessage__CBbWX",successMessage:"AdminPages_successMessage__42J_F",shippingRate:"AdminPages_shippingRate__4pZsF",removeButton:"AdminPages_removeButton__k0hN8",paymentSection:"AdminPages_paymentSection___O13P",securitySection:"AdminPages_securitySection__lvIFb",testButton:"AdminPages_testButton__Xov_6"}},11163:function(e,s,a){e.exports=a(43079)}},function(e){e.O(0,[1664,2575,2888,9774,179],function(){return e(e.s=79014)}),_N_E=e.O()}]);