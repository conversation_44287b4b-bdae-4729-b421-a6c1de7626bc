(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7930],{44976:function(a,e,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/chatbot",function(){return s(70377)}])},70377:function(a,e,s){"use strict";s.r(e),s.d(e,{default:function(){return _}});var i=s(85893),n=s(67294),t=s(33299),d=s(11163),r=s(9008),c=s.n(r),l=s(41664),o=s.n(l),m=s(15748),h=s.n(m);function _(){let{data:a,status:e}=(0,t.useSession)(),s=(0,d.useRouter)(),[r,l]=(0,n.useState)("overview"),[m,_]=(0,n.useState)([]),[x,v]=(0,n.useState)({totalConversations:0,totalMessages:0,avgMessagesPerConversation:0,topIntents:[],satisfactionRate:0}),[j,b]=(0,n.useState)([]),[u,N]=(0,n.useState)(!0);(0,n.useEffect)(()=>{"loading"===e||a&&a.user.isAdmin||s.push("/auth/signin?callbackUrl=/admin/chatbot")},[a,e,s]),(0,n.useEffect)(()=>{"authenticated"===e&&a.user.isAdmin&&(async()=>{N(!0);try{let a=await fetch("/api/admin/chatbot/conversations"),e=await a.json();e.success&&_(e.conversations);let s=await fetch("/api/admin/chatbot/analytics"),i=await s.json();i.success&&v(i.analytics);let n=await fetch("/api/admin/chatbot/articles"),t=await n.json();t.success&&b(t.articles)}catch(a){console.error("Error fetching chatbot data:",a)}finally{N(!1)}})()},[a,e]);let C=a=>new Date(a).toLocaleString();return"loading"===e||u?(0,i.jsxs)("div",{className:h().loadingContainer,children:[(0,i.jsx)("div",{className:h().spinner}),(0,i.jsx)("p",{children:"Loading chatbot dashboard..."})]}):a&&a.user.isAdmin?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(c(),{children:[(0,i.jsx)("title",{children:"Chatbot Admin | MDTS Admin"}),(0,i.jsx)("meta",{name:"description",content:"Chatbot administration dashboard for MDTS admin"})]}),(0,i.jsxs)("div",{className:h().adminContainer,children:[(0,i.jsxs)("div",{className:h().adminSidebar,children:[(0,i.jsx)("h2",{className:h().adminLogo,children:"MDTS Admin"}),(0,i.jsxs)("nav",{className:h().adminNav,children:[(0,i.jsx)(o(),{href:"/admin",className:h().adminNavItem,children:"Dashboard"}),(0,i.jsx)(o(),{href:"/admin/products",className:h().adminNavItem,children:"Products"}),(0,i.jsx)(o(),{href:"/admin/orders",className:h().adminNavItem,children:"Orders"}),(0,i.jsx)(o(),{href:"/admin/customers",className:h().adminNavItem,children:"Customers"}),(0,i.jsx)(o(),{href:"/admin/chatbot",className:"".concat(h().adminNavItem," ").concat(h().active),children:"Chatbot"})]})]}),(0,i.jsxs)("div",{className:h().adminContent,children:[(0,i.jsxs)("header",{className:h().adminHeader,children:[(0,i.jsx)("h1",{children:"Chatbot Administration"}),(0,i.jsx)("div",{className:h().adminUser,children:(0,i.jsx)("span",{children:a.user.name||a.user.email})})]}),(0,i.jsxs)("div",{className:h().adminTabs,children:[(0,i.jsx)("button",{className:"".concat(h().adminTab," ").concat("overview"===r?h().activeTab:""),onClick:()=>l("overview"),children:"Overview"}),(0,i.jsx)("button",{className:"".concat(h().adminTab," ").concat("conversations"===r?h().activeTab:""),onClick:()=>l("conversations"),children:"Conversations"}),(0,i.jsx)("button",{className:"".concat(h().adminTab," ").concat("analytics"===r?h().activeTab:""),onClick:()=>l("analytics"),children:"Analytics"}),(0,i.jsx)("button",{className:"".concat(h().adminTab," ").concat("articles"===r?h().activeTab:""),onClick:()=>l("articles"),children:"Support Articles"})]}),(0,i.jsxs)("div",{className:h().adminTabContent,children:["overview"===r&&(0,i.jsxs)("div",{className:h().overviewTab,children:[(0,i.jsxs)("div",{className:h().statsGrid,children:[(0,i.jsxs)("div",{className:h().statCard,children:[(0,i.jsx)("h3",{children:"Total Conversations"}),(0,i.jsx)("div",{className:h().statValue,children:x.totalConversations})]}),(0,i.jsxs)("div",{className:h().statCard,children:[(0,i.jsx)("h3",{children:"Total Messages"}),(0,i.jsx)("div",{className:h().statValue,children:x.totalMessages})]}),(0,i.jsxs)("div",{className:h().statCard,children:[(0,i.jsx)("h3",{children:"Avg. Messages Per Conversation"}),(0,i.jsx)("div",{className:h().statValue,children:x.avgMessagesPerConversation.toFixed(1)})]}),(0,i.jsxs)("div",{className:h().statCard,children:[(0,i.jsx)("h3",{children:"Satisfaction Rate"}),(0,i.jsxs)("div",{className:h().statValue,children:[(100*x.satisfactionRate).toFixed(1),"%"]})]})]}),(0,i.jsxs)("div",{className:h().overviewCharts,children:[(0,i.jsxs)("div",{className:h().chartCard,children:[(0,i.jsx)("h3",{children:"Top Intents"}),(0,i.jsx)("div",{className:h().barChart,children:x.topIntents.map((a,e)=>{var s;return(0,i.jsxs)("div",{className:h().barChartItem,children:[(0,i.jsx)("div",{className:h().barLabel,children:a.name}),(0,i.jsxs)("div",{className:h().barContainer,children:[(0,i.jsx)("div",{className:h().bar,style:{width:"".concat(Math.min(100,a.count/((null===(s=x.topIntents[0])||void 0===s?void 0:s.count)||1)*100),"%")}}),(0,i.jsx)("div",{className:h().barValue,children:a.count})]})]},e)})})]}),(0,i.jsxs)("div",{className:h().chartCard,children:[(0,i.jsx)("h3",{children:"Recent Activity"}),(0,i.jsx)("div",{className:h().recentActivity,children:m.slice(0,5).map((a,e)=>(0,i.jsxs)("div",{className:h().activityItem,children:[(0,i.jsx)("div",{className:h().activityIcon,children:(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,i.jsx)("path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"})})}),(0,i.jsxs)("div",{className:h().activityContent,children:[(0,i.jsxs)("div",{className:h().activityTitle,children:["New conversation from ",a.user_id]}),(0,i.jsx)("div",{className:h().activityTime,children:C(a.created_at)})]})]},e))})]})]}),(0,i.jsxs)("div",{className:h().actionButtons,children:[(0,i.jsx)("button",{className:h().actionButton,onClick:()=>l("conversations"),children:"View All Conversations"}),(0,i.jsx)("button",{className:h().actionButton,onClick:()=>l("analytics"),children:"View Detailed Analytics"})]})]}),"conversations"===r&&(0,i.jsx)("div",{className:h().conversationsTab,children:(0,i.jsx)("div",{className:h().tableContainer,children:(0,i.jsxs)("table",{className:h().dataTable,children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{children:"ID"}),(0,i.jsx)("th",{children:"User"}),(0,i.jsx)("th",{children:"Started"}),(0,i.jsx)("th",{children:"Last Activity"}),(0,i.jsx)("th",{children:"Messages"}),(0,i.jsx)("th",{children:"Status"}),(0,i.jsx)("th",{children:"Actions"})]})}),(0,i.jsx)("tbody",{children:m.map((a,e)=>(0,i.jsxs)("tr",{children:[(0,i.jsxs)("td",{children:[a.id.substring(0,8),"..."]}),(0,i.jsx)("td",{children:a.user_id}),(0,i.jsx)("td",{children:C(a.created_at)}),(0,i.jsx)("td",{children:C(a.updated_at)}),(0,i.jsx)("td",{children:a.message_count||0}),(0,i.jsx)("td",{children:(0,i.jsx)("span",{className:"".concat(h().statusBadge," ").concat(h()[a.status]),children:a.status})}),(0,i.jsx)("td",{children:(0,i.jsx)("button",{className:h().viewButton,children:"View"})})]},e))})]})})}),"analytics"===r&&(0,i.jsxs)("div",{className:h().analyticsTab,children:[(0,i.jsxs)("div",{className:h().analyticsSection,children:[(0,i.jsx)("h3",{children:"Intent Distribution"}),(0,i.jsxs)("div",{className:h().pieChartContainer,children:[(0,i.jsx)("div",{className:h().pieChart,children:(0,i.jsx)("div",{className:h().pieChartPlaceholder,children:"Pie Chart Visualization"})}),(0,i.jsx)("div",{className:h().pieChartLegend,children:x.topIntents.map((a,e)=>(0,i.jsxs)("div",{className:h().legendItem,children:[(0,i.jsx)("div",{className:h().legendColor,style:{backgroundColor:function(a){let e=["#0066cc","#4caf50","#ff9800","#e53e3e","#9c27b0","#3f51b5","#00bcd4","#009688","#8bc34a","#ffeb3b"];return e[a%e.length]}(e)}}),(0,i.jsx)("div",{className:h().legendLabel,children:a.name}),(0,i.jsxs)("div",{className:h().legendValue,children:[(a.count/x.totalMessages*100).toFixed(1),"%"]})]},e))})]})]}),(0,i.jsxs)("div",{className:h().analyticsSection,children:[(0,i.jsx)("h3",{children:"Conversation Length Distribution"}),(0,i.jsxs)("div",{className:h().barChartHorizontal,children:[(0,i.jsxs)("div",{className:h().barChartItem,children:[(0,i.jsx)("div",{className:h().barLabel,children:"1-2 messages"}),(0,i.jsxs)("div",{className:h().barContainer,children:[(0,i.jsx)("div",{className:h().bar,style:{width:"45%"}}),(0,i.jsx)("div",{className:h().barValue,children:"45%"})]})]}),(0,i.jsxs)("div",{className:h().barChartItem,children:[(0,i.jsx)("div",{className:h().barLabel,children:"3-5 messages"}),(0,i.jsxs)("div",{className:h().barContainer,children:[(0,i.jsx)("div",{className:h().bar,style:{width:"30%"}}),(0,i.jsx)("div",{className:h().barValue,children:"30%"})]})]}),(0,i.jsxs)("div",{className:h().barChartItem,children:[(0,i.jsx)("div",{className:h().barLabel,children:"6-10 messages"}),(0,i.jsxs)("div",{className:h().barContainer,children:[(0,i.jsx)("div",{className:h().bar,style:{width:"15%"}}),(0,i.jsx)("div",{className:h().barValue,children:"15%"})]})]}),(0,i.jsxs)("div",{className:h().barChartItem,children:[(0,i.jsx)("div",{className:h().barLabel,children:"11+ messages"}),(0,i.jsxs)("div",{className:h().barContainer,children:[(0,i.jsx)("div",{className:h().bar,style:{width:"10%"}}),(0,i.jsx)("div",{className:h().barValue,children:"10%"})]})]})]})]}),(0,i.jsxs)("div",{className:h().analyticsSection,children:[(0,i.jsx)("h3",{children:"Common User Queries"}),(0,i.jsx)("div",{className:h().wordCloudContainer,children:(0,i.jsx)("div",{className:h().wordCloudPlaceholder,children:"Word Cloud Visualization"})})]}),(0,i.jsxs)("div",{className:h().analyticsSection,children:[(0,i.jsx)("h3",{children:"Chatbot Performance"}),(0,i.jsxs)("div",{className:h().performanceMetrics,children:[(0,i.jsxs)("div",{className:h().metricCard,children:[(0,i.jsx)("h4",{children:"Resolution Rate"}),(0,i.jsx)("div",{className:h().metricValue,children:"78%"}),(0,i.jsx)("div",{className:h().metricDescription,children:"Percentage of conversations resolved without human intervention"})]}),(0,i.jsxs)("div",{className:h().metricCard,children:[(0,i.jsx)("h4",{children:"Handoff Rate"}),(0,i.jsx)("div",{className:h().metricValue,children:"22%"}),(0,i.jsx)("div",{className:h().metricDescription,children:"Percentage of conversations transferred to human support"})]}),(0,i.jsxs)("div",{className:h().metricCard,children:[(0,i.jsx)("h4",{children:"Avg. Response Time"}),(0,i.jsx)("div",{className:h().metricValue,children:"1.2s"}),(0,i.jsx)("div",{className:h().metricDescription,children:"Average time to generate a response"})]})]})]})]}),"articles"===r&&(0,i.jsxs)("div",{className:h().articlesTab,children:[(0,i.jsxs)("div",{className:h().articlesHeader,children:[(0,i.jsx)("h3",{children:"Support Articles"}),(0,i.jsxs)("button",{className:h().addButton,children:[(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,i.jsx)("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),(0,i.jsx)("line",{x1:"5",y1:"12",x2:"19",y2:"12"})]}),"Add New Article"]})]}),(0,i.jsx)("div",{className:h().tableContainer,children:(0,i.jsxs)("table",{className:h().dataTable,children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{children:"Title"}),(0,i.jsx)("th",{children:"Category"}),(0,i.jsx)("th",{children:"Created"}),(0,i.jsx)("th",{children:"Updated"}),(0,i.jsx)("th",{children:"Actions"})]})}),(0,i.jsx)("tbody",{children:j.map((a,e)=>(0,i.jsxs)("tr",{children:[(0,i.jsx)("td",{children:a.title}),(0,i.jsx)("td",{children:a.category}),(0,i.jsx)("td",{children:C(a.created_at)}),(0,i.jsx)("td",{children:C(a.updated_at)}),(0,i.jsx)("td",{children:(0,i.jsxs)("div",{className:h().actionButtons,children:[(0,i.jsxs)("button",{className:h().editButton,children:[(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,i.jsx)("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),(0,i.jsx)("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"})]}),"Edit"]}),(0,i.jsxs)("button",{className:h().deleteButton,children:[(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,i.jsx)("polyline",{points:"3 6 5 6 21 6"}),(0,i.jsx)("path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"})]}),"Delete"]})]})})]},e))})]})})]})]})]})]})]}):null}},15748:function(a){a.exports={adminContainer:"Admin_adminContainer__fnIiy",adminSidebar:"Admin_adminSidebar__ECUVT",adminLogo:"Admin_adminLogo__KRj8V",adminNav:"Admin_adminNav__6VV7e",adminNavItem:"Admin_adminNavItem__vGP0q",active:"Admin_active__BhXLC",adminContent:"Admin_adminContent__la1bh",adminHeader:"Admin_adminHeader__D8Awi",adminUser:"Admin_adminUser__9UhpZ",adminTabs:"Admin_adminTabs__cqvDR",adminTab:"Admin_adminTab__W_1Cu",activeTab:"Admin_activeTab__fvtdm",adminTabContent:"Admin_adminTabContent__2Pyru",loadingContainer:"Admin_loadingContainer__co6Jm",spinner:"Admin_spinner__UFXHG",spin:"Admin_spin__Ah3vg",statsGrid:"Admin_statsGrid__TB_yj",statCard:"Admin_statCard__TRCV8",statValue:"Admin_statValue__7vDB_",overviewCharts:"Admin_overviewCharts__CbCXN",chartCard:"Admin_chartCard__nhG5d",barChart:"Admin_barChart__P7yDX",barChartItem:"Admin_barChartItem__upy5l",barLabel:"Admin_barLabel__zg5pf",barContainer:"Admin_barContainer__rV5lJ",bar:"Admin_bar__KT2HJ",redBar:"Admin_redBar__Ux63t",barValue:"Admin_barValue__by0tg",tableContainer:"Admin_tableContainer__jS7Fw",dataTable:"Admin_dataTable__HJk6Z",actionButton:"Admin_actionButton__UMz0P",zeroResultsActions:"Admin_zeroResultsActions__a8LFP",recommendationsList:"Admin_recommendationsList__kt0vF",marketplacesTab:"Admin_marketplacesTab__F1B_G",tabDescription:"Admin_tabDescription__4232R",customersTab:"Admin_customersTab__K7_If",ordersTab:"Admin_ordersTab__nYVxi",productsTab:"Admin_productsTab__c7E0b",settingsTab:"Admin_settingsTab__uKxYF",adminSection:"Admin_adminSection__vTklz",sectionDescription:"Admin_sectionDescription__fkI2f",errorMessage:"Admin_errorMessage__X7vgq",successMessage:"Admin_successMessage__TfbaZ",integrationCards:"Admin_integrationCards__ahfcD",integrationCard:"Admin_integrationCard__UDVVJ",integrationHeader:"Admin_integrationHeader__ZpOSz",integrationLogo:"Admin_integrationLogo__L6Jp1",integrationInfo:"Admin_integrationInfo__xh2eJ",integrationStatus:"Admin_integrationStatus___Iy_i",statusIndicator:"Admin_statusIndicator__71aId",connected:"Admin_connected__7QFvd",disconnected:"Admin_disconnected__Awmdk",integrationBody:"Admin_integrationBody__DHwlP",integrationField:"Admin_integrationField__vCIAg",apiKeyField:"Admin_apiKeyField__Flra0",copyButton:"Admin_copyButton__jtW5y",databasesList:"Admin_databasesList__ANXzk",webhooksList:"Admin_webhooksList__4hKEU",databaseItem:"Admin_databaseItem__By6lR",webhookItem:"Admin_webhookItem__TtL6j",databaseInfo:"Admin_databaseInfo__5cusK",webhookInfo:"Admin_webhookInfo__rvvlE",databaseName:"Admin_databaseName__mbr33",webhookName:"Admin_webhookName__fRaXr",databaseId:"Admin_databaseId__QqduL",webhookUrl:"Admin_webhookUrl__6HqB3",switch:"Admin_switch__9nB1M",slider:"Admin_slider__GAnsH",connectContainer:"Admin_connectContainer__U5lly",connectButton:"Admin_connectButton__x3D79",disconnectButton:"Admin_disconnectButton__NA_DW",integrationHelp:"Admin_integrationHelp__oqKc1"}},11163:function(a,e,s){a.exports=s(43079)}},function(a){a.O(0,[1664,2888,9774,179],function(){return a(a.s=44976)}),_N_E=a.O()}]);