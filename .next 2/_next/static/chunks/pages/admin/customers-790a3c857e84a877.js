(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7939],{77706:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/customers",function(){return s(80343)}])},80343:function(e,t,s){"use strict";s.r(t),s.d(t,{__N_SSP:function(){return g},default:function(){return h}});var n=s(85893),a=s(67294),i=s(33299),o=s(11163),c=s(9008),_=s.n(c),r=s(41664),d=s.n(r),l=s(2575),m=s(60506),u=s.n(m),g=!0;function h(){let{data:e}=(0,i.useSession)(),[t,s]=(0,a.useState)([]),[c,r]=(0,a.useState)(!0),[m,g]=(0,a.useState)(""),[h,p]=(0,a.useState)("created_at"),[P,A]=(0,a.useState)("desc"),[x,j]=(0,a.useState)(1),[f,C]=(0,a.useState)(1);async function B(){try{r(!0);let e=new URLSearchParams;e.append("search",m),e.append("sortBy",h),e.append("sortOrder",P),e.append("page",x),e.append("limit",10);let t=await fetch("/api/admin/customers?".concat(e.toString()));if(!t.ok)throw Error("Failed to fetch customers");let n=await t.json();s(n.customers),C(n.totalPages),r(!1)}catch(e){console.error("Error fetching customers:",e),r(!1)}}(0,o.useRouter)(),(0,a.useEffect)(()=>{B()},[m,h,P,x]);let b=e=>{h===e?A("asc"===P?"desc":"asc"):(p(e),A("desc")),j(1)},N=e=>{e>=1&&e<=f&&j(e)},S=e=>new Date(e).toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(_(),{children:(0,n.jsx)("title",{children:"Customers | Admin Dashboard"})}),(0,n.jsxs)(l.Z,{children:[(0,n.jsx)("div",{className:u().pageHeader,children:(0,n.jsx)("h1",{children:"Customers"})}),(0,n.jsx)("div",{className:u().filterBar,children:(0,n.jsxs)("form",{onSubmit:e=>{e.preventDefault(),j(1),B()},className:u().searchForm,children:[(0,n.jsx)("input",{type:"text",placeholder:"Search by name or email...",value:m,onChange:e=>{g(e.target.value)},className:u().searchInput}),(0,n.jsx)("button",{type:"submit",className:u().searchButton,children:"Search"})]})}),c?(0,n.jsxs)("div",{className:u().loading,children:[(0,n.jsx)("div",{className:u().spinner}),(0,n.jsx)("p",{children:"Loading customers..."})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:u().tableContainer,children:(0,n.jsxs)("table",{className:u().table,children:[(0,n.jsx)("thead",{children:(0,n.jsxs)("tr",{children:[(0,n.jsxs)("th",{className:"".concat(u().sortableColumn," ").concat("name"===h?u().activeSortColumn:""),onClick:()=>b("name"),children:["Name","name"===h&&(0,n.jsx)("span",{className:u().sortIcon,children:"asc"===P?"↑":"↓"})]}),(0,n.jsxs)("th",{className:"".concat(u().sortableColumn," ").concat("email"===h?u().activeSortColumn:""),onClick:()=>b("email"),children:["Email","email"===h&&(0,n.jsx)("span",{className:u().sortIcon,children:"asc"===P?"↑":"↓"})]}),(0,n.jsxs)("th",{className:"".concat(u().sortableColumn," ").concat("created_at"===h?u().activeSortColumn:""),onClick:()=>b("created_at"),children:["Joined","created_at"===h&&(0,n.jsx)("span",{className:u().sortIcon,children:"asc"===P?"↑":"↓"})]}),(0,n.jsxs)("th",{className:"".concat(u().sortableColumn," ").concat("orders_count"===h?u().activeSortColumn:""),onClick:()=>b("orders_count"),children:["Orders","orders_count"===h&&(0,n.jsx)("span",{className:u().sortIcon,children:"asc"===P?"↑":"↓"})]}),(0,n.jsxs)("th",{className:"".concat(u().sortableColumn," ").concat("total_spent"===h?u().activeSortColumn:""),onClick:()=>b("total_spent"),children:["Total Spent","total_spent"===h&&(0,n.jsx)("span",{className:u().sortIcon,children:"asc"===P?"↑":"↓"})]}),(0,n.jsx)("th",{children:"Actions"})]})}),(0,n.jsx)("tbody",{children:t.length>0?t.map(e=>(0,n.jsxs)("tr",{children:[(0,n.jsxs)("td",{children:[e.first_name," ",e.last_name]}),(0,n.jsx)("td",{children:e.email}),(0,n.jsx)("td",{children:S(e.created_at)}),(0,n.jsx)("td",{children:e.orders_count||0}),(0,n.jsxs)("td",{children:["$",(e.total_spent||0).toFixed(2)]}),(0,n.jsxs)("td",{className:u().actions,children:[(0,n.jsx)(d(),{href:"/admin/customers/".concat(e.id),className:u().actionButton,children:"View"}),(0,n.jsx)(d(),{href:"/admin/customers/".concat(e.id,"/edit"),className:"".concat(u().actionButton," ").concat(u().editButton),children:"Edit"}),(0,n.jsx)(d(),{href:"/admin/customers/".concat(e.id,"/orders"),className:"".concat(u().actionButton," ").concat(u().ordersButton),children:"Orders"})]})]},e.id)):(0,n.jsx)("tr",{children:(0,n.jsx)("td",{colSpan:"6",className:u().noData,children:"No customers found"})})})]})}),(0,n.jsxs)("div",{className:u().pagination,children:[(0,n.jsx)("button",{onClick:()=>N(1),disabled:1===x,className:u().paginationButton,children:"First"}),(0,n.jsx)("button",{onClick:()=>N(x-1),disabled:1===x,className:u().paginationButton,children:"Previous"}),(0,n.jsxs)("span",{className:u().pageInfo,children:["Page ",x," of ",f]}),(0,n.jsx)("button",{onClick:()=>N(x+1),disabled:x===f,className:u().paginationButton,children:"Next"}),(0,n.jsx)("button",{onClick:()=>N(f),disabled:x===f,className:u().paginationButton,children:"Last"})]})]})]})]})}},60506:function(e){e.exports={container:"AdminPages_container__nj6XB",pageHeader:"AdminPages_pageHeader__LHSBG",addButton:"AdminPages_addButton__3YV2v",filterBar:"AdminPages_filterBar__fJxVO",filterGroup:"AdminPages_filterGroup__CvemU",select:"AdminPages_select__5v_Ue",searchForm:"AdminPages_searchForm__DxHvp",searchInput:"AdminPages_searchInput__czn7m",searchButton:"AdminPages_searchButton__HrKq3",tableContainer:"AdminPages_tableContainer__EZ3Ac",table:"AdminPages_table__KVh8N",sortableColumn:"AdminPages_sortableColumn___99vU",activeSortColumn:"AdminPages_activeSortColumn__pPIua",sortIcon:"AdminPages_sortIcon__tiBuk",imageCell:"AdminPages_imageCell__ahCj4",imageColumn:"AdminPages_imageColumn__m3_De",productImage:"AdminPages_productImage__8KMQy",stockBadge:"AdminPages_stockBadge__Uquo4",inStock:"AdminPages_inStock__4D2jV",lowStock:"AdminPages_lowStock__JQmaX",outOfStock:"AdminPages_outOfStock__PVOkV",stockCount:"AdminPages_stockCount__CJVuo",actions:"AdminPages_actions__5NUWv",actionButton:"AdminPages_actionButton__A25EK",editButton:"AdminPages_editButton__xhIdN",deleteButton:"AdminPages_deleteButton__wFOYO",ordersButton:"AdminPages_ordersButton__nuIHY",updateButton:"AdminPages_updateButton__TKzfU",statusBadge:"AdminPages_statusBadge__vf_aR",statusPending:"AdminPages_statusPending__uo129",statusProcessing:"AdminPages_statusProcessing__mabVA",statusShipped:"AdminPages_statusShipped__Jc_6I",statusDelivered:"AdminPages_statusDelivered__falDE",statusCancelled:"AdminPages_statusCancelled__fxl2i",statusDropdown:"AdminPages_statusDropdown__FTpQA",dropdownContent:"AdminPages_dropdownContent__4sAyN",activeStatus:"AdminPages_activeStatus__rh9Cs",noData:"AdminPages_noData__Mc9eU",pagination:"AdminPages_pagination__1uxxQ",paginationButton:"AdminPages_paginationButton__kiY8c",pageInfo:"AdminPages_pageInfo__XD0jp",loading:"AdminPages_loading__zLU2R",spinner:"AdminPages_spinner__lVOQO",spin:"AdminPages_spin__s6LXq",settingsContainer:"AdminPages_settingsContainer__yidTp",settingsTabs:"AdminPages_settingsTabs__Iw9Zr",tabButton:"AdminPages_tabButton__Swz33",activeTab:"AdminPages_activeTab___YGm8",settingsContent:"AdminPages_settingsContent__5zi5i",settingsForm:"AdminPages_settingsForm__6w8pE",formGroup:"AdminPages_formGroup__MbgWF",input:"AdminPages_input__NjzSf",textarea:"AdminPages_textarea__zm7h5",checkboxLabel:"AdminPages_checkboxLabel__QnbQ4",helpText:"AdminPages_helpText__6QXDW",formRow:"AdminPages_formRow__vrK66",formActions:"AdminPages_formActions__6_1in",saveButton:"AdminPages_saveButton__aoM8K",errorMessage:"AdminPages_errorMessage__CBbWX",successMessage:"AdminPages_successMessage__42J_F",shippingRate:"AdminPages_shippingRate__4pZsF",removeButton:"AdminPages_removeButton__k0hN8",paymentSection:"AdminPages_paymentSection___O13P",securitySection:"AdminPages_securitySection__lvIFb",testButton:"AdminPages_testButton__Xov_6"}},11163:function(e,t,s){e.exports=s(43079)}},function(e){e.O(0,[1664,2575,2888,9774,179],function(){return e(e.s=77706)}),_N_E=e.O()}]);