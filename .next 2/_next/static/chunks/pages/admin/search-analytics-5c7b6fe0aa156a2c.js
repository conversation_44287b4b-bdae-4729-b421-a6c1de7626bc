(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6904],{4353:function(e,n,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/search-analytics",function(){return a(43933)}])},43933:function(e,n,a){"use strict";a.r(n),a.d(n,{default:function(){return u}});var s=a(85893),t=a(67294),r=a(33299),i=a(11163),c=a(9008),d=a.n(c),o=a(41664),l=a.n(o),m=a(15748),h=a.n(m),_=a(80197);function u(){let{data:e,status:n}=(0,r.useSession)(),a=(0,i.useRouter)(),[c,o]=(0,t.useState)("overview"),[m,u]=(0,t.useState)({recentSearches:[],popularSearches:[],zeroResultSearches:[],conversionRate:0}),[b,j]=(0,t.useState)(!0);(0,t.useEffect)(()=>{"loading"===n||e&&e.user.isAdmin||a.push("/auth/signin?callbackUrl=/admin/search-analytics")},[e,n,a]),(0,t.useEffect)(()=>{let e=()=>{j(!0);try{let e=(0,_.nO)().slice(0,20),n=(0,_.Z8)(10),a=(0,_.fT)().slice(0,10),s=(0,_.rw)();u({recentSearches:e,popularSearches:n,zeroResultSearches:a,conversionRate:s})}catch(e){console.error("Error loading search analytics:",e)}finally{j(!1)}};e();let n=setInterval(e,3e4);return()=>clearInterval(n)},[]);let x=e=>new Date(e).toLocaleString();return"loading"===n||b?(0,s.jsxs)("div",{className:h().loadingContainer,children:[(0,s.jsx)("div",{className:h().spinner}),(0,s.jsx)("p",{children:"Loading search analytics..."})]}):e&&e.user.isAdmin?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(d(),{children:[(0,s.jsx)("title",{children:"Search Analytics | MDTS Admin"}),(0,s.jsx)("meta",{name:"description",content:"Search analytics dashboard for MDTS admin"})]}),(0,s.jsxs)("div",{className:h().adminContainer,children:[(0,s.jsxs)("div",{className:h().adminSidebar,children:[(0,s.jsx)("h2",{className:h().adminLogo,children:"MDTS Admin"}),(0,s.jsxs)("nav",{className:h().adminNav,children:[(0,s.jsx)(l(),{href:"/admin",className:h().adminNavItem,children:"Dashboard"}),(0,s.jsx)(l(),{href:"/admin/products",className:h().adminNavItem,children:"Products"}),(0,s.jsx)(l(),{href:"/admin/orders",className:h().adminNavItem,children:"Orders"}),(0,s.jsx)(l(),{href:"/admin/customers",className:h().adminNavItem,children:"Customers"}),(0,s.jsx)(l(),{href:"/admin/search-analytics",className:"".concat(h().adminNavItem," ").concat(h().active),children:"Search Analytics"})]})]}),(0,s.jsxs)("div",{className:h().adminContent,children:[(0,s.jsxs)("header",{className:h().adminHeader,children:[(0,s.jsx)("h1",{children:"Search Analytics"}),(0,s.jsx)("div",{className:h().adminUser,children:(0,s.jsx)("span",{children:e.user.name||e.user.email})})]}),(0,s.jsxs)("div",{className:h().adminTabs,children:[(0,s.jsx)("button",{className:"".concat(h().adminTab," ").concat("overview"===c?h().activeTab:""),onClick:()=>o("overview"),children:"Overview"}),(0,s.jsx)("button",{className:"".concat(h().adminTab," ").concat("recent"===c?h().activeTab:""),onClick:()=>o("recent"),children:"Recent Searches"}),(0,s.jsx)("button",{className:"".concat(h().adminTab," ").concat("popular"===c?h().activeTab:""),onClick:()=>o("popular"),children:"Popular Searches"}),(0,s.jsx)("button",{className:"".concat(h().adminTab," ").concat("zero"===c?h().activeTab:""),onClick:()=>o("zero"),children:"Zero Results"})]}),(0,s.jsxs)("div",{className:h().adminTabContent,children:["overview"===c&&(0,s.jsxs)("div",{className:h().overviewTab,children:[(0,s.jsxs)("div",{className:h().statsGrid,children:[(0,s.jsxs)("div",{className:h().statCard,children:[(0,s.jsx)("h3",{children:"Total Searches"}),(0,s.jsx)("div",{className:h().statValue,children:m.recentSearches.length})]}),(0,s.jsxs)("div",{className:h().statCard,children:[(0,s.jsx)("h3",{children:"Conversion Rate"}),(0,s.jsxs)("div",{className:h().statValue,children:[(100*m.conversionRate).toFixed(2),"%"]})]}),(0,s.jsxs)("div",{className:h().statCard,children:[(0,s.jsx)("h3",{children:"Zero Result Searches"}),(0,s.jsx)("div",{className:h().statValue,children:m.zeroResultSearches.length})]}),(0,s.jsxs)("div",{className:h().statCard,children:[(0,s.jsx)("h3",{children:"Unique Search Terms"}),(0,s.jsx)("div",{className:h().statValue,children:new Set(m.recentSearches.map(e=>e.query)).size})]})]}),(0,s.jsxs)("div",{className:h().overviewCharts,children:[(0,s.jsxs)("div",{className:h().chartCard,children:[(0,s.jsx)("h3",{children:"Top 5 Searches"}),(0,s.jsx)("div",{className:h().barChart,children:m.popularSearches.slice(0,5).map((e,n)=>{var a;return(0,s.jsxs)("div",{className:h().barChartItem,children:[(0,s.jsx)("div",{className:h().barLabel,children:e.query}),(0,s.jsxs)("div",{className:h().barContainer,children:[(0,s.jsx)("div",{className:h().bar,style:{width:"".concat(Math.min(100,e.count/((null===(a=m.popularSearches[0])||void 0===a?void 0:a.count)||1)*100),"%")}}),(0,s.jsx)("div",{className:h().barValue,children:e.count})]})]},n)})})]}),(0,s.jsxs)("div",{className:h().chartCard,children:[(0,s.jsx)("h3",{children:"Top Zero Result Searches"}),(0,s.jsx)("div",{className:h().barChart,children:m.zeroResultSearches.slice(0,5).map((e,n)=>{var a;return(0,s.jsxs)("div",{className:h().barChartItem,children:[(0,s.jsx)("div",{className:h().barLabel,children:e.query}),(0,s.jsxs)("div",{className:h().barContainer,children:[(0,s.jsx)("div",{className:"".concat(h().bar," ").concat(h().redBar),style:{width:"".concat(Math.min(100,e.count/((null===(a=m.zeroResultSearches[0])||void 0===a?void 0:a.count)||1)*100),"%")}}),(0,s.jsx)("div",{className:h().barValue,children:e.count})]})]},n)})})]})]})]}),"recent"===c&&(0,s.jsx)("div",{className:h().recentTab,children:(0,s.jsx)("div",{className:h().tableContainer,children:(0,s.jsxs)("table",{className:h().dataTable,children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{children:"Query"}),(0,s.jsx)("th",{children:"Timestamp"}),(0,s.jsx)("th",{children:"Results"}),(0,s.jsx)("th",{children:"Filters"}),(0,s.jsx)("th",{children:"Session ID"})]})}),(0,s.jsx)("tbody",{children:m.recentSearches.map((e,n)=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{children:e.query}),(0,s.jsx)("td",{children:x(e.timestamp)}),(0,s.jsx)("td",{children:e.resultsCount}),(0,s.jsx)("td",{children:Object.keys(e.filters||{}).length>0?(0,s.jsxs)("details",{children:[(0,s.jsx)("summary",{children:"View Filters"}),(0,s.jsx)("pre",{children:JSON.stringify(e.filters,null,2)})]}):"None"}),(0,s.jsx)("td",{children:e.sessionId})]},n))})]})})}),"popular"===c&&(0,s.jsx)("div",{className:h().popularTab,children:(0,s.jsx)("div",{className:h().tableContainer,children:(0,s.jsxs)("table",{className:h().dataTable,children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{children:"Query"}),(0,s.jsx)("th",{children:"Search Count"}),(0,s.jsx)("th",{children:"Actions"})]})}),(0,s.jsx)("tbody",{children:m.popularSearches.map((e,n)=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{children:e.query}),(0,s.jsx)("td",{children:e.count}),(0,s.jsx)("td",{children:(0,s.jsx)(l(),{href:"/search?q=".concat(encodeURIComponent(e.query)),className:h().actionButton,target:"_blank",children:"View Results"})})]},n))})]})})}),"zero"===c&&(0,s.jsxs)("div",{className:h().zeroTab,children:[(0,s.jsx)("div",{className:h().tableContainer,children:(0,s.jsxs)("table",{className:h().dataTable,children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{children:"Query"}),(0,s.jsx)("th",{children:"Search Count"}),(0,s.jsx)("th",{children:"Actions"})]})}),(0,s.jsx)("tbody",{children:m.zeroResultSearches.map((e,n)=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{children:e.query}),(0,s.jsx)("td",{children:e.count}),(0,s.jsx)("td",{children:(0,s.jsx)(l(),{href:"/admin/products/new?suggested_name=".concat(encodeURIComponent(e.query)),className:h().actionButton,children:"Create Product"})})]},n))})]})}),(0,s.jsxs)("div",{className:h().zeroResultsActions,children:[(0,s.jsx)("h3",{children:"Recommendations"}),(0,s.jsx)("p",{children:"Based on zero-result searches, consider adding these products or categories:"}),(0,s.jsx)("ul",{className:h().recommendationsList,children:m.zeroResultSearches.slice(0,5).map((e,n)=>(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:e.query})," - Searched ",e.count," times with no results"]},n))})]})]})]})]})]})]}):null}},80197:function(e,n,a){"use strict";a.d(n,{HS:function(){return i},J8:function(){return t},U3:function(){return s},Z8:function(){return c},fT:function(){return d},nO:function(){return r},rw:function(){return o}});let s=function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;try{if(!e||""===e.trim())return;let s=r(),t={query:e.trim().toLowerCase(),timestamp:Date.now(),filters:n||{},resultsCount:a,sessionId:l()};s.unshift(t);let i=s.slice(0,100);localStorage.setItem("searchHistory",JSON.stringify(i)),m(t)}catch(e){console.error("Error tracking search:",e)}},t=(e,n,a)=>{try{if(!e||!n)return;let s=i(),t={query:e.trim().toLowerCase(),productId:n,position:a,timestamp:Date.now(),sessionId:l()};s.unshift(t);let r=s.slice(0,100);localStorage.setItem("searchClicks",JSON.stringify(r)),h(t)}catch(e){console.error("Error tracking search result click:",e)}},r=()=>{try{let e=localStorage.getItem("searchHistory");return e?JSON.parse(e):[]}catch(e){return console.error("Error getting search history:",e),[]}},i=()=>{try{let e=localStorage.getItem("searchClicks");return e?JSON.parse(e):[]}catch(e){return console.error("Error getting search clicks:",e),[]}},c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;try{let n=r().reduce((e,n)=>{let a=n.query;return e[a]=(e[a]||0)+1,e},{});return Object.entries(n).map(e=>{let[n,a]=e;return{query:n,count:a}}).sort((e,n)=>n.count-e.count).slice(0,e)}catch(e){return console.error("Error getting popular searches:",e),[]}},d=()=>{try{let e=r().filter(e=>0===e.resultsCount).reduce((e,n)=>{let a=n.query;return e[a]=(e[a]||0)+1,e},{});return Object.entries(e).map(e=>{let[n,a]=e;return{query:n,count:a}}).sort((e,n)=>n.count-e.count)}catch(e){return console.error("Error getting zero-result searches:",e),[]}},o=()=>{try{let e=r(),n=i();if(0===e.length)return 0;return n.length/e.length}catch(e){return console.error("Error calculating search conversion rate:",e),0}},l=()=>{let e=sessionStorage.getItem("searchSessionId");return e||(e="session_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,9)),sessionStorage.setItem("searchSessionId",e)),e},m=async e=>{},h=async e=>{}},15748:function(e){e.exports={adminContainer:"Admin_adminContainer__fnIiy",adminSidebar:"Admin_adminSidebar__ECUVT",adminLogo:"Admin_adminLogo__KRj8V",adminNav:"Admin_adminNav__6VV7e",adminNavItem:"Admin_adminNavItem__vGP0q",active:"Admin_active__BhXLC",adminContent:"Admin_adminContent__la1bh",adminHeader:"Admin_adminHeader__D8Awi",adminUser:"Admin_adminUser__9UhpZ",adminTabs:"Admin_adminTabs__cqvDR",adminTab:"Admin_adminTab__W_1Cu",activeTab:"Admin_activeTab__fvtdm",adminTabContent:"Admin_adminTabContent__2Pyru",loadingContainer:"Admin_loadingContainer__co6Jm",spinner:"Admin_spinner__UFXHG",spin:"Admin_spin__Ah3vg",statsGrid:"Admin_statsGrid__TB_yj",statCard:"Admin_statCard__TRCV8",statValue:"Admin_statValue__7vDB_",overviewCharts:"Admin_overviewCharts__CbCXN",chartCard:"Admin_chartCard__nhG5d",barChart:"Admin_barChart__P7yDX",barChartItem:"Admin_barChartItem__upy5l",barLabel:"Admin_barLabel__zg5pf",barContainer:"Admin_barContainer__rV5lJ",bar:"Admin_bar__KT2HJ",redBar:"Admin_redBar__Ux63t",barValue:"Admin_barValue__by0tg",tableContainer:"Admin_tableContainer__jS7Fw",dataTable:"Admin_dataTable__HJk6Z",actionButton:"Admin_actionButton__UMz0P",zeroResultsActions:"Admin_zeroResultsActions__a8LFP",recommendationsList:"Admin_recommendationsList__kt0vF",marketplacesTab:"Admin_marketplacesTab__F1B_G",tabDescription:"Admin_tabDescription__4232R",customersTab:"Admin_customersTab__K7_If",ordersTab:"Admin_ordersTab__nYVxi",productsTab:"Admin_productsTab__c7E0b",settingsTab:"Admin_settingsTab__uKxYF",adminSection:"Admin_adminSection__vTklz",sectionDescription:"Admin_sectionDescription__fkI2f",errorMessage:"Admin_errorMessage__X7vgq",successMessage:"Admin_successMessage__TfbaZ",integrationCards:"Admin_integrationCards__ahfcD",integrationCard:"Admin_integrationCard__UDVVJ",integrationHeader:"Admin_integrationHeader__ZpOSz",integrationLogo:"Admin_integrationLogo__L6Jp1",integrationInfo:"Admin_integrationInfo__xh2eJ",integrationStatus:"Admin_integrationStatus___Iy_i",statusIndicator:"Admin_statusIndicator__71aId",connected:"Admin_connected__7QFvd",disconnected:"Admin_disconnected__Awmdk",integrationBody:"Admin_integrationBody__DHwlP",integrationField:"Admin_integrationField__vCIAg",apiKeyField:"Admin_apiKeyField__Flra0",copyButton:"Admin_copyButton__jtW5y",databasesList:"Admin_databasesList__ANXzk",webhooksList:"Admin_webhooksList__4hKEU",databaseItem:"Admin_databaseItem__By6lR",webhookItem:"Admin_webhookItem__TtL6j",databaseInfo:"Admin_databaseInfo__5cusK",webhookInfo:"Admin_webhookInfo__rvvlE",databaseName:"Admin_databaseName__mbr33",webhookName:"Admin_webhookName__fRaXr",databaseId:"Admin_databaseId__QqduL",webhookUrl:"Admin_webhookUrl__6HqB3",switch:"Admin_switch__9nB1M",slider:"Admin_slider__GAnsH",connectContainer:"Admin_connectContainer__U5lly",connectButton:"Admin_connectButton__x3D79",disconnectButton:"Admin_disconnectButton__NA_DW",integrationHelp:"Admin_integrationHelp__oqKc1"}},11163:function(e,n,a){e.exports=a(43079)}},function(e){e.O(0,[1664,2888,9774,179],function(){return e(e.s=4353)}),_N_E=e.O()}]);