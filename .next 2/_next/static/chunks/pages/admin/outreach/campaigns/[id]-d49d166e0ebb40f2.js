(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4673],{27954:function(e,a,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/outreach/campaigns/[id]",function(){return n(68842)}])},68842:function(e,a,n){"use strict";n.r(a),n.d(a,{default:function(){return en}});var t=n(85893),s=n(67294),i=n(11163),r=n(33299),l=n(2575),c=n(13536),o=n(84653),d=n(50447),h=n(92137),u=n(13553),x=n(3742),m=n(77975),p=n(44490),j=n(45881),Z=n(21449),g=n(76809),f=n(47987),y=n(18804),b=n(77093),v=n(78738),C=n(64032),S=n(50143),w=n(83254),k=n(32644),E=n(3517),P=n(80409),N=n(55241),T=n(84945),O=n(34454),_=n(99366),A=n(251),W=n(3239),D=n(43615),F=n(7620),q=n(38174),I=n(65900),M=n(88563),R=n(65963),B=n(33476),J=n(35885),L=n(56796),z=n(40454),U=n(99983),H=n(14502),X=n(21018),Y=n(58997),G=n(9958),K=n(75011),Q=n(34521),V=n(61532),$=n(89308);let ee={email:(0,t.jsx)(R.Z,{}),whatsapp:(0,t.jsx)(B.Z,{}),linkedin:(0,t.jsx)(J.Z,{}),facebook:(0,t.jsx)(L.Z,{}),instagram:(0,t.jsx)(z.Z,{}),telegram:(0,t.jsx)(U.Z,{})},ea={draft:"default",scheduled:"primary",in_progress:"info",paused:"warning",stopped:"error",completed:"success"};function en(){var e,a,n,R;let{data:B,status:J}=(0,r.useSession)(),L=(0,i.useRouter)(),{id:z}=L.query,[U,en]=(0,s.useState)(null),[et,es]=(0,s.useState)(!0),[ei,er]=(0,s.useState)(0),[el,ec]=(0,s.useState)(null),[eo,ed]=(0,s.useState)(!1),[eh,eu]=(0,s.useState)(!1),[ex,em]=(0,s.useState)(!1),[ep,ej]=(0,s.useState)({frequency:"daily",startDate:new Date().toISOString().split("T")[0],hour:9,minute:0,batchSize:50}),[eZ,eg]=(0,s.useState)({channel:"",subject:"",template:""}),[ef,ey]=(0,s.useState)([]),[eb,ev]=(0,s.useState)(0),[eC,eS]=(0,s.useState)(10),[ew,ek]=(0,s.useState)(0);(0,s.useEffect)(()=>{"authenticated"===J&&z&&(eE(),eP())},[J,z]),(0,s.useEffect)(()=>{"unauthenticated"===J&&L.push("/signin?callbackUrl=/admin/outreach")},[J,L]);let eE=async()=>{try{es(!0);let e=await fetch("/api/outreach/campaigns/".concat(z));if(!e.ok)throw Error("Failed to fetch campaign");let a=await e.json();en(a)}catch(e){console.error("Error fetching campaign:",e)}finally{es(!1)}},eP=async()=>{try{let e=new URLSearchParams;e.append("page",eb+1),e.append("limit",eC);let a=await fetch("/api/outreach/campaigns/".concat(z,"/recipients?").concat(e.toString()));if(!a.ok)throw Error("Failed to fetch recipients");let n=await a.json();ey(n.recipients),ek(n.pagination.totalCount)}catch(e){console.error("Error fetching recipients:",e)}},eN=()=>{ec(null)},eT=()=>{ed(!1)},eO=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";eg({channel:e,subject:"",template:""}),eu(!0)},e_=()=>{eu(!1)},eA=()=>{em(!1)},eW=e=>{let{name:a,value:n}=e.target;ej(e=>({...e,[a]:n}))},eD=e=>{let{name:a,value:n}=e.target;eg(e=>({...e,[a]:n}))},eF=async()=>{try{if(!(await fetch("/api/outreach/campaigns/".concat(z),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"schedule",scheduleOptions:ep})})).ok)throw Error("Failed to schedule campaign");eT(),eE()}catch(e){console.error("Error scheduling campaign:",e)}},eq=async()=>{try{if(eN(),!(await fetch("/api/outreach/campaigns/".concat(z),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"execute"})})).ok)throw Error("Failed to execute campaign");eE()}catch(e){console.error("Error executing campaign:",e)}},eI=async()=>{try{if(eN(),!(await fetch("/api/outreach/campaigns/".concat(z),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"pause"})})).ok)throw Error("Failed to pause campaign");eE()}catch(e){console.error("Error pausing campaign:",e)}},eM=async()=>{try{if(eN(),!(await fetch("/api/outreach/campaigns/".concat(z),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"resume"})})).ok)throw Error("Failed to resume campaign");eE()}catch(e){console.error("Error resuming campaign:",e)}},eR=async()=>{try{if(eN(),!(await fetch("/api/outreach/campaigns/".concat(z),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"stop"})})).ok)throw Error("Failed to stop campaign");eE()}catch(e){console.error("Error stopping campaign:",e)}},eB=async()=>{try{if(!(await fetch("/api/outreach/campaigns/".concat(z),{method:"DELETE"})).ok)throw Error("Failed to delete campaign");eA(),L.push("/admin/outreach")}catch(e){console.error("Error deleting campaign:",e)}},eJ=async()=>{try{if(!eZ.channel||!eZ.template)return;if(!(await fetch("/api/outreach/campaigns/".concat(z,"/messages"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(eZ)})).ok)throw Error("Failed to create message");e_(),eE()}catch(e){console.error("Error creating message:",e)}};(0,s.useEffect)(()=>{z&&eP()},[z,eb,eC]);let eL=[{value:"email",label:"Email"},{value:"whatsapp",label:"WhatsApp"},{value:"linkedin",label:"LinkedIn"},{value:"facebook",label:"Facebook"},{value:"instagram",label:"Instagram"},{value:"telegram",label:"Telegram"}],ez=()=>{if(!U||!U.messages)return eL;let e=U.messages.map(e=>e.channel);return eL.filter(a=>!e.includes(a.value))},eU=()=>{if(!U||!U.recipientStats)return 0;let{total:e,sent:a,failed:n}=U.recipientStats;return 0===e?0:Math.round((a+n)/e*100)};return(0,t.jsx)(l.Z,{title:U?"Campaign: ".concat(U.name):"Campaign Details",children:(0,t.jsxs)(c.Z,{maxWidth:"xl",children:[et?(0,t.jsx)(o.Z,{sx:{display:"flex",justifyContent:"center",my:4},children:(0,t.jsx)(d.Z,{children:"Loading campaign..."})}):U?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(o.Z,{sx:{mb:4},children:[(0,t.jsxs)(h.Z,{container:!0,spacing:2,alignItems:"center",children:[(0,t.jsx)(h.Z,{item:!0,children:(0,t.jsx)(u.Z,{onClick:()=>L.push("/admin/outreach"),children:(0,t.jsx)(H.Z,{})})}),(0,t.jsx)(h.Z,{item:!0,xs:!0,children:(0,t.jsx)(d.Z,{variant:"h4",component:"h1",children:U.name})}),(0,t.jsx)(h.Z,{item:!0,children:(0,t.jsx)(x.Z,{label:U.status,color:ea[U.status]||"default"})}),(0,t.jsxs)(h.Z,{item:!0,children:[(0,t.jsx)(u.Z,{onClick:e=>{ec(e.currentTarget)},children:(0,t.jsx)(X.Z,{})}),(0,t.jsxs)(m.Z,{anchorEl:el,open:!!el,onClose:eN,children:["draft"===U.status&&(0,t.jsxs)(p.Z,{onClick:()=>{eN(),ed(!0)},children:[(0,t.jsx)(Y.Z,{sx:{mr:1}})," Schedule"]}),"draft"===U.status&&(0,t.jsxs)(p.Z,{onClick:eq,children:[(0,t.jsx)(G.Z,{sx:{mr:1}})," Execute Now"]}),"scheduled"===U.status&&(0,t.jsxs)(p.Z,{onClick:eq,children:[(0,t.jsx)(G.Z,{sx:{mr:1}})," Execute Now"]}),"in_progress"===U.status&&(0,t.jsxs)(p.Z,{onClick:eI,children:[(0,t.jsx)(K.Z,{sx:{mr:1}})," Pause"]}),"paused"===U.status&&(0,t.jsxs)(p.Z,{onClick:eM,children:[(0,t.jsx)(G.Z,{sx:{mr:1}})," Resume"]}),("scheduled"===U.status||"in_progress"===U.status||"paused"===U.status)&&(0,t.jsxs)(p.Z,{onClick:eR,children:[(0,t.jsx)(Q.Z,{sx:{mr:1}})," Stop"]}),(0,t.jsx)(j.Z,{}),(0,t.jsxs)(p.Z,{onClick:()=>{eN(),em(!0)},children:[(0,t.jsx)(V.Z,{sx:{mr:1}})," Delete"]})]})]})]}),U.description&&(0,t.jsx)(d.Z,{variant:"body1",color:"text.secondary",sx:{mt:1},children:U.description}),(0,t.jsx)(o.Z,{sx:{mt:2,display:"flex",gap:1},children:U.channels&&JSON.parse(U.channels).map(e=>(0,t.jsx)(x.Z,{icon:ee[e],label:e,variant:"outlined"},e))})]}),(0,t.jsxs)(h.Z,{container:!0,spacing:3,sx:{mb:4},children:[(0,t.jsx)(h.Z,{item:!0,xs:12,md:3,children:(0,t.jsx)(Z.Z,{children:(0,t.jsxs)(g.Z,{children:[(0,t.jsx)(d.Z,{variant:"h6",gutterBottom:!0,children:"Recipients"}),(0,t.jsx)(d.Z,{variant:"h4",children:(null===(e=U.recipientStats)||void 0===e?void 0:e.total)||0})]})})}),(0,t.jsx)(h.Z,{item:!0,xs:12,md:3,children:(0,t.jsx)(Z.Z,{children:(0,t.jsxs)(g.Z,{children:[(0,t.jsx)(d.Z,{variant:"h6",gutterBottom:!0,children:"Sent"}),(0,t.jsx)(d.Z,{variant:"h4",children:(null===(a=U.recipientStats)||void 0===a?void 0:a.sent)||0})]})})}),(0,t.jsx)(h.Z,{item:!0,xs:12,md:3,children:(0,t.jsx)(Z.Z,{children:(0,t.jsxs)(g.Z,{children:[(0,t.jsx)(d.Z,{variant:"h6",gutterBottom:!0,children:"Failed"}),(0,t.jsx)(d.Z,{variant:"h4",children:(null===(n=U.recipientStats)||void 0===n?void 0:n.failed)||0})]})})}),(0,t.jsx)(h.Z,{item:!0,xs:12,md:3,children:(0,t.jsx)(Z.Z,{children:(0,t.jsxs)(g.Z,{children:[(0,t.jsx)(d.Z,{variant:"h6",gutterBottom:!0,children:"Pending"}),(0,t.jsx)(d.Z,{variant:"h4",children:(null===(R=U.recipientStats)||void 0===R?void 0:R.pending)||0})]})})})]}),("in_progress"===U.status||"paused"===U.status)&&(0,t.jsxs)(o.Z,{sx:{mb:4},children:[(0,t.jsxs)(d.Z,{variant:"body2",gutterBottom:!0,children:["Progress: ",eU(),"%"]}),(0,t.jsx)(f.Z,{variant:"determinate",value:eU(),sx:{height:10,borderRadius:5}})]}),(0,t.jsxs)(o.Z,{sx:{mb:4},children:[(0,t.jsxs)(y.Z,{value:ei,onChange:(e,a)=>{er(a)},children:[(0,t.jsx)(b.Z,{label:"Messages"}),(0,t.jsx)(b.Z,{label:"Recipients"}),(0,t.jsx)(b.Z,{label:"Analytics"})]}),(0,t.jsx)(j.Z,{}),0===ei&&(0,t.jsxs)(o.Z,{sx:{mt:3},children:[(0,t.jsxs)(o.Z,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[(0,t.jsx)(d.Z,{variant:"h6",children:"Campaign Messages"}),(0,t.jsx)(v.Z,{variant:"contained",startIcon:(0,t.jsx)($.Z,{}),onClick:()=>eO(),disabled:0===ez().length,children:"Add Message"})]}),U.messages&&0===U.messages.length?(0,t.jsx)(C.Z,{severity:"info",children:"No messages have been created for this campaign yet. Add messages for each channel you want to use."}):(0,t.jsx)(h.Z,{container:!0,spacing:3,children:U.messages&&U.messages.map(e=>(0,t.jsx)(h.Z,{item:!0,xs:12,md:6,children:(0,t.jsx)(Z.Z,{children:(0,t.jsxs)(g.Z,{children:[(0,t.jsxs)(o.Z,{sx:{display:"flex",alignItems:"center",mb:2},children:[ee[e.channel],(0,t.jsx)(d.Z,{variant:"h6",sx:{ml:1},children:e.channel.charAt(0).toUpperCase()+e.channel.slice(1)})]}),"email"===e.channel&&(0,t.jsxs)(d.Z,{variant:"subtitle1",gutterBottom:!0,children:["Subject: ",e.subject]}),(0,t.jsx)(d.Z,{variant:"body2",color:"text.secondary",children:e.template.length>200?e.template.substring(0,200)+"...":e.template})]})})},e.id))})]}),1===ei&&(0,t.jsxs)(o.Z,{sx:{mt:3},children:[(0,t.jsxs)(o.Z,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[(0,t.jsx)(d.Z,{variant:"h6",children:"Campaign Recipients"}),(0,t.jsx)(v.Z,{variant:"contained",startIcon:(0,t.jsx)($.Z,{}),onClick:()=>L.push("/admin/outreach/campaigns/".concat(z,"/recipients/add")),children:"Add Recipients"})]}),(0,t.jsxs)(S.Z,{component:w.Z,children:[(0,t.jsxs)(k.Z,{children:[(0,t.jsx)(E.Z,{children:(0,t.jsxs)(P.Z,{children:[(0,t.jsx)(N.Z,{children:"Name"}),(0,t.jsx)(N.Z,{children:"Email"}),(0,t.jsx)(N.Z,{children:"Phone"}),(0,t.jsx)(N.Z,{children:"Platform"}),(0,t.jsx)(N.Z,{children:"Status"}),(0,t.jsx)(N.Z,{children:"Added"})]})}),(0,t.jsx)(T.Z,{children:0===ef.length?(0,t.jsx)(P.Z,{children:(0,t.jsx)(N.Z,{colSpan:6,align:"center",children:"No recipients found"})}):ef.map(e=>(0,t.jsxs)(P.Z,{children:[(0,t.jsx)(N.Z,{children:e.name||"N/A"}),(0,t.jsx)(N.Z,{children:e.email||"N/A"}),(0,t.jsx)(N.Z,{children:e.phone||"N/A"}),(0,t.jsx)(N.Z,{children:e.platform?"".concat(e.platform," (").concat(e.platform_id,")"):"N/A"}),(0,t.jsx)(N.Z,{children:(0,t.jsx)(x.Z,{label:e.status,color:"sent"===e.status?"success":"failed"===e.status?"error":"default",size:"small"})}),(0,t.jsx)(N.Z,{children:new Date(e.added_at).toLocaleDateString()})]},e.id))})]}),(0,t.jsx)(O.Z,{rowsPerPageOptions:[10,25,50,100],component:"div",count:ew,rowsPerPage:eC,page:eb,onPageChange:(e,a)=>{ev(a)},onRowsPerPageChange:e=>{eS(parseInt(e.target.value,10)),ev(0)}})]})]}),2===ei&&(0,t.jsxs)(o.Z,{sx:{mt:3},children:[(0,t.jsx)(d.Z,{variant:"h6",gutterBottom:!0,children:"Campaign Analytics"}),(0,t.jsx)(C.Z,{severity:"info",sx:{mb:2},children:"Detailed analytics will be available once the campaign has started sending messages."})]})]})]}):(0,t.jsx)(o.Z,{sx:{display:"flex",justifyContent:"center",my:4},children:(0,t.jsx)(d.Z,{children:"Campaign not found"})}),(0,t.jsxs)(_.Z,{open:eo,onClose:eT,maxWidth:"sm",fullWidth:!0,children:[(0,t.jsx)(A.Z,{children:"Schedule Campaign"}),(0,t.jsx)(W.Z,{children:(0,t.jsxs)(o.Z,{sx:{pt:1},children:[(0,t.jsxs)(D.Z,{fullWidth:!0,margin:"normal",children:[(0,t.jsx)(F.Z,{id:"frequency-label",children:"Frequency"}),(0,t.jsxs)(q.Z,{labelId:"frequency-label",name:"frequency",value:ep.frequency,onChange:eW,label:"Frequency",children:[(0,t.jsx)(p.Z,{value:"once",children:"One time"}),(0,t.jsx)(p.Z,{value:"hourly",children:"Hourly"}),(0,t.jsx)(p.Z,{value:"daily",children:"Daily"}),(0,t.jsx)(p.Z,{value:"weekly",children:"Weekly"}),(0,t.jsx)(p.Z,{value:"monthly",children:"Monthly"})]})]}),(0,t.jsx)(I.Z,{fullWidth:!0,label:"Start Date",name:"startDate",type:"date",value:ep.startDate,onChange:eW,margin:"normal",InputLabelProps:{shrink:!0}}),(0,t.jsxs)(h.Z,{container:!0,spacing:2,children:[(0,t.jsx)(h.Z,{item:!0,xs:6,children:(0,t.jsx)(I.Z,{fullWidth:!0,label:"Hour",name:"hour",type:"number",value:ep.hour,onChange:eW,margin:"normal",inputProps:{min:0,max:23}})}),(0,t.jsx)(h.Z,{item:!0,xs:6,children:(0,t.jsx)(I.Z,{fullWidth:!0,label:"Minute",name:"minute",type:"number",value:ep.minute,onChange:eW,margin:"normal",inputProps:{min:0,max:59}})})]}),(0,t.jsx)(I.Z,{fullWidth:!0,label:"Batch Size",name:"batchSize",type:"number",value:ep.batchSize,onChange:eW,margin:"normal",helperText:"Number of recipients to process in each batch",inputProps:{min:1,max:1e3}})]})}),(0,t.jsxs)(M.Z,{children:[(0,t.jsx)(v.Z,{onClick:eT,children:"Cancel"}),(0,t.jsx)(v.Z,{onClick:eF,variant:"contained",color:"primary",children:"Schedule"})]})]}),(0,t.jsxs)(_.Z,{open:eh,onClose:e_,maxWidth:"md",fullWidth:!0,children:[(0,t.jsx)(A.Z,{children:"Add Campaign Message"}),(0,t.jsx)(W.Z,{children:(0,t.jsxs)(o.Z,{sx:{pt:1},children:[(0,t.jsxs)(D.Z,{fullWidth:!0,margin:"normal",required:!0,children:[(0,t.jsx)(F.Z,{id:"message-channel-label",children:"Channel"}),(0,t.jsx)(q.Z,{labelId:"message-channel-label",name:"channel",value:eZ.channel,onChange:eD,label:"Channel",children:ez().map(e=>(0,t.jsx)(p.Z,{value:e.value,children:e.label},e.value))})]}),"email"===eZ.channel&&(0,t.jsx)(I.Z,{fullWidth:!0,label:"Subject",name:"subject",value:eZ.subject,onChange:eD,margin:"normal",required:!0}),(0,t.jsx)(I.Z,{fullWidth:!0,label:"Message Template",name:"template",value:eZ.template,onChange:eD,margin:"normal",multiline:!0,rows:10,required:!0,helperText:"You can use variables like {{recipient.name}}, {{recipient.email}}, etc."})]})}),(0,t.jsxs)(M.Z,{children:[(0,t.jsx)(v.Z,{onClick:e_,children:"Cancel"}),(0,t.jsx)(v.Z,{onClick:eJ,variant:"contained",color:"primary",disabled:!eZ.channel||!eZ.template||"email"===eZ.channel&&!eZ.subject,children:"Add Message"})]})]}),(0,t.jsxs)(_.Z,{open:ex,onClose:eA,children:[(0,t.jsx)(A.Z,{children:"Delete Campaign"}),(0,t.jsx)(W.Z,{children:(0,t.jsx)(d.Z,{children:"Are you sure you want to delete this campaign? This action cannot be undone."})}),(0,t.jsxs)(M.Z,{children:[(0,t.jsx)(v.Z,{onClick:eA,children:"Cancel"}),(0,t.jsx)(v.Z,{onClick:eB,variant:"contained",color:"error",children:"Delete"})]})]})]})})}}},function(e){e.O(0,[1664,986,923,4053,4102,2575,2888,9774,179],function(){return e(e.s=27954)}),_N_E=e.O()}]);