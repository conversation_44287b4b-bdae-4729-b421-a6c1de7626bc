(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3164],{51472:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/outreach/campaigns/[id]/recipients/add",function(){return n(2007)}])},2007:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return es}});var r=n(85893),a=n(67294),i=n(11163),l=n(33299),o=n(2575),s=n(13536),c=n(84653),d=n(50447),u=n(92137),m=n(13553),p=n(18804),h=n(77093),f=n(45881),x=n(64032),g=n(83254),v=n(65900),Z=n(78738),j=n(27178),y=n(50143),C=n(32644),b=n(3517),w=n(80409),S=n(55241),E=n(84945),k=n(49348),P=n(75198),I=n(62923),R=n(83592),L=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{autoHideDuration:t=null,disableWindowBlurListener:n=!1,onClose:r,open:i,resumeHideDuration:l}=e,o=(0,P.Z)();a.useEffect(()=>{if(i)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){e.defaultPrevented||"Escape"!==e.key||null==r||r(e,"escapeKeyDown")}},[i,r]);let s=(0,I.Z)((e,t)=>{null==r||r(e,t)}),c=(0,I.Z)(e=>{r&&null!=e&&o.start(e,()=>{s(null,"timeout")})});a.useEffect(()=>(i&&c(t),o.clear),[i,t,c,o]);let d=o.clear,u=a.useCallback(()=>{null!=t&&c(null!=l?l:.5*t)},[t,l,c]),m=e=>t=>{let n=e.onBlur;null==n||n(t),u()},p=e=>t=>{let n=e.onFocus;null==n||n(t),d()},h=e=>t=>{let n=e.onMouseEnter;null==n||n(t),d()},f=e=>t=>{let n=e.onMouseLeave;null==n||n(t),u()};return a.useEffect(()=>{if(!n&&i)return window.addEventListener("focus",u),window.addEventListener("blur",d),()=>{window.removeEventListener("focus",u),window.removeEventListener("blur",d)}},[n,i,u,d]),{getRootProps:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n={...(0,R.Z)(e),...(0,R.Z)(t)};return{role:"presentation",...t,...n,onBlur:m(n),onFocus:p(n),onMouseEnter:h(n),onMouseLeave:f(n)}},onClickAway:e=>{null==r||r(e,"clickaway")}}},O=n(24038),T=n(67603),A=n(27088);function M(e){return e.substring(2).toLowerCase()}function N(e){let{children:t,disableReactTree:n=!1,mouseEvent:r="onClick",onClickAway:i,touchEvent:l="onTouchEnd"}=e,o=a.useRef(!1),s=a.useRef(null),c=a.useRef(!1),d=a.useRef(!1);a.useEffect(()=>(setTimeout(()=>{c.current=!0},0),()=>{c.current=!1}),[]);let u=(0,O.Z)((0,A.Z)(t),s),m=(0,I.Z)(e=>{let t=d.current;d.current=!1;let r=(0,T.Z)(s.current);if(c.current&&s.current&&(!("clientX"in e)||!(r.documentElement.clientWidth<e.clientX)&&!(r.documentElement.clientHeight<e.clientY))){if(o.current){o.current=!1;return}(e.composedPath?e.composedPath().includes(s.current):!r.documentElement.contains(e.target)||s.current.contains(e.target))||!n&&t||i(e)}}),p=e=>n=>{d.current=!0;let r=t.props[e];r&&r(n)},h={ref:u};return!1!==l&&(h[l]=p(l)),a.useEffect(()=>{if(!1!==l){let e=M(l),t=(0,T.Z)(s.current),n=()=>{o.current=!0};return t.addEventListener(e,m),t.addEventListener("touchmove",n),()=>{t.removeEventListener(e,m),t.removeEventListener("touchmove",n)}}},[m,l]),!1!==r&&(h[r]=p(r)),a.useEffect(()=>{if(!1!==r){let e=M(r),t=(0,T.Z)(s.current);return t.addEventListener(e,m),()=>{t.removeEventListener(e,m)}}},[m,r]),a.cloneElement(t,h)}var F=n(26061),W=n(40533),_=n(99551),z=n(67631),V=n(57315),B=n(38438),D=n(8780),U=n(93784),X=n(57480),q=n(1801);function H(e){return(0,q.ZP)("MuiSnackbarContent",e)}(0,X.Z)("MuiSnackbarContent",["root","message","action"]);let G=e=>{let{classes:t}=e;return(0,k.Z)({root:["root"],action:["action"],message:["message"]},H,t)},J=(0,F.ZP)(g.Z,{name:"MuiSnackbarContent",slot:"Root",overridesResolver:(e,t)=>t.root})((0,_.Z)(e=>{let{theme:t}=e,n="light"===t.palette.mode?.8:.98,r=(0,U._4)(t.palette.background.default,n);return{...t.typography.body2,color:t.vars?t.vars.palette.SnackbarContent.color:t.palette.getContrastText(r),backgroundColor:t.vars?t.vars.palette.SnackbarContent.bg:r,display:"flex",alignItems:"center",flexWrap:"wrap",padding:"6px 16px",borderRadius:(t.vars||t).shape.borderRadius,flexGrow:1,[t.breakpoints.up("sm")]:{flexGrow:"initial",minWidth:288}}})),K=(0,F.ZP)("div",{name:"MuiSnackbarContent",slot:"Message",overridesResolver:(e,t)=>t.message})({padding:"8px 0"}),Y=(0,F.ZP)("div",{name:"MuiSnackbarContent",slot:"Action",overridesResolver:(e,t)=>t.action})({display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:16,marginRight:-8}),Q=a.forwardRef(function(e,t){let n=(0,z.i)({props:e,name:"MuiSnackbarContent"}),{action:a,className:i,message:l,role:o="alert",...s}=n,c=G(n);return(0,r.jsxs)(J,{role:o,square:!0,elevation:6,className:(0,D.Z)(c.root,i),ownerState:n,ref:t,...s,children:[(0,r.jsx)(K,{className:c.message,ownerState:n,children:l}),a?(0,r.jsx)(Y,{className:c.action,ownerState:n,children:a}):null]})});function $(e){return(0,q.ZP)("MuiSnackbar",e)}(0,X.Z)("MuiSnackbar",["root","anchorOriginTopCenter","anchorOriginBottomCenter","anchorOriginTopRight","anchorOriginBottomRight","anchorOriginTopLeft","anchorOriginBottomLeft"]);var ee=n(61484);let et=e=>{let{classes:t,anchorOrigin:n}=e,r={root:["root","anchorOrigin".concat((0,V.Z)(n.vertical)).concat((0,V.Z)(n.horizontal))]};return(0,k.Z)(r,$,t)},en=(0,F.ZP)("div",{name:"MuiSnackbar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,t["anchorOrigin".concat((0,V.Z)(n.anchorOrigin.vertical)).concat((0,V.Z)(n.anchorOrigin.horizontal))]]}})((0,_.Z)(e=>{let{theme:t}=e;return{zIndex:(t.vars||t).zIndex.snackbar,position:"fixed",display:"flex",left:8,right:8,justifyContent:"center",alignItems:"center",variants:[{props:e=>{let{ownerState:t}=e;return"top"===t.anchorOrigin.vertical},style:{top:8,[t.breakpoints.up("sm")]:{top:24}}},{props:e=>{let{ownerState:t}=e;return"top"!==t.anchorOrigin.vertical},style:{bottom:8,[t.breakpoints.up("sm")]:{bottom:24}}},{props:e=>{let{ownerState:t}=e;return"left"===t.anchorOrigin.horizontal},style:{justifyContent:"flex-start",[t.breakpoints.up("sm")]:{left:24,right:"auto"}}},{props:e=>{let{ownerState:t}=e;return"right"===t.anchorOrigin.horizontal},style:{justifyContent:"flex-end",[t.breakpoints.up("sm")]:{right:24,left:"auto"}}},{props:e=>{let{ownerState:t}=e;return"center"===t.anchorOrigin.horizontal},style:{[t.breakpoints.up("sm")]:{left:"50%",right:"auto",transform:"translateX(-50%)"}}}]}})),er=a.forwardRef(function(e,t){let n=(0,z.i)({props:e,name:"MuiSnackbar"}),i=(0,W.Z)(),l={enter:i.transitions.duration.enteringScreen,exit:i.transitions.duration.leavingScreen},{action:o,anchorOrigin:{vertical:s,horizontal:c}={vertical:"bottom",horizontal:"left"},autoHideDuration:d=null,children:u,className:m,ClickAwayListenerProps:p,ContentProps:h,disableWindowBlurListener:f=!1,message:x,onBlur:g,onClose:v,onFocus:Z,onMouseEnter:j,onMouseLeave:y,open:C,resumeHideDuration:b,slots:w={},slotProps:S={},TransitionComponent:E,transitionDuration:k=l,TransitionProps:{onEnter:P,onExited:I,...R}={},...O}=n,T={...n,anchorOrigin:{vertical:s,horizontal:c},autoHideDuration:d,disableWindowBlurListener:f,TransitionComponent:E,transitionDuration:k},A=et(T),{getRootProps:M,onClickAway:F}=L({...T}),[_,V]=a.useState(!0),D=e=>{V(!0),I&&I(e)},U=(e,t)=>{V(!1),P&&P(e,t)},X={slots:{transition:E,...w},slotProps:{content:h,clickAwayListener:p,transition:R,...S}},[q,H]=(0,ee.Z)("root",{ref:t,className:[A.root,m],elementType:en,getSlotProps:M,externalForwardedProps:{...X,...O},ownerState:T}),[G,{ownerState:J,...K}]=(0,ee.Z)("clickAwayListener",{elementType:N,externalForwardedProps:X,getSlotProps:e=>({onClickAway:function(){for(var t,n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];null===(t=e.onClickAway)||void 0===t||t.call(e,...r),F(...r)}}),ownerState:T}),[Y,$]=(0,ee.Z)("content",{elementType:Q,shouldForwardComponentProp:!0,externalForwardedProps:X,additionalProps:{message:x,action:o},ownerState:T}),[er,ea]=(0,ee.Z)("transition",{elementType:B.Z,externalForwardedProps:X,getSlotProps:e=>({onEnter:function(){for(var t,n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];null===(t=e.onEnter)||void 0===t||t.call(e,...r),U(...r)},onExited:function(){for(var t,n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];null===(t=e.onExited)||void 0===t||t.call(e,...r),D(...r)}}),additionalProps:{appear:!0,in:C,timeout:k,direction:"top"===s?"down":"up"},ownerState:T});return!C&&_?null:(0,r.jsx)(G,{...K,...w.clickAwayListener&&{ownerState:J},children:(0,r.jsx)(q,{...H,children:(0,r.jsx)(er,{...ea,children:u||(0,r.jsx)(Y,{...$})})})})});var ea=n(14502),ei=n(61532),el=n(89308),eo=(0,n(5496).Z)((0,r.jsx)("path",{d:"M5 20h14v-2H5zm0-10h4v6h6v-6h4l-7-7z"}),"Upload");function es(){let{data:e,status:t}=(0,l.useSession)(),n=(0,i.useRouter)(),{id:k}=n.query,[P,I]=(0,a.useState)(null),[R,L]=(0,a.useState)(!0),[O,T]=(0,a.useState)(0),[A,M]=(0,a.useState)([{name:"",email:"",phone:"",platform:"",platformId:""}]),[N,F]=(0,a.useState)(null),[W,_]=(0,a.useState)([]),[z,V]=(0,a.useState)([]),[B,D]=(0,a.useState)({name:"",email:"",phone:"",platform:"",platformId:""}),[U,X]=(0,a.useState)(!1),[q,H]=(0,a.useState)({open:!1,message:"",severity:"info"});(0,a.useEffect)(()=>{"authenticated"===t&&k&&G()},[t,k]),(0,a.useEffect)(()=>{"unauthenticated"===t&&n.push("/signin?callbackUrl=/admin/outreach")},[t,n]);let G=async()=>{try{L(!0);let e=await fetch("/api/outreach/campaigns/".concat(k));if(!e.ok)throw Error("Failed to fetch campaign");let t=await e.json();I(t)}catch(e){console.error("Error fetching campaign:",e),ee("Failed to fetch campaign details","error")}finally{L(!1)}},J=(e,t,n)=>{let r=[...A];r[e]={...r[e],[t]:n},M(r)},K=e=>{if(1===A.length){M([{name:"",email:"",phone:"",platform:"",platformId:""}]);return}M(A.filter((t,n)=>n!==e))},Y=(e,t)=>{D({...B,[e]:t})},Q=async()=>{try{let e=A.filter(e=>e.name||e.email||e.phone||e.platform&&e.platformId);if(0===e.length){ee("Please add at least one recipient with valid information","error");return}if(X(!0),!(await fetch("/api/outreach/campaigns/".concat(k,"/recipients"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({recipients:e})})).ok)throw Error("Failed to add recipients");ee("Successfully added ".concat(e.length," recipients"),"success"),M([{name:"",email:"",phone:"",platform:"",platformId:""}]),setTimeout(()=>{n.push("/admin/outreach/campaigns/".concat(k))},1500)}catch(e){console.error("Error adding recipients:",e),ee("Failed to add recipients","error")}finally{X(!1)}},$=async()=>{try{if(!N){ee("Please upload a CSV file","error");return}if(!B.name&&!B.email&&!B.phone&&!(B.platform&&B.platformId)){ee("Please map at least one identification field (name, email, phone, or platform with platformId)","error");return}X(!0);let e=new FileReader;e.onload=async e=>{try{let t=e.target.result.split("\\n"),r=t[0].split(",").map(e=>e.trim()),a=[];for(let e=1;e<t.length;e++){if(""===t[e].trim())continue;let n=t[e].split(",").map(e=>e.trim()),i={name:"",email:"",phone:"",platform:"",platformId:""};Object.keys(B).forEach(e=>{if(B[e]){let t=r.indexOf(B[e]);-1!==t&&(i[e]=n[t]||"")}}),(i.name||i.email||i.phone||i.platform&&i.platformId)&&a.push(i)}if(0===a.length){ee("No valid recipients found in CSV file","error"),X(!1);return}if(!(await fetch("/api/outreach/campaigns/".concat(k,"/recipients"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({recipients:a})})).ok)throw Error("Failed to add recipients");ee("Successfully added ".concat(a.length," recipients from CSV"),"success"),F(null),_([]),V([]),D({name:"",email:"",phone:"",platform:"",platformId:""});let i=document.getElementById("csv-file-input");i&&(i.value=""),setTimeout(()=>{n.push("/admin/outreach/campaigns/".concat(k))},1500)}catch(e){console.error("Error processing CSV:",e),ee("Failed to process CSV file","error"),X(!1)}},e.readAsText(N)}catch(e){console.error("Error adding recipients from CSV:",e),ee("Failed to add recipients from CSV","error"),X(!1)}},ee=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";H({open:!0,message:e,severity:t})};return(0,r.jsx)(o.Z,{title:P?"Add Recipients: ".concat(P.name):"Add Recipients",children:(0,r.jsxs)(s.Z,{maxWidth:"xl",children:[R?(0,r.jsx)(c.Z,{sx:{display:"flex",justifyContent:"center",my:4},children:(0,r.jsx)(d.Z,{children:"Loading campaign..."})}):P?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.Z,{sx:{mb:4},children:(0,r.jsxs)(u.Z,{container:!0,spacing:2,alignItems:"center",children:[(0,r.jsx)(u.Z,{item:!0,children:(0,r.jsx)(m.Z,{onClick:()=>n.push("/admin/outreach/campaigns/".concat(k)),children:(0,r.jsx)(ea.Z,{})})}),(0,r.jsxs)(u.Z,{item:!0,xs:!0,children:[(0,r.jsx)(d.Z,{variant:"h4",component:"h1",children:"Add Recipients"}),(0,r.jsxs)(d.Z,{variant:"subtitle1",color:"text.secondary",children:["Campaign: ",P.name]})]})]})}),(0,r.jsxs)(c.Z,{sx:{mb:4},children:[(0,r.jsxs)(p.Z,{value:O,onChange:(e,t)=>{T(t)},children:[(0,r.jsx)(h.Z,{label:"Manual Entry"}),(0,r.jsx)(h.Z,{label:"CSV Upload"})]}),(0,r.jsx)(f.Z,{}),0===O&&(0,r.jsxs)(c.Z,{sx:{mt:3},children:[(0,r.jsx)(x.Z,{severity:"info",sx:{mb:3},children:"Add recipients manually by filling in their details. At least one of name, email, phone, or platform with platform ID is required."}),(0,r.jsx)(g.Z,{sx:{p:3,mb:3},children:A.map((e,t)=>(0,r.jsxs)(u.Z,{container:!0,spacing:2,sx:{mb:t<A.length-1?3:0},children:[(0,r.jsx)(u.Z,{item:!0,xs:12,sm:6,md:3,children:(0,r.jsx)(v.Z,{fullWidth:!0,label:"Name",value:e.name,onChange:e=>J(t,"name",e.target.value)})}),(0,r.jsx)(u.Z,{item:!0,xs:12,sm:6,md:3,children:(0,r.jsx)(v.Z,{fullWidth:!0,label:"Email",type:"email",value:e.email,onChange:e=>J(t,"email",e.target.value)})}),(0,r.jsx)(u.Z,{item:!0,xs:12,sm:6,md:2,children:(0,r.jsx)(v.Z,{fullWidth:!0,label:"Phone",value:e.phone,onChange:e=>J(t,"phone",e.target.value)})}),(0,r.jsx)(u.Z,{item:!0,xs:12,sm:6,md:2,children:(0,r.jsx)(v.Z,{fullWidth:!0,label:"Platform",value:e.platform,onChange:e=>J(t,"platform",e.target.value),helperText:"e.g. linkedin, facebook"})}),(0,r.jsx)(u.Z,{item:!0,xs:12,sm:6,md:2,children:(0,r.jsx)(v.Z,{fullWidth:!0,label:"Platform ID",value:e.platformId,onChange:e=>J(t,"platformId",e.target.value)})}),(0,r.jsx)(u.Z,{item:!0,xs:12,sm:6,md:1,sx:{display:"flex",alignItems:"center"},children:(0,r.jsx)(m.Z,{color:"error",onClick:()=>K(t),"aria-label":"Remove recipient",children:(0,r.jsx)(ei.Z,{})})}),t<A.length-1&&(0,r.jsx)(u.Z,{item:!0,xs:12,children:(0,r.jsx)(f.Z,{})})]},t))}),(0,r.jsxs)(c.Z,{sx:{display:"flex",justifyContent:"space-between",mb:3},children:[(0,r.jsx)(Z.Z,{variant:"outlined",startIcon:(0,r.jsx)(el.Z,{}),onClick:()=>{M([...A,{name:"",email:"",phone:"",platform:"",platformId:""}])},children:"Add Another Recipient"}),(0,r.jsx)(Z.Z,{variant:"contained",color:"primary",onClick:Q,disabled:U,children:U?(0,r.jsx)(j.Z,{size:24}):"Add Recipients"})]})]}),1===O&&(0,r.jsxs)(c.Z,{sx:{mt:3},children:[(0,r.jsx)(x.Z,{severity:"info",sx:{mb:3},children:"Upload a CSV file with recipient information. The first row should contain headers."}),(0,r.jsxs)(g.Z,{sx:{p:3,mb:3},children:[(0,r.jsxs)(c.Z,{sx:{mb:3},children:[(0,r.jsxs)(Z.Z,{variant:"outlined",component:"label",startIcon:(0,r.jsx)(eo,{}),children:["Upload CSV File",(0,r.jsx)("input",{id:"csv-file-input",type:"file",accept:".csv",hidden:!0,onChange:e=>{let t=e.target.files[0];if(!t)return;F(t);let n=new FileReader;n.onload=e=>{let t=e.target.result.split("\\n");if(t.length<2){ee("CSV file must contain at least a header row and one data row","error");return}let n=t[0].split(",").map(e=>e.trim());V(n);let r={};n.forEach(e=>{let t=e.toLowerCase();t.includes("name")&&(r.name=e),t.includes("email")&&(r.email=e),t.includes("phone")&&(r.phone=e),t.includes("platform")&&(r.platform=e),(t.includes("platform_id")||t.includes("platformid"))&&(r.platformId=e)}),D(r);let a=[];for(let e=1;e<Math.min(t.length,6);e++){if(""===t[e].trim())continue;let r=t[e].split(",").map(e=>e.trim()),i={};n.forEach((e,t)=>{i[e]=r[t]||""}),a.push(i)}_(a)},n.readAsText(t)}})]}),N&&(0,r.jsxs)(d.Z,{variant:"body2",sx:{mt:1},children:["Selected file: ",N.name]})]}),z.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.Z,{variant:"h6",gutterBottom:!0,children:"Map CSV Columns"}),(0,r.jsxs)(u.Z,{container:!0,spacing:2,sx:{mb:3},children:[(0,r.jsx)(u.Z,{item:!0,xs:12,sm:6,md:4,children:(0,r.jsxs)(v.Z,{fullWidth:!0,select:!0,label:"Name Column",value:B.name,onChange:e=>Y("name",e.target.value),SelectProps:{native:!0},children:[(0,r.jsx)("option",{value:"",children:"Not mapped"}),z.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})}),(0,r.jsx)(u.Z,{item:!0,xs:12,sm:6,md:4,children:(0,r.jsxs)(v.Z,{fullWidth:!0,select:!0,label:"Email Column",value:B.email,onChange:e=>Y("email",e.target.value),SelectProps:{native:!0},children:[(0,r.jsx)("option",{value:"",children:"Not mapped"}),z.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})}),(0,r.jsx)(u.Z,{item:!0,xs:12,sm:6,md:4,children:(0,r.jsxs)(v.Z,{fullWidth:!0,select:!0,label:"Phone Column",value:B.phone,onChange:e=>Y("phone",e.target.value),SelectProps:{native:!0},children:[(0,r.jsx)("option",{value:"",children:"Not mapped"}),z.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})}),(0,r.jsx)(u.Z,{item:!0,xs:12,sm:6,md:4,children:(0,r.jsxs)(v.Z,{fullWidth:!0,select:!0,label:"Platform Column",value:B.platform,onChange:e=>Y("platform",e.target.value),SelectProps:{native:!0},children:[(0,r.jsx)("option",{value:"",children:"Not mapped"}),z.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})}),(0,r.jsx)(u.Z,{item:!0,xs:12,sm:6,md:4,children:(0,r.jsxs)(v.Z,{fullWidth:!0,select:!0,label:"Platform ID Column",value:B.platformId,onChange:e=>Y("platformId",e.target.value),SelectProps:{native:!0},children:[(0,r.jsx)("option",{value:"",children:"Not mapped"}),z.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})})]}),(0,r.jsx)(d.Z,{variant:"h6",gutterBottom:!0,children:"Preview"}),(0,r.jsx)(y.Z,{component:g.Z,sx:{mb:3},children:(0,r.jsxs)(C.Z,{size:"small",children:[(0,r.jsx)(b.Z,{children:(0,r.jsx)(w.Z,{children:z.map(e=>(0,r.jsx)(S.Z,{children:e},e))})}),(0,r.jsx)(E.Z,{children:W.map((e,t)=>(0,r.jsx)(w.Z,{children:z.map(t=>(0,r.jsx)(S.Z,{children:e[t]},t))},t))})]})}),(0,r.jsx)(c.Z,{sx:{display:"flex",justifyContent:"flex-end"},children:(0,r.jsx)(Z.Z,{variant:"contained",color:"primary",onClick:$,disabled:U,children:U?(0,r.jsx)(j.Z,{size:24}):"Import Recipients"})})]})]})]})]})]}):(0,r.jsx)(c.Z,{sx:{display:"flex",justifyContent:"center",my:4},children:(0,r.jsx)(d.Z,{children:"Campaign not found"})}),(0,r.jsx)(er,{open:q.open,autoHideDuration:6e3,onClose:()=>{H({...q,open:!1})},message:q.message,severity:q.severity})]})})}}},function(e){e.O(0,[1664,986,923,2575,2888,9774,179],function(){return e(e.s=51472)}),_N_E=e.O()}]);