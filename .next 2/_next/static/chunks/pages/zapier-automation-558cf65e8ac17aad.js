(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5648],{22285:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/zapier-automation",function(){return r(98552)}])},98552:function(e,t,r){"use strict";r.r(t);var i=r(85893),o=r(67294),n=r(9008),d=r.n(n),s=r(56029);t.default=()=>{let[e,t]=(0,o.useState)("support"),[r,n]=(0,o.useState)({name:"",email:"",subject:"",message:"",productId:"prod-123",productName:"iPhone 13 Pro Screen",rating:5,reviewText:"",customerName:"",productIdInventory:"prod-456",productNameInventory:"Samsung Galaxy S21 Battery",quantity:3,threshold:5}),[a,l]=(0,o.useState)(!1),[p,c]=(0,o.useState)(null),u=e=>{let{name:t,value:r}=e.target;n(e=>({...e,[t]:r}))},x=async t=>{t.preventDefault(),l(!0),c(null);try{let t,i;"support"===e?(t="/api/zapier/webhooks?event=support_request",i={name:r.name,email:r.email,subject:r.subject,message:r.message}):"review"===e?(t="/api/zapier/webhooks?event=product_review",i={productId:r.productId,productName:r.productName,rating:parseInt(r.rating),reviewText:r.reviewText,customerName:r.customerName}):"inventory"===e&&(t="/api/zapier/webhooks?event=low_inventory",i={id:r.productIdInventory,name:r.productNameInventory,quantity:parseInt(r.quantity),threshold:parseInt(r.threshold)});let o=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)}),n=await o.json();c({success:n.success,message:n.message||(n.success?"Request sent successfully!":"Failed to send request")})}catch(e){console.error("Error sending request:",e),c({success:!1,message:e.message||"An error occurred"})}finally{l(!1)}};return(0,i.jsxs)("div",{children:[(0,i.jsxs)(d(),{children:[(0,i.jsx)("title",{children:"Zapier Automation | MDTS Tech"}),(0,i.jsx)("meta",{name:"description",content:"Test Zapier automation integrations"})]}),(0,i.jsx)(s.Z,{title:"Zapier Automation | MDTS Tech",description:"Test Zapier automation integrations",children:(0,i.jsxs)("div",{style:{maxWidth:"800px",margin:"0 auto"},children:[(0,i.jsx)("h1",{style:{textAlign:"center",marginBottom:"30px"},children:"Zapier Automation"}),(0,i.jsx)("div",{style:{display:"flex",justifyContent:"center",marginBottom:"30px"},children:(0,i.jsx)("div",{style:{display:"flex",gap:"10px"},children:[{id:"support",label:"Support Request"},{id:"review",label:"Product Review"},{id:"inventory",label:"Low Inventory Alert"}].map(r=>(0,i.jsx)("button",{onClick:()=>t(r.id),style:{padding:"8px 16px",backgroundColor:e===r.id?"#0066cc":"#f0f0f0",color:e===r.id?"white":"#333",border:"none",borderRadius:"4px",cursor:"pointer"},children:r.label},r.id))})}),p&&(0,i.jsxs)("div",{style:{padding:"15px",marginBottom:"20px",borderRadius:"4px",backgroundColor:p.success?"#d4edda":"#f8d7da",color:p.success?"#155724":"#721c24",border:"1px solid ".concat(p.success?"#c3e6cb":"#f5c6cb")},children:[(0,i.jsx)("p",{children:p.message}),!p.success&&(0,i.jsx)("p",{style:{marginTop:"10px",fontSize:"0.9rem"},children:"Note: This feature requires a Zapier webhook to be set up."})]}),(0,i.jsxs)("div",{style:{backgroundColor:"white",padding:"30px",borderRadius:"8px",boxShadow:"0 2px 10px rgba(0, 0, 0, 0.1)"},children:["support"===e&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("h2",{style:{marginBottom:"20px"},children:"Submit a Support Request"}),(0,i.jsxs)("form",{onSubmit:x,children:[(0,i.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,i.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Name"}),(0,i.jsx)("input",{type:"text",name:"name",value:r.name,onChange:u,required:!0,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"}})]}),(0,i.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,i.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Email"}),(0,i.jsx)("input",{type:"email",name:"email",value:r.email,onChange:u,required:!0,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"}})]}),(0,i.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,i.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Subject"}),(0,i.jsx)("input",{type:"text",name:"subject",value:r.subject,onChange:u,required:!0,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"}})]}),(0,i.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,i.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Message"}),(0,i.jsx)("textarea",{name:"message",value:r.message,onChange:u,required:!0,rows:5,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px",resize:"vertical"}})]}),(0,i.jsx)("button",{type:"submit",disabled:a,style:{backgroundColor:"#0066cc",color:"white",border:"none",padding:"12px 20px",borderRadius:"4px",cursor:a?"not-allowed":"pointer",opacity:a?.7:1},children:a?"Sending...":"Submit Support Request"})]})]}),"review"===e&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("h2",{style:{marginBottom:"20px"},children:"Submit a Product Review"}),(0,i.jsxs)("form",{onSubmit:x,children:[(0,i.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,i.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Product"}),(0,i.jsxs)("select",{name:"productId",value:r.productId,onChange:e=>{let t="prod-123"===e.target.value?"iPhone 13 Pro Screen":"Samsung Galaxy S21 Battery";n(r=>({...r,productId:e.target.value,productName:t}))},style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"},children:[(0,i.jsx)("option",{value:"prod-123",children:"iPhone 13 Pro Screen"}),(0,i.jsx)("option",{value:"prod-456",children:"Samsung Galaxy S21 Battery"})]})]}),(0,i.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,i.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Your Name"}),(0,i.jsx)("input",{type:"text",name:"customerName",value:r.customerName,onChange:u,required:!0,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"}})]}),(0,i.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,i.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Rating"}),(0,i.jsx)("select",{name:"rating",value:r.rating,onChange:u,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"},children:[5,4,3,2,1].map(e=>(0,i.jsxs)("option",{value:e,children:[e," Star",1!==e?"s":""]},e))})]}),(0,i.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,i.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Review"}),(0,i.jsx)("textarea",{name:"reviewText",value:r.reviewText,onChange:u,required:!0,rows:5,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px",resize:"vertical"}})]}),(0,i.jsx)("button",{type:"submit",disabled:a,style:{backgroundColor:"#0066cc",color:"white",border:"none",padding:"12px 20px",borderRadius:"4px",cursor:a?"not-allowed":"pointer",opacity:a?.7:1},children:a?"Submitting...":"Submit Review"})]})]}),"inventory"===e&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("h2",{style:{marginBottom:"20px"},children:"Simulate Low Inventory Alert"}),(0,i.jsxs)("form",{onSubmit:x,children:[(0,i.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,i.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Product"}),(0,i.jsxs)("select",{name:"productIdInventory",value:r.productIdInventory,onChange:e=>{let t="prod-123"===e.target.value?"iPhone 13 Pro Screen":"Samsung Galaxy S21 Battery";n(r=>({...r,productIdInventory:e.target.value,productNameInventory:t}))},style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"},children:[(0,i.jsx)("option",{value:"prod-456",children:"Samsung Galaxy S21 Battery"}),(0,i.jsx)("option",{value:"prod-123",children:"iPhone 13 Pro Screen"})]})]}),(0,i.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,i.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Current Quantity"}),(0,i.jsx)("input",{type:"number",name:"quantity",value:r.quantity,onChange:u,required:!0,min:"0",style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"}})]}),(0,i.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,i.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Threshold"}),(0,i.jsx)("input",{type:"number",name:"threshold",value:r.threshold,onChange:u,required:!0,min:"1",style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"4px"}}),(0,i.jsx)("p",{style:{marginTop:"5px",fontSize:"0.9rem",color:"#666"},children:"Alert will be triggered if quantity is below threshold"})]}),(0,i.jsx)("button",{type:"submit",disabled:a,style:{backgroundColor:"#0066cc",color:"white",border:"none",padding:"12px 20px",borderRadius:"4px",cursor:a?"not-allowed":"pointer",opacity:a?.7:1},children:a?"Sending...":"Send Inventory Alert"})]})]})]})]})})]})}},11163:function(e,t,r){e.exports=r(43079)}},function(e){e.O(0,[1664,5675,6029,2888,9774,179],function(){return e(e.s=22285)}),_N_E=e.O()}]);