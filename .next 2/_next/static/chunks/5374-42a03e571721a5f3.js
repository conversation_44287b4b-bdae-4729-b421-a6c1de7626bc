(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5374],{25730:function(e,t,r){"use strict";var o=r(58612),n=r(1768),i=r(68928),a=r(59770);e.exports=a||o.call(i,n)},1768:function(e){"use strict";e.exports=Function.prototype.apply},68928:function(e){"use strict";e.exports=Function.prototype.call},40319:function(e,t,r){"use strict";var o=r(58612),n=r(14453),i=r(68928),a=r(25730);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new n("a function is required");return a(o,i,e)}},59770:function(e){"use strict";e.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},17379:function(e,t,r){"use strict";var o=r(40210),n=r(40319),i=n([o("%String.prototype.indexOf%")]);e.exports=function(e,t){var r=o(e,!!t);return"function"==typeof r&&i(e,".prototype.")>-1?n([r]):r}},96504:function(e,t,r){"use strict";var o,n=r(40319),i=r(27296);try{o=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var a=!!o&&i&&i(Object.prototype,"__proto__"),s=Object,l=s.getPrototypeOf;e.exports=a&&"function"==typeof a.get?n([a.get]):"function"==typeof l&&function(e){return l(null==e?e:s(e))}},24429:function(e){"use strict";var t=Object.defineProperty||!1;if(t)try{t({},"a",{value:1})}catch(e){t=!1}e.exports=t},53981:function(e){"use strict";e.exports=EvalError},81648:function(e){"use strict";e.exports=Error},24726:function(e){"use strict";e.exports=RangeError},26712:function(e){"use strict";e.exports=ReferenceError},33464:function(e){"use strict";e.exports=SyntaxError},14453:function(e){"use strict";e.exports=TypeError},43915:function(e){"use strict";e.exports=URIError},68892:function(e){"use strict";e.exports=Object},17648:function(e){"use strict";var t=Object.prototype.toString,r=Math.max,o=function(e,t){for(var r=[],o=0;o<e.length;o+=1)r[o]=e[o];for(var n=0;n<t.length;n+=1)r[n+e.length]=t[n];return r},n=function(e,t){for(var r=[],o=t||0,n=0;o<e.length;o+=1,n+=1)r[n]=e[o];return r},i=function(e,t){for(var r="",o=0;o<e.length;o+=1)r+=e[o],o+1<e.length&&(r+=t);return r};e.exports=function(e){var a,s=this;if("function"!=typeof s||"[object Function]"!==t.apply(s))throw TypeError("Function.prototype.bind called on incompatible "+s);for(var l=n(arguments,1),u=r(0,s.length-l.length),c=[],d=0;d<u;d++)c[d]="$"+d;if(a=Function("binder","return function ("+i(c,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof a){var t=s.apply(this,o(l,arguments));return Object(t)===t?t:this}return s.apply(e,o(l,arguments))}),s.prototype){var h=function(){};h.prototype=s.prototype,a.prototype=new h,h.prototype=null}return a}},58612:function(e,t,r){"use strict";var o=r(17648);e.exports=Function.prototype.bind||o},40210:function(e,t,r){"use strict";var o,n=r(68892),i=r(81648),a=r(53981),s=r(24726),l=r(26712),u=r(33464),c=r(14453),d=r(43915),h=r(59738),p=r(76329),f=r(52264),m=r(55730),y=r(20707),v=r(63862),P=r(29550),g=Function,T=function(e){try{return g('"use strict"; return ('+e+").constructor;")()}catch(e){}},_=r(27296),E=r(24429),S=function(){throw new c},b=_?function(){try{return arguments.callee,S}catch(e){try{return _(arguments,"callee").get}catch(e){return S}}}():S,O=r(41405)(),x=r(81618),w=r(68899),A=r(10443),G=r(1768),R=r(68928),C={},j="undefined"!=typeof Uint8Array&&x?x(Uint8Array):o,I={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":O&&x?x([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":C,"%AsyncGenerator%":C,"%AsyncGeneratorFunction%":C,"%AsyncIteratorPrototype%":C,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?o:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?o:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":i,"%eval%":eval,"%EvalError%":a,"%Float16Array%":"undefined"==typeof Float16Array?o:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":g,"%GeneratorFunction%":C,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":O&&x?x(x([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&O&&x?x(new Map()[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":n,"%Object.getOwnPropertyDescriptor%":_,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":s,"%ReferenceError%":l,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&O&&x?x(new Set()[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":O&&x?x(""[Symbol.iterator]()):o,"%Symbol%":O?Symbol:o,"%SyntaxError%":u,"%ThrowTypeError%":b,"%TypedArray%":j,"%TypeError%":c,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":d,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet,"%Function.prototype.call%":R,"%Function.prototype.apply%":G,"%Object.defineProperty%":E,"%Object.getPrototypeOf%":w,"%Math.abs%":h,"%Math.floor%":p,"%Math.max%":f,"%Math.min%":m,"%Math.pow%":y,"%Math.round%":v,"%Math.sign%":P,"%Reflect.getPrototypeOf%":A};if(x)try{null.error}catch(e){var k=x(x(e));I["%Error.prototype%"]=k}var D=function e(t){var r;if("%AsyncFunction%"===t)r=T("async function () {}");else if("%GeneratorFunction%"===t)r=T("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=T("async function* () {}");else if("%AsyncGenerator%"===t){var o=e("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&x&&(r=x(n.prototype))}return I[t]=r,r},N={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},M=r(58612),F=r(48824),q=M.call(R,Array.prototype.concat),L=M.call(G,Array.prototype.splice),U=M.call(R,String.prototype.replace),H=M.call(R,String.prototype.slice),$=M.call(R,RegExp.prototype.exec),z=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,B=/\\(\\)?/g,W=function(e){var t=H(e,0,1),r=H(e,-1);if("%"===t&&"%"!==r)throw new u("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new u("invalid intrinsic syntax, expected opening `%`");var o=[];return U(e,z,function(e,t,r,n){o[o.length]=r?U(n,B,"$1"):t||e}),o},K=function(e,t){var r,o=e;if(F(N,o)&&(o="%"+(r=N[o])[0]+"%"),F(I,o)){var n=I[o];if(n===C&&(n=D(o)),void 0===n&&!t)throw new c("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new u("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new c("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new c('"allowMissing" argument must be a boolean');if(null===$(/^%?[^%]*%?$/,e))throw new u("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=W(e),o=r.length>0?r[0]:"",n=K("%"+o+"%",t),i=n.name,a=n.value,s=!1,l=n.alias;l&&(o=l[0],L(r,q([0,1],l)));for(var d=1,h=!0;d<r.length;d+=1){var p=r[d],f=H(p,0,1),m=H(p,-1);if(('"'===f||"'"===f||"`"===f||'"'===m||"'"===m||"`"===m)&&f!==m)throw new u("property names with quotes must have matching quotes");if("constructor"!==p&&h||(s=!0),o+="."+p,F(I,i="%"+o+"%"))a=I[i];else if(null!=a){if(!(p in a)){if(!t)throw new c("base intrinsic for "+e+" exists, but the property is not available.");return}if(_&&d+1>=r.length){var y=_(a,p);a=(h=!!y)&&"get"in y&&!("originalValue"in y.get)?y.get:a[p]}else h=F(a,p),a=a[p];h&&!s&&(I[i]=a)}}return a}},68899:function(e,t,r){"use strict";var o=r(68892);e.exports=o.getPrototypeOf||null},10443:function(e){"use strict";e.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},81618:function(e,t,r){"use strict";var o=r(10443),n=r(68899),i=r(96504);e.exports=o?function(e){return o(e)}:n?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("getProto: not an object");return n(e)}:i?function(e){return i(e)}:null},40690:function(e){"use strict";e.exports=Object.getOwnPropertyDescriptor},27296:function(e,t,r){"use strict";var o=r(40690);if(o)try{o([],"length")}catch(e){o=null}e.exports=o},41405:function(e,t,r){"use strict";var o="undefined"!=typeof Symbol&&Symbol,n=r(55419);e.exports=function(){return"function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n()}},55419:function(e){"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t||"[object Symbol]"!==Object.prototype.toString.call(t)||"[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(var o in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t||!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0}},48824:function(e,t,r){"use strict";var o=Function.prototype.call,n=Object.prototype.hasOwnProperty,i=r(58612);e.exports=i.call(o,n)},59738:function(e){"use strict";e.exports=Math.abs},76329:function(e){"use strict";e.exports=Math.floor},43678:function(e){"use strict";e.exports=Number.isNaN||function(e){return e!=e}},52264:function(e){"use strict";e.exports=Math.max},55730:function(e){"use strict";e.exports=Math.min},20707:function(e){"use strict";e.exports=Math.pow},63862:function(e){"use strict";e.exports=Math.round},29550:function(e,t,r){"use strict";var o=r(43678);e.exports=function(e){return o(e)||0===e?e:e<0?-1:1}},11163:function(e,t,r){e.exports=r(43079)},70631:function(e,t,r){var o="function"==typeof Map&&Map.prototype,n=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,i=o&&n&&"function"==typeof n.get?n.get:null,a=o&&Map.prototype.forEach,s="function"==typeof Set&&Set.prototype,l=Object.getOwnPropertyDescriptor&&s?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,u=s&&l&&"function"==typeof l.get?l.get:null,c=s&&Set.prototype.forEach,d="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,h="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,p="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,f=Boolean.prototype.valueOf,m=Object.prototype.toString,y=Function.prototype.toString,v=String.prototype.match,P=String.prototype.slice,g=String.prototype.replace,T=String.prototype.toUpperCase,_=String.prototype.toLowerCase,E=RegExp.prototype.test,S=Array.prototype.concat,b=Array.prototype.join,O=Array.prototype.slice,x=Math.floor,w="function"==typeof BigInt?BigInt.prototype.valueOf:null,A=Object.getOwnPropertySymbols,G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,R="function"==typeof Symbol&&"object"==typeof Symbol.iterator,C="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===R?"object":"symbol")?Symbol.toStringTag:null,j=Object.prototype.propertyIsEnumerable,I=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function k(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||E.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var o=e<0?-x(-e):x(e);if(o!==e){var n=String(o),i=P.call(t,n.length+1);return g.call(n,r,"$&_")+"."+g.call(g.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return g.call(t,r,"$&_")}var D=r(24654),N=D.custom,M=z(N)?N:null,F={__proto__:null,double:'"',single:"'"},q={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};function L(e,t,r){var o=F[r.quoteStyle||t];return o+e+o}function U(e){return!C||!("object"==typeof e&&(C in e||void 0!==e[C]))}function H(e){return"[object Array]"===K(e)&&U(e)}function $(e){return"[object RegExp]"===K(e)&&U(e)}function z(e){if(R)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!G)return!1;try{return G.call(e),!0}catch(e){}return!1}e.exports=function e(t,o,n,s){var l=o||{};if(W(l,"quoteStyle")&&!W(F,l.quoteStyle))throw TypeError('option "quoteStyle" must be "single" or "double"');if(W(l,"maxStringLength")&&("number"==typeof l.maxStringLength?l.maxStringLength<0&&l.maxStringLength!==1/0:null!==l.maxStringLength))throw TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var m=!W(l,"customInspect")||l.customInspect;if("boolean"!=typeof m&&"symbol"!==m)throw TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(W(l,"indent")&&null!==l.indent&&"	"!==l.indent&&!(parseInt(l.indent,10)===l.indent&&l.indent>0))throw TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(W(l,"numericSeparator")&&"boolean"!=typeof l.numericSeparator)throw TypeError('option "numericSeparator", if provided, must be `true` or `false`');var T=l.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return function e(t,r){if(t.length>r.maxStringLength){var o=t.length-r.maxStringLength;return e(P.call(t,0,r.maxStringLength),r)+"... "+o+" more character"+(o>1?"s":"")}var n=q[r.quoteStyle||"single"];return n.lastIndex=0,L(g.call(g.call(t,n,"\\$1"),/[\x00-\x1f]/g,J),"single",r)}(t,l);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var E=String(t);return T?k(t,E):E}if("bigint"==typeof t){var x=String(t)+"n";return T?k(t,x):x}var A=void 0===l.depth?5:l.depth;if(void 0===n&&(n=0),n>=A&&A>0&&"object"==typeof t)return H(t)?"[Array]":"[Object]";var N=function(e,t){var r;if("	"===e.indent)r="	";else{if("number"!=typeof e.indent||!(e.indent>0))return null;r=b.call(Array(e.indent+1)," ")}return{base:r,prev:b.call(Array(t+1),r)}}(l,n);if(void 0===s)s=[];else if(V(s,t)>=0)return"[Circular]";function B(t,r,o){if(r&&(s=O.call(s)).push(r),o){var i={depth:l.depth};return W(l,"quoteStyle")&&(i.quoteStyle=l.quoteStyle),e(t,i,n+1,s)}return e(t,l,n+1,s)}if("function"==typeof t&&!$(t)){var et=function(e){if(e.name)return e.name;var t=v.call(y.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),er=ee(t,B);return"[Function"+(et?": "+et:" (anonymous)")+"]"+(er.length>0?" { "+b.call(er,", ")+" }":"")}if(z(t)){var eo=R?g.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):G.call(t);return"object"!=typeof t||R?eo:Q(eo)}if(t&&"object"==typeof t&&("undefined"!=typeof HTMLElement&&t instanceof HTMLElement||"string"==typeof t.nodeName&&"function"==typeof t.getAttribute)){for(var en,ei="<"+_.call(String(t.nodeName)),ea=t.attributes||[],es=0;es<ea.length;es++)ei+=" "+ea[es].name+"="+L((en=ea[es].value,g.call(String(en),/"/g,"&quot;")),"double",l);return ei+=">",t.childNodes&&t.childNodes.length&&(ei+="..."),ei+="</"+_.call(String(t.nodeName))+">"}if(H(t)){if(0===t.length)return"[]";var el=ee(t,B);return N&&!function(e){for(var t=0;t<e.length;t++)if(V(e[t],"\n")>=0)return!1;return!0}(el)?"["+Z(el,N)+"]":"[ "+b.call(el,", ")+" ]"}if("[object Error]"===K(t)&&U(t)){var eu=ee(t,B);return"cause"in Error.prototype||!("cause"in t)||j.call(t,"cause")?0===eu.length?"["+String(t)+"]":"{ ["+String(t)+"] "+b.call(eu,", ")+" }":"{ ["+String(t)+"] "+b.call(S.call("[cause]: "+B(t.cause),eu),", ")+" }"}if("object"==typeof t&&m){if(M&&"function"==typeof t[M]&&D)return D(t,{depth:A-n});if("symbol"!==m&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!i||!e||"object"!=typeof e)return!1;try{i.call(e);try{u.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var ec=[];return a&&a.call(t,function(e,r){ec.push(B(r,t,!0)+" => "+B(e,t))}),Y("Map",i.call(t),ec,N)}if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e);try{i.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var ed=[];return c&&c.call(t,function(e){ed.push(B(e,t))}),Y("Set",u.call(t),ed,N)}if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{h.call(e,h)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return X("WeakMap");if(function(e){if(!h||!e||"object"!=typeof e)return!1;try{h.call(e,h);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return X("WeakSet");if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{return p.call(e),!0}catch(e){}return!1}(t))return X("WeakRef");if("[object Number]"===K(t)&&U(t))return Q(B(Number(t)));if(function(e){if(!e||"object"!=typeof e||!w)return!1;try{return w.call(e),!0}catch(e){}return!1}(t))return Q(B(w.call(t)));if("[object Boolean]"===K(t)&&U(t))return Q(f.call(t));if("[object String]"===K(t)&&U(t))return Q(B(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||void 0!==r.g&&t===r.g)return"{ [object globalThis] }";if(!("[object Date]"===K(t)&&U(t))&&!$(t)){var eh=ee(t,B),ep=I?I(t)===Object.prototype:t instanceof Object||t.constructor===Object,ef=t instanceof Object?"":"null prototype",em=!ep&&C&&Object(t)===t&&C in t?P.call(K(t),8,-1):ef?"Object":"",ey=(ep||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(em||ef?"["+b.call(S.call([],em||[],ef||[]),": ")+"] ":"");return 0===eh.length?ey+"{}":N?ey+"{"+Z(eh,N)+"}":ey+"{ "+b.call(eh,", ")+" }"}return String(t)};var B=Object.prototype.hasOwnProperty||function(e){return e in this};function W(e,t){return B.call(e,t)}function K(e){return m.call(e)}function V(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,o=e.length;r<o;r++)if(e[r]===t)return r;return -1}function J(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+T.call(t.toString(16))}function Q(e){return"Object("+e+")"}function X(e){return e+" { ? }"}function Y(e,t,r,o){return e+" ("+t+") {"+(o?Z(r,o):b.call(r,", "))+"}"}function Z(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+b.call(e,","+r)+"\n"+t.prev}function ee(e,t){var r,o=H(e),n=[];if(o){n.length=e.length;for(var i=0;i<e.length;i++)n[i]=W(e,i)?t(e[i],e):""}var a="function"==typeof A?A(e):[];if(R){r={};for(var s=0;s<a.length;s++)r["$"+a[s]]=a[s]}for(var l in e)W(e,l)&&(!o||String(Number(l))!==l||!(l<e.length))&&(R&&r["$"+l]instanceof Symbol||(E.call(/[^\w$]/,l)?n.push(t(l,e)+": "+t(e[l],e)):n.push(l+": "+t(e[l],e))));if("function"==typeof A)for(var u=0;u<a.length;u++)j.call(e,a[u])&&n.push("["+t(a[u])+"]: "+t(e[a[u]],e));return n}},55798:function(e){"use strict";var t=String.prototype.replace,r=/%20/g,o="RFC3986";e.exports={default:o,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:o}},80129:function(e,t,r){"use strict";var o=r(58261),n=r(55235),i=r(55798);e.exports={formats:i,parse:n,stringify:o}},55235:function(e,t,r){"use strict";var o=r(12769),n=Object.prototype.hasOwnProperty,i=Array.isArray,a={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:o.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},s=function(e,t,r){if(e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1)return e.split(",");if(t.throwOnLimitExceeded&&r>=t.arrayLimit)throw RangeError("Array limit exceeded. Only "+t.arrayLimit+" element"+(1===t.arrayLimit?"":"s")+" allowed in an array.");return e},l=function(e,t){var r={__proto__:null},l=t.ignoreQueryPrefix?e.replace(/^\?/,""):e;l=l.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var u=t.parameterLimit===1/0?void 0:t.parameterLimit,c=l.split(t.delimiter,t.throwOnLimitExceeded?u+1:u);if(t.throwOnLimitExceeded&&c.length>u)throw RangeError("Parameter limit exceeded. Only "+u+" parameter"+(1===u?"":"s")+" allowed.");var d=-1,h=t.charset;if(t.charsetSentinel)for(p=0;p<c.length;++p)0===c[p].indexOf("utf8=")&&("utf8=%E2%9C%93"===c[p]?h="utf-8":"utf8=%26%2310003%3B"===c[p]&&(h="iso-8859-1"),d=p,p=c.length);for(p=0;p<c.length;++p)if(p!==d){var p,f,m,y=c[p],v=y.indexOf("]="),P=-1===v?y.indexOf("="):v+1;-1===P?(f=t.decoder(y,a.decoder,h,"key"),m=t.strictNullHandling?null:""):(f=t.decoder(y.slice(0,P),a.decoder,h,"key"),m=o.maybeMap(s(y.slice(P+1),t,i(r[f])?r[f].length:0),function(e){return t.decoder(e,a.decoder,h,"value")})),m&&t.interpretNumericEntities&&"iso-8859-1"===h&&(m=String(m).replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),y.indexOf("[]=")>-1&&(m=i(m)?[m]:m);var g=n.call(r,f);g&&"combine"===t.duplicates?r[f]=o.combine(r[f],m):g&&"last"!==t.duplicates||(r[f]=m)}return r},u=function(e,t,r,n){var i=0;if(e.length>0&&"[]"===e[e.length-1]){var a=e.slice(0,-1).join("");i=Array.isArray(t)&&t[a]?t[a].length:0}for(var l=n?t:s(t,r,i),u=e.length-1;u>=0;--u){var c,d=e[u];if("[]"===d&&r.parseArrays)c=r.allowEmptyArrays&&(""===l||r.strictNullHandling&&null===l)?[]:o.combine([],l);else{c=r.plainObjects?{__proto__:null}:{};var h="["===d.charAt(0)&&"]"===d.charAt(d.length-1)?d.slice(1,-1):d,p=r.decodeDotInKeys?h.replace(/%2E/g,"."):h,f=parseInt(p,10);r.parseArrays||""!==p?!isNaN(f)&&d!==p&&String(f)===p&&f>=0&&r.parseArrays&&f<=r.arrayLimit?(c=[])[f]=l:"__proto__"!==p&&(c[p]=l):c={0:l}}l=c}return l},c=function(e,t,r,o){if(e){var i=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,a=/(\[[^[\]]*])/g,s=r.depth>0&&/(\[[^[\]]*])/.exec(i),l=s?i.slice(0,s.index):i,c=[];if(l){if(!r.plainObjects&&n.call(Object.prototype,l)&&!r.allowPrototypes)return;c.push(l)}for(var d=0;r.depth>0&&null!==(s=a.exec(i))&&d<r.depth;){if(d+=1,!r.plainObjects&&n.call(Object.prototype,s[1].slice(1,-1))&&!r.allowPrototypes)return;c.push(s[1])}if(s){if(!0===r.strictDepth)throw RangeError("Input depth exceeded depth option of "+r.depth+" and strictDepth is true");c.push("["+i.slice(s.index)+"]")}return u(c,t,r,o)}},d=function(e){if(!e)return a;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(void 0!==e.throwOnLimitExceeded&&"boolean"!=typeof e.throwOnLimitExceeded)throw TypeError("`throwOnLimitExceeded` option must be a boolean");var t=void 0===e.charset?a.charset:e.charset,r=void 0===e.duplicates?a.duplicates:e.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||a.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:a.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:a.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:a.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:a.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:a.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:a.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:a.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:a.decoder,delimiter:"string"==typeof e.delimiter||o.isRegExp(e.delimiter)?e.delimiter:a.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:a.depth,duplicates:r,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:a.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:a.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:a.plainObjects,strictDepth:"boolean"==typeof e.strictDepth?!!e.strictDepth:a.strictDepth,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:a.strictNullHandling,throwOnLimitExceeded:"boolean"==typeof e.throwOnLimitExceeded&&e.throwOnLimitExceeded}};e.exports=function(e,t){var r=d(t);if(""===e||null==e)return r.plainObjects?{__proto__:null}:{};for(var n="string"==typeof e?l(e,r):e,i=r.plainObjects?{__proto__:null}:{},a=Object.keys(n),s=0;s<a.length;++s){var u=a[s],h=c(u,n[u],r,"string"==typeof e);i=o.merge(i,h,r)}return!0===r.allowSparse?i:o.compact(i)}},58261:function(e,t,r){"use strict";var o=r(37478),n=r(12769),i=r(55798),a=Object.prototype.hasOwnProperty,s={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},l=Array.isArray,u=Array.prototype.push,c=function(e,t){u.apply(e,l(t)?t:[t])},d=Date.prototype.toISOString,h=i.default,p={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:n.encode,encodeValuesOnly:!1,filter:void 0,format:h,formatter:i.formatters[h],indices:!1,serializeDate:function(e){return d.call(e)},skipNulls:!1,strictNullHandling:!1},f={},m=function e(t,r,i,a,s,u,d,h,m,y,v,P,g,T,_,E,S,b){for(var O,x,w=t,A=b,G=0,R=!1;void 0!==(A=A.get(f))&&!R;){var C=A.get(t);if(G+=1,void 0!==C){if(C===G)throw RangeError("Cyclic object value");R=!0}void 0===A.get(f)&&(G=0)}if("function"==typeof y?w=y(r,w):w instanceof Date?w=g(w):"comma"===i&&l(w)&&(w=n.maybeMap(w,function(e){return e instanceof Date?g(e):e})),null===w){if(u)return m&&!E?m(r,p.encoder,S,"key",T):r;w=""}if("string"==typeof(O=w)||"number"==typeof O||"boolean"==typeof O||"symbol"==typeof O||"bigint"==typeof O||n.isBuffer(w))return m?[_(E?r:m(r,p.encoder,S,"key",T))+"="+_(m(w,p.encoder,S,"value",T))]:[_(r)+"="+_(String(w))];var j=[];if(void 0===w)return j;if("comma"===i&&l(w))E&&m&&(w=n.maybeMap(w,m)),x=[{value:w.length>0?w.join(",")||null:void 0}];else if(l(y))x=y;else{var I=Object.keys(w);x=v?I.sort(v):I}var k=h?String(r).replace(/\./g,"%2E"):String(r),D=a&&l(w)&&1===w.length?k+"[]":k;if(s&&l(w)&&0===w.length)return D+"[]";for(var N=0;N<x.length;++N){var M=x[N],F="object"==typeof M&&M&&void 0!==M.value?M.value:w[M];if(!d||null!==F){var q=P&&h?String(M).replace(/\./g,"%2E"):String(M),L=l(w)?"function"==typeof i?i(D,q):D:D+(P?"."+q:"["+q+"]");b.set(t,G);var U=o();U.set(f,b),c(j,e(F,L,i,a,s,u,d,h,"comma"===i&&E&&l(w)?null:m,y,v,P,g,T,_,E,S,U))}}return j},y=function(e){if(!e)return p;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");var t,r=e.charset||p.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var o=i.default;if(void 0!==e.format){if(!a.call(i.formatters,e.format))throw TypeError("Unknown format option provided.");o=e.format}var n=i.formatters[o],u=p.filter;if(("function"==typeof e.filter||l(e.filter))&&(u=e.filter),t=e.arrayFormat in s?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":p.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");var c=void 0===e.allowDots?!0===e.encodeDotInKeys||p.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:p.addQueryPrefix,allowDots:c,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:p.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:p.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?p.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:p.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:p.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:p.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:p.encodeValuesOnly,filter:u,format:o,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:p.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:p.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:p.strictNullHandling}};e.exports=function(e,t){var r,n=e,i=y(t);"function"==typeof i.filter?n=(0,i.filter)("",n):l(i.filter)&&(r=i.filter);var a=[];if("object"!=typeof n||null===n)return"";var u=s[i.arrayFormat],d="comma"===u&&i.commaRoundTrip;r||(r=Object.keys(n)),i.sort&&r.sort(i.sort);for(var h=o(),p=0;p<r.length;++p){var f=r[p],v=n[f];i.skipNulls&&null===v||c(a,m(v,f,u,d,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,h))}var P=a.join(i.delimiter),g=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?g+="utf8=%26%2310003%3B&":g+="utf8=%E2%9C%93&"),P.length>0?g+P:""}},12769:function(e,t,r){"use strict";var o=r(55798),n=Object.prototype.hasOwnProperty,i=Array.isArray,a=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),s=function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(i(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);t.obj[t.prop]=o}}},l=function(e,t){for(var r=t&&t.plainObjects?{__proto__:null}:{},o=0;o<e.length;++o)void 0!==e[o]&&(r[o]=e[o]);return r};e.exports={arrayToObject:l,assign:function(e,t){return Object.keys(t).reduce(function(e,r){return e[r]=t[r],e},e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],o=0;o<t.length;++o)for(var n=t[o],i=n.obj[n.prop],a=Object.keys(i),l=0;l<a.length;++l){var u=a[l],c=i[u];"object"==typeof c&&null!==c&&-1===r.indexOf(c)&&(t.push({obj:i,prop:u}),r.push(c))}return s(t),e},decode:function(e,t,r){var o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(e){return o}},encode:function(e,t,r,n,i){if(0===e.length)return e;var s=e;if("symbol"==typeof e?s=Symbol.prototype.toString.call(e):"string"!=typeof e&&(s=String(e)),"iso-8859-1"===r)return escape(s).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});for(var l="",u=0;u<s.length;u+=1024){for(var c=s.length>=1024?s.slice(u,u+1024):s,d=[],h=0;h<c.length;++h){var p=c.charCodeAt(h);if(45===p||46===p||95===p||126===p||p>=48&&p<=57||p>=65&&p<=90||p>=97&&p<=122||i===o.RFC1738&&(40===p||41===p)){d[d.length]=c.charAt(h);continue}if(p<128){d[d.length]=a[p];continue}if(p<2048){d[d.length]=a[192|p>>6]+a[128|63&p];continue}if(p<55296||p>=57344){d[d.length]=a[224|p>>12]+a[128|p>>6&63]+a[128|63&p];continue}h+=1,p=65536+((1023&p)<<10|1023&c.charCodeAt(h)),d[d.length]=a[240|p>>18]+a[128|p>>12&63]+a[128|p>>6&63]+a[128|63&p]}l+=d.join("")}return l},isBuffer:function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(i(e)){for(var r=[],o=0;o<e.length;o+=1)r.push(t(e[o]));return r}return t(e)},merge:function e(t,r,o){if(!r)return t;if("object"!=typeof r&&"function"!=typeof r){if(i(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!n.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var a=t;return(i(t)&&!i(r)&&(a=l(t,o)),i(t)&&i(r))?(r.forEach(function(r,i){if(n.call(t,i)){var a=t[i];a&&"object"==typeof a&&r&&"object"==typeof r?t[i]=e(a,r,o):t.push(r)}else t[i]=r}),t):Object.keys(r).reduce(function(t,i){var a=r[i];return n.call(t,i)?t[i]=e(t[i],a,o):t[i]=a,t},a)}}},35747:function(e,t,r){"use strict";var o=r(70631),n=r(14453),i=function(e,t,r){for(var o,n=e;null!=(o=n.next);n=o)if(o.key===t)return n.next=o.next,r||(o.next=e.next,e.next=o),o},a=function(e,t){if(e){var r=i(e,t);return r&&r.value}},s=function(e,t,r){var o=i(e,t);o?o.value=r:e.next={key:t,next:e.next,value:r}},l=function(e,t){if(e)return i(e,t,!0)};e.exports=function(){var e,t={assert:function(e){if(!t.has(e))throw new n("Side channel does not contain "+o(e))},delete:function(t){var r=e&&e.next,o=l(e,t);return o&&r&&r===o&&(e=void 0),!!o},get:function(t){return a(e,t)},has:function(t){var r;return!!(r=e)&&!!i(r,t)},set:function(t,r){e||(e={next:void 0}),s(e,t,r)}};return t}},23595:function(e,t,r){"use strict";var o=r(40210),n=r(17379),i=r(70631),a=r(14453),s=o("%Map%",!0),l=n("Map.prototype.get",!0),u=n("Map.prototype.set",!0),c=n("Map.prototype.has",!0),d=n("Map.prototype.delete",!0),h=n("Map.prototype.size",!0);e.exports=!!s&&function(){var e,t={assert:function(e){if(!t.has(e))throw new a("Side channel does not contain "+i(e))},delete:function(t){if(e){var r=d(e,t);return 0===h(e)&&(e=void 0),r}return!1},get:function(t){if(e)return l(e,t)},has:function(t){return!!e&&c(e,t)},set:function(t,r){e||(e=new s),u(e,t,r)}};return t}},69034:function(e,t,r){"use strict";var o=r(40210),n=r(17379),i=r(70631),a=r(23595),s=r(14453),l=o("%WeakMap%",!0),u=n("WeakMap.prototype.get",!0),c=n("WeakMap.prototype.set",!0),d=n("WeakMap.prototype.has",!0),h=n("WeakMap.prototype.delete",!0);e.exports=l?function(){var e,t,r={assert:function(e){if(!r.has(e))throw new s("Side channel does not contain "+i(e))},delete:function(r){if(l&&r&&("object"==typeof r||"function"==typeof r)){if(e)return h(e,r)}else if(a&&t)return t.delete(r);return!1},get:function(r){return l&&r&&("object"==typeof r||"function"==typeof r)&&e?u(e,r):t&&t.get(r)},has:function(r){return l&&r&&("object"==typeof r||"function"==typeof r)&&e?d(e,r):!!t&&t.has(r)},set:function(r,o){l&&r&&("object"==typeof r||"function"==typeof r)?(e||(e=new l),c(e,r,o)):a&&(t||(t=a()),t.set(r,o))}};return r}:a},37478:function(e,t,r){"use strict";var o=r(14453),n=r(70631),i=r(35747),a=r(23595),s=r(69034)||a||i;e.exports=function(){var e,t={assert:function(e){if(!t.has(e))throw new o("Side channel does not contain "+n(e))},delete:function(t){return!!e&&e.delete(t)},get:function(t){return e&&e.get(t)},has:function(t){return!!e&&e.has(t)},set:function(t,r){e||(e=s()),e.set(t,r)}};return t}},2859:function(e,t,r){"use strict";r.d(t,{J:function(){return P}});var o,n="basil",i="https://js.stripe.com",a="".concat(i,"/").concat(n,"/stripe.js"),s=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,l=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,u=function(){for(var e=document.querySelectorAll('script[src^="'.concat(i,'"]')),t=0;t<e.length;t++){var r,o=e[t];if(r=o.src,s.test(r)||l.test(r))return o}return null},c=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",r=document.createElement("script");r.src="".concat(a).concat(t);var o=document.head||document.body;if(!o)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return o.appendChild(r),r},d=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"7.2.0",startTime:t})},h=null,p=null,f=null,m=function(e,t,r){if(null===e)return null;var o,i=t[0].match(/^pk_test/),a=3===(o=e.version)?"v3":o;i&&a!==n&&console.warn("Stripe.js@".concat(a," was loaded on the page, but @stripe/stripe-js@").concat("7.2.0"," expected Stripe.js@").concat(n,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var s=e.apply(void 0,t);return d(s,r),s},y=!1,v=function(){return o||(o=(null!==h?h:(h=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var r,o=u();o?o&&null!==f&&null!==p&&(o.removeEventListener("load",f),o.removeEventListener("error",p),null===(r=o.parentNode)||void 0===r||r.removeChild(o),o=c(null)):o=c(null),f=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},p=function(e){t(Error("Failed to load Stripe.js",{cause:e}))},o.addEventListener("load",f),o.addEventListener("error",p)}catch(e){t(e);return}})).catch(function(e){return h=null,Promise.reject(e)})).catch(function(e){return o=null,Promise.reject(e)}))};Promise.resolve().then(function(){return v()}).catch(function(e){y||console.warn(e)});var P=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];y=!0;var o=Date.now();return v().then(function(e){return m(e,t,o)})}},85472:function(e,t,r){"use strict";r.d(t,{Z:function(){return nA}});var o={};r.r(o),r.d(o,{StripeAPIError:function(){return E},StripeAuthenticationError:function(){return S},StripeCardError:function(){return T},StripeConnectionError:function(){return x},StripeError:function(){return g},StripeIdempotencyError:function(){return A},StripeInvalidGrantError:function(){return G},StripeInvalidRequestError:function(){return _},StripePermissionError:function(){return b},StripeRateLimitError:function(){return O},StripeSignatureVerificationError:function(){return w},StripeUnknownError:function(){return R},TemporarySessionExpiredError:function(){return C},generateV1Error:function(){return v},generateV2Error:function(){return P}});var n={};r.r(n),r.d(n,{Account:function(){return rO},AccountLinks:function(){return rw},AccountSessions:function(){return rG},Accounts:function(){return rO},ApplePayDomains:function(){return rC},ApplicationFees:function(){return rI},Apps:function(){return nn},Balance:function(){return rD},BalanceTransactions:function(){return rM},Billing:function(){return ni},BillingPortal:function(){return na},Charges:function(){return rq},Checkout:function(){return ns},Climate:function(){return nl},ConfirmationTokens:function(){return rU},CountrySpecs:function(){return r$},Coupons:function(){return rB},CreditNotes:function(){return rK},CustomerSessions:function(){return rJ},Customers:function(){return rX},Disputes:function(){return rZ},Entitlements:function(){return nu},EphemeralKeys:function(){return r0},Events:function(){return r8},ExchangeRates:function(){return r3},FileLinks:function(){return r5},Files:function(){return oe},FinancialConnections:function(){return nc},Forwarding:function(){return nd},Identity:function(){return nh},InvoiceItems:function(){return or},InvoicePayments:function(){return on},InvoiceRenderingTemplates:function(){return oa},Invoices:function(){return ol},Issuing:function(){return np},Mandates:function(){return oc},OAuth:function(){return op},PaymentIntents:function(){return om},PaymentLinks:function(){return ov},PaymentMethodConfigurations:function(){return og},PaymentMethodDomains:function(){return o_},PaymentMethods:function(){return oS},Payouts:function(){return oO},Plans:function(){return ow},Prices:function(){return oG},Products:function(){return oC},PromotionCodes:function(){return oI},Quotes:function(){return oD},Radar:function(){return nf},Refunds:function(){return oM},Reporting:function(){return nm},Reviews:function(){return oq},SetupAttempts:function(){return oU},SetupIntents:function(){return o$},ShippingRates:function(){return oB},Sigma:function(){return ny},Sources:function(){return oK},SubscriptionItems:function(){return oJ},SubscriptionSchedules:function(){return oX},Subscriptions:function(){return oZ},Tax:function(){return nv},TaxCodes:function(){return o0},TaxIds:function(){return o8},TaxRates:function(){return o3},Terminal:function(){return nP},TestHelpers:function(){return ng},Tokens:function(){return o5},Topups:function(){return o7},Transfers:function(){return nt},Treasury:function(){return nT},V2:function(){return n_},WebhookEndpoints:function(){return no}});class i{getClientName(){throw Error("getClientName not implemented.")}makeRequest(e,t,r,o,n,i,a,s){throw Error("makeRequest not implemented.")}static makeTimeoutError(){let e=TypeError(i.TIMEOUT_ERROR_CODE);return e.code=i.TIMEOUT_ERROR_CODE,e}}i.CONNECTION_CLOSED_ERROR_CODES=["ECONNRESET","EPIPE"],i.TIMEOUT_ERROR_CODE="ETIMEDOUT";class a{constructor(e,t){this._statusCode=e,this._headers=t}getStatusCode(){return this._statusCode}getHeaders(){return this._headers}getRawResponse(){throw Error("getRawResponse not implemented.")}toStream(e){throw Error("toStream not implemented.")}toJSON(){throw Error("toJSON not implemented.")}}class s extends i{constructor(e){if(super(),!e){if(!globalThis.fetch)throw Error("fetch() function not provided and is not defined in the global scope. You must provide a fetch implementation.");e=globalThis.fetch}globalThis.AbortController?this._fetchFn=s.makeFetchWithAbortTimeout(e):this._fetchFn=s.makeFetchWithRaceTimeout(e)}static makeFetchWithRaceTimeout(e){return(t,r,o)=>{let n;let a=new Promise((e,t)=>{n=setTimeout(()=>{n=null,t(i.makeTimeoutError())},o)});return Promise.race([e(t,r),a]).finally(()=>{n&&clearTimeout(n)})}}static makeFetchWithAbortTimeout(e){return async(t,r,o)=>{let n=new AbortController,a=setTimeout(()=>{a=null,n.abort(i.makeTimeoutError())},o);try{return await e(t,Object.assign(Object.assign({},r),{signal:n.signal}))}catch(e){if("AbortError"===e.name)throw i.makeTimeoutError();throw e}finally{a&&clearTimeout(a)}}}getClientName(){return"fetch"}async makeRequest(e,t,r,o,n,i,a,s){let u=new URL(r,`${"http"===a?"http":"https"}://${e}`);u.port=t;let c="POST"==o||"PUT"==o||"PATCH"==o;return new l(await this._fetchFn(u.toString(),{method:o,headers:n,body:i||(c?"":void 0)},s))}}class l extends a{constructor(e){super(e.status,l._transformHeadersToObject(e.headers)),this._res=e}getRawResponse(){return this._res}toStream(e){return e(),this._res.body}toJSON(){return this._res.json()}static _transformHeadersToObject(e){let t={};for(let r of e){if(!Array.isArray(r)||2!=r.length)throw Error("Response objects produced by the fetch function given to FetchHttpClient do not have an iterable headers map. Response#headers should be an iterable object.");t[r[0]]=r[1]}return t}}class u{computeHMACSignature(e,t){throw Error("computeHMACSignature not implemented.")}computeHMACSignatureAsync(e,t){throw Error("computeHMACSignatureAsync not implemented.")}computeSHA256Async(e){throw Error("computeSHA256 not implemented.")}}class c extends Error{}class d extends u{constructor(e){super(),this.subtleCrypto=e||crypto.subtle}computeHMACSignature(e,t){throw new c("SubtleCryptoProvider cannot be used in a synchronous context.")}async computeHMACSignatureAsync(e,t){let r=new TextEncoder,o=await this.subtleCrypto.importKey("raw",r.encode(t),{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),n=new Uint8Array(await this.subtleCrypto.sign("hmac",o,r.encode(e))),i=Array(n.length);for(let e=0;e<n.length;e++)i[e]=h[n[e]];return i.join("")}async computeSHA256Async(e){return new Uint8Array(await this.subtleCrypto.digest("SHA-256",e))}}let h=Array(256);for(let e=0;e<h.length;e++)h[e]=e.toString(16).padStart(2,"0");class p{constructor(){this._fetchFn=null,this._agent=null}getUname(){throw Error("getUname not implemented.")}uuid4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}secureCompare(e,t){if(e.length!==t.length)return!1;let r=e.length,o=0;for(let n=0;n<r;++n)o|=e.charCodeAt(n)^t.charCodeAt(n);return 0===o}createEmitter(){throw Error("createEmitter not implemented.")}tryBufferData(e){throw Error("tryBufferData not implemented.")}createNodeHttpClient(e){throw Error("createNodeHttpClient not implemented.")}createFetchHttpClient(e){return new s(e)}createDefaultHttpClient(){throw Error("createDefaultHttpClient not implemented.")}createNodeCryptoProvider(){throw Error("createNodeCryptoProvider not implemented.")}createSubtleCryptoProvider(e){return new d(e)}createDefaultCryptoProvider(){throw Error("createDefaultCryptoProvider not implemented.")}}class f extends Event{constructor(e,t){super(e),this.data=t}}class m{constructor(){this.eventTarget=new EventTarget,this.listenerMapping=new Map}on(e,t){let r=e=>{t(e.data)};return this.listenerMapping.set(t,r),this.eventTarget.addEventListener(e,r)}removeListener(e,t){let r=this.listenerMapping.get(t);return this.listenerMapping.delete(t),this.eventTarget.removeEventListener(e,r)}once(e,t){let r=e=>{t(e.data)};return this.listenerMapping.set(t,r),this.eventTarget.addEventListener(e,r,{once:!0})}emit(e,t){return this.eventTarget.dispatchEvent(new f(e,t))}}class y extends p{getUname(){return Promise.resolve(null)}createEmitter(){return new m}tryBufferData(e){if(e.file.data instanceof ReadableStream)throw Error("Uploading a file as a stream is not supported in non-Node environments. Please open or upvote an issue at github.com/stripe/stripe-node if you use this, detailing your use-case.");return Promise.resolve(e)}createNodeHttpClient(){throw Error("Stripe: `createNodeHttpClient()` is not available in non-Node environments. Please use `createFetchHttpClient()` instead.")}createDefaultHttpClient(){return super.createFetchHttpClient()}createNodeCryptoProvider(){throw Error("Stripe: `createNodeCryptoProvider()` is not available in non-Node environments. Please use `createSubtleCryptoProvider()` instead.")}createDefaultCryptoProvider(){return this.createSubtleCryptoProvider()}}let v=e=>{switch(e.type){case"card_error":return new T(e);case"invalid_request_error":return new _(e);case"api_error":return new E(e);case"authentication_error":return new S(e);case"rate_limit_error":return new O(e);case"idempotency_error":return new A(e);case"invalid_grant":return new G(e);default:return new R(e)}},P=e=>"temporary_session_expired"===e.type?new C(e):"invalid_fields"===e.code?new _(e):v(e);class g extends Error{constructor(e={},t=null){super(e.message),this.type=t||this.constructor.name,this.raw=e,this.rawType=e.type,this.code=e.code,this.doc_url=e.doc_url,this.param=e.param,this.detail=e.detail,this.headers=e.headers,this.requestId=e.requestId,this.statusCode=e.statusCode,this.message=e.message,this.userMessage=e.user_message,this.charge=e.charge,this.decline_code=e.decline_code,this.payment_intent=e.payment_intent,this.payment_method=e.payment_method,this.payment_method_type=e.payment_method_type,this.setup_intent=e.setup_intent,this.source=e.source}}g.generate=v;class T extends g{constructor(e={}){super(e,"StripeCardError")}}class _ extends g{constructor(e={}){super(e,"StripeInvalidRequestError")}}class E extends g{constructor(e={}){super(e,"StripeAPIError")}}class S extends g{constructor(e={}){super(e,"StripeAuthenticationError")}}class b extends g{constructor(e={}){super(e,"StripePermissionError")}}class O extends g{constructor(e={}){super(e,"StripeRateLimitError")}}class x extends g{constructor(e={}){super(e,"StripeConnectionError")}}class w extends g{constructor(e,t,r={}){super(r,"StripeSignatureVerificationError"),this.header=e,this.payload=t}}class A extends g{constructor(e={}){super(e,"StripeIdempotencyError")}}class G extends g{constructor(e={}){super(e,"StripeInvalidGrantError")}}class R extends g{constructor(e={}){super(e,"StripeUnknownError")}}class C extends g{constructor(e={}){super(e,"TemporarySessionExpiredError")}}var j=r(80129),I=r(34155);let k=["apiKey","idempotencyKey","stripeAccount","apiVersion","maxNetworkRetries","timeout","host","authenticator","stripeContext","additionalHeaders"];function D(e){return e&&"object"==typeof e&&k.some(t=>Object.prototype.hasOwnProperty.call(e,t))}function N(e,t){return j.stringify(e,{serializeDate:e=>Math.floor(e.getTime()/1e3).toString(),arrayFormat:"v2"==t?"repeat":"indices"}).replace(/%5B/g,"[").replace(/%5D/g,"]")}let M=(()=>{let e={"\n":"\\n",'"':'\\"',"\u2028":"\\u2028","\u2029":"\\u2029"};return t=>{let r=t.replace(/["\n\r\u2028\u2029]/g,t=>e[t]);return e=>r.replace(/\{([\s\S]+?)\}/g,(t,r)=>encodeURIComponent(e[r]||""))}})();function F(e){if(!Array.isArray(e)||!e[0]||"object"!=typeof e[0])return{};if(!D(e[0]))return e.shift();let t=Object.keys(e[0]),r=t.filter(e=>k.includes(e));return r.length>0&&r.length!==t.length&&H(`Options found in arguments (${r.join(", ")}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options.`),{}}function q(e){let t={host:null,headers:{},settings:{}};if(e.length>0){let r=e[e.length-1];if("string"==typeof r)t.authenticator=z(e.pop());else if(D(r)){let r=Object.assign({},e.pop()),o=Object.keys(r).filter(e=>!k.includes(e));if(o.length&&H(`Invalid options found (${o.join(", ")}); ignoring.`),r.apiKey&&(t.authenticator=z(r.apiKey)),r.idempotencyKey&&(t.headers["Idempotency-Key"]=r.idempotencyKey),r.stripeAccount&&(t.headers["Stripe-Account"]=r.stripeAccount),r.stripeContext){if(t.headers["Stripe-Account"])throw Error("Can't specify both stripeAccount and stripeContext.");t.headers["Stripe-Context"]=r.stripeContext}if(r.apiVersion&&(t.headers["Stripe-Version"]=r.apiVersion),Number.isInteger(r.maxNetworkRetries)&&(t.settings.maxNetworkRetries=r.maxNetworkRetries),Number.isInteger(r.timeout)&&(t.settings.timeout=r.timeout),r.host&&(t.host=r.host),r.authenticator){if(r.apiKey)throw Error("Can't specify both apiKey and authenticator.");if("function"!=typeof r.authenticator)throw Error("The authenticator must be a function receiving a request as the first parameter.");t.authenticator=r.authenticator}r.additionalHeaders&&(t.headers=r.additionalHeaders)}}return t}function L(e){if("object"!=typeof e)throw Error("Argument must be an object");return Object.keys(e).reduce((t,r)=>(null!=e[r]&&(t[r]=e[r]),t),{})}function U(e,t){return t?e.then(e=>{setTimeout(()=>{t(null,e)},0)},e=>{setTimeout(()=>{t(e,null)},0)}):e}function H(e){return"function"!=typeof I.emitWarning?console.warn(`Stripe: ${e}`):I.emitWarning(e,"Stripe")}function $(e,t,r){if(!Number.isInteger(t)){if(void 0!==r)return r;throw Error(`${e} must be an integer`)}return t}function z(e){let t=t=>(t.headers.Authorization="Bearer "+e,Promise.resolve());return t._apiKey=e,t}function B(e,t){return this[e]instanceof Date?Math.floor(this[e].getTime()/1e3).toString():t}function W(e){return e&&e.startsWith("/v2")?"v2":"v1"}class K{constructor(e,t){this._stripe=e,this._maxBufferedRequestMetric=t}_addHeadersDirectlyToObject(e,t){e.requestId=t["request-id"],e.stripeAccount=e.stripeAccount||t["stripe-account"],e.apiVersion=e.apiVersion||t["stripe-version"],e.idempotencyKey=e.idempotencyKey||t["idempotency-key"]}_makeResponseEvent(e,t,r){let o=Date.now(),n=o-e.request_start_time;return L({api_version:r["stripe-version"],account:r["stripe-account"],idempotency_key:r["idempotency-key"],method:e.method,path:e.path,status:t,request_id:this._getRequestId(r),elapsed:n,request_start_time:e.request_start_time,request_end_time:o})}_getRequestId(e){return e["request-id"]}_streamingResponseHandler(e,t,r){return o=>{let n=o.getHeaders(),i=o.toStream(()=>{let r=this._makeResponseEvent(e,o.getStatusCode(),n);this._stripe._emitter.emit("response",r),this._recordRequestMetrics(this._getRequestId(n),r.elapsed,t)});return this._addHeadersDirectlyToObject(i,n),r(null,i)}}_jsonResponseHandler(e,t,r,o){return n=>{let i=n.getHeaders(),a=this._getRequestId(i),s=n.getStatusCode(),l=this._makeResponseEvent(e,s,i);this._stripe._emitter.emit("response",l),n.toJSON().then(e=>{if(e.error)throw"string"==typeof e.error&&(e.error={type:e.error,message:e.error_description}),e.error.headers=i,e.error.statusCode=s,e.error.requestId=a,401===s?new S(e.error):403===s?new b(e.error):429===s?new O(e.error):"v2"===t?P(e.error):v(e.error);return e},e=>{throw new E({message:"Invalid JSON received from the Stripe API",exception:e,requestId:i["request-id"]})}).then(e=>{this._recordRequestMetrics(a,l.elapsed,r);let t=n.getRawResponse();this._addHeadersDirectlyToObject(t,i),Object.defineProperty(e,"lastResponse",{enumerable:!1,writable:!1,value:t}),o(null,e)},e=>o(e,null))}}static _generateConnectionErrorMessage(e){return`An error occurred with our connection to Stripe.${e>0?` Request was retried ${e} times.`:""}`}static _shouldRetry(e,t,r,o){return!!(o&&0===t&&i.CONNECTION_CLOSED_ERROR_CODES.includes(o.code))||!(t>=r)&&(!e||"false"!==e.getHeaders()["stripe-should-retry"]&&!!("true"===e.getHeaders()["stripe-should-retry"]||409===e.getStatusCode()||e.getStatusCode()>=500))}_getSleepTimeInMS(e,t=null){let r=this._stripe.getInitialNetworkRetryDelay(),o=Math.min(r*Math.pow(2,e-1),this._stripe.getMaxNetworkRetryDelay());return o*=.5*(1+Math.random()),o=Math.max(r,o),Number.isInteger(t)&&t<=60&&(o=Math.max(o,t)),1e3*o}_getMaxNetworkRetries(e={}){return void 0!==e.maxNetworkRetries&&Number.isInteger(e.maxNetworkRetries)?e.maxNetworkRetries:this._stripe.getMaxNetworkRetries()}_defaultIdempotencyKey(e,t,r){let o=this._getMaxNetworkRetries(t),n=()=>`stripe-node-retry-${this._stripe._platformFunctions.uuid4()}`;if("v2"===r){if("POST"===e||"DELETE"===e)return n()}else if("v1"===r&&"POST"===e&&o>0)return n();return null}_makeHeaders({contentType:e,contentLength:t,apiVersion:r,clientUserAgent:o,method:n,userSuppliedHeaders:i,userSuppliedSettings:a,stripeAccount:s,stripeContext:l,apiMode:u}){let c={Accept:"application/json","Content-Type":e,"User-Agent":this._getUserAgentString(u),"X-Stripe-Client-User-Agent":o,"X-Stripe-Client-Telemetry":this._getTelemetryHeader(),"Stripe-Version":r,"Stripe-Account":s,"Stripe-Context":l,"Idempotency-Key":this._defaultIdempotencyKey(n,a,u)},d="POST"==n||"PUT"==n||"PATCH"==n;return(d||t)&&(d||H(`${n} method had non-zero contentLength but no payload is expected for this verb`),c["Content-Length"]=t),Object.assign(L(c),i&&"object"==typeof i?Object.keys(i).reduce((e,t)=>(e[t.split("-").map(e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()).join("-")]=i[t],e),{}):i)}_getUserAgentString(e){let t=this._stripe.getConstant("PACKAGE_VERSION"),r=this._stripe._appInfo?this._stripe.getAppInfoAsString():"";return`Stripe/${e} NodeBindings/${t} ${r}`.trim()}_getTelemetryHeader(){if(this._stripe.getTelemetryEnabled()&&this._stripe._prevRequestMetrics.length>0)return JSON.stringify({last_request_metrics:this._stripe._prevRequestMetrics.shift()})}_recordRequestMetrics(e,t,r){if(this._stripe.getTelemetryEnabled()&&e){if(this._stripe._prevRequestMetrics.length>this._maxBufferedRequestMetric)H("Request metrics buffer is full, dropping telemetry message.");else{let o={request_id:e,request_duration_ms:t};r&&r.length>0&&(o.usage=r),this._stripe._prevRequestMetrics.push(o)}}}_rawRequest(e,t,r,o){return new Promise((n,i)=>{let a;try{let n=e.toUpperCase();if("POST"!==n&&r&&0!==Object.keys(r).length)throw Error("rawRequest only supports params on POST requests. Please pass null and add your parameters to path.");let i=[].slice.call([r,o]),s=F(i),l=Object.assign({},s),u=q(i),c=u.headers,d=u.authenticator;a={requestMethod:n,requestPath:t,bodyData:l,queryData:{},authenticator:d,headers:c,host:null,streaming:!1,settings:{},usage:["raw_request"]}}catch(e){i(e);return}let{headers:s,settings:l}=a,u=a.authenticator;this._request(a.requestMethod,a.host,t,a.bodyData,u,{headers:s,settings:l,streaming:a.streaming},a.usage,function(e,t){e?i(e):n(t)})})}_request(e,t,r,o,n,a,s=[],l,u=null){var c;let d;n=null!==(c=null!=n?n:this._stripe._authenticator)&&void 0!==c?c:null;let h=W(r),p=(e,t,r,o,n)=>setTimeout(e,this._getSleepTimeInMS(o,n),t,r,o+1),f=(o,u,c)=>{let m=a.settings&&a.settings.timeout&&Number.isInteger(a.settings.timeout)&&a.settings.timeout>=0?a.settings.timeout:this._stripe.getApiField("timeout"),y={host:t||this._stripe.getApiField("host"),port:this._stripe.getApiField("port"),path:r,method:e,headers:Object.assign({},u),body:d,protocol:this._stripe.getApiField("protocol")};n(y).then(()=>{let t=this._stripe.getApiField("httpClient").makeRequest(y.host,y.port,y.path,y.method,y.headers,y.body,y.protocol,m),n=Date.now(),d=L({api_version:o,account:u["Stripe-Account"],idempotency_key:u["Idempotency-Key"],method:e,path:r,request_start_time:n}),v=c||0,P=this._getMaxNetworkRetries(a.settings||{});this._stripe._emitter.emit("request",d),t.then(e=>K._shouldRetry(e,v,P)?p(f,o,u,v,e.getHeaders()["retry-after"]):a.streaming&&400>e.getStatusCode()?this._streamingResponseHandler(d,s,l)(e):this._jsonResponseHandler(d,h,s,l)(e)).catch(e=>K._shouldRetry(null,v,P,e)?p(f,o,u,v,null):l(new x({message:e.code&&e.code===i.TIMEOUT_ERROR_CODE?`Request aborted due to timeout being reached (${m}ms)`:K._generateConnectionErrorMessage(v),detail:e})))}).catch(e=>{throw new g({message:"Unable to authenticate the request",exception:e})})},m=(t,r)=>{if(t)return l(t);d=r,this._stripe.getClientUserAgent(t=>{let r=this._stripe.getApiField("version"),o=this._makeHeaders({contentType:"v2"==h?"application/json":"application/x-www-form-urlencoded",contentLength:d.length,apiVersion:r,clientUserAgent:t,method:e,userSuppliedHeaders:a.headers,userSuppliedSettings:a.settings,stripeAccount:"v2"==h?null:this._stripe.getApiField("stripeAccount"),stripeContext:"v2"==h?this._stripe.getApiField("stripeContext"):null,apiMode:h});f(r,o,0)})};if(u)u(e,o,a.headers,m);else{let e;m(null,"v2"==h?o?JSON.stringify(o,B):"":N(o||{},h))}}}class V{constructor(e,t,r,o){this.index=0,this.pagePromise=e,this.promiseCache={currentPromise:null},this.requestArgs=t,this.spec=r,this.stripeResource=o}async iterate(e){if(!(e&&e.data&&"number"==typeof e.data.length))throw Error("Unexpected: Stripe API response does not have a well-formed `data` array.");let t=ee(this.requestArgs);if(this.index<e.data.length){let r=t?e.data.length-1-this.index:this.index,o=e.data[r];return this.index+=1,{value:o,done:!1}}if(e.has_more){this.index=0,this.pagePromise=this.getNextPage(e);let t=await this.pagePromise;return this.iterate(t)}return{done:!0,value:void 0}}getNextPage(e){throw Error("Unimplemented")}async _next(){return this.iterate(await this.pagePromise)}next(){if(this.promiseCache.currentPromise)return this.promiseCache.currentPromise;let e=(async()=>{let e=await this._next();return this.promiseCache.currentPromise=null,e})();return this.promiseCache.currentPromise=e,e}}class J extends V{getNextPage(e){let t=ee(this.requestArgs),r=function(e,t){let r=t?0:e.data.length-1,o=e.data[r],n=o&&o.id;if(!n)throw Error("Unexpected: No `id` found on the last item while auto-paging a list.");return n}(e,t);return this.stripeResource._makeRequest(this.requestArgs,this.spec,{[t?"ending_before":"starting_after"]:r})}}class Q extends V{getNextPage(e){if(!e.next_page)throw Error("Unexpected: Stripe API response does not have a well-formed `next_page` field, but `has_more` was true.");return this.stripeResource._makeRequest(this.requestArgs,this.spec,{page:e.next_page})}}class X{constructor(e,t,r,o){this.currentPageIterator=(async()=>(await e).data[Symbol.iterator]())(),this.nextPageUrl=(async()=>(await e).next_page_url||null)(),this.requestArgs=t,this.spec=r,this.stripeResource=o}async turnPage(){let e=await this.nextPageUrl;if(!e)return null;this.spec.fullPath=e;let t=await this.stripeResource._makeRequest([],this.spec,{});return this.nextPageUrl=Promise.resolve(t.next_page_url),this.currentPageIterator=Promise.resolve(t.data[Symbol.iterator]()),this.currentPageIterator}async next(){{let e=(await this.currentPageIterator).next();if(!e.done)return{done:!1,value:e.value}}let e=await this.turnPage();if(!e)return{done:!0,value:void 0};let t=e.next();return t.done?{done:!0,value:void 0}:{done:!1,value:t.value}}}let Y=(e,t,r,o)=>{let n=W(r.fullPath||r.path);return"v2"!==n&&"search"===r.methodType?Z(new Q(o,t,r,e)):"v2"!==n&&"list"===r.methodType?Z(new J(o,t,r,e)):"v2"===n&&"list"===r.methodType?Z(new X(o,t,r,e)):null},Z=e=>{var t;let r=(t=(...t)=>e.next(...t),function(){let e=[].slice.call(arguments),r=function(e){if(0===e.length)return;let t=e[0];if("function"!=typeof t)throw Error(`The first argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);if(2===t.length)return t;if(t.length>2)throw Error(`The \`onItem\` callback function passed to autoPagingEach must accept at most two arguments; got ${t}`);return function(e,r){r(t(e))}}(e),o=function(e){if(e.length<2)return null;let t=e[1];if("function"!=typeof t)throw Error(`The second argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);return t}(e);if(e.length>2)throw Error(`autoPagingEach takes up to two arguments; received ${e}`);return U(new Promise((e,o)=>{t().then(function o(n){if(n.done){e();return}let i=n.value;return new Promise(e=>{r(i,e)}).then(e=>!1===e?o({done:!0,value:void 0}):t().then(o))}).catch(o)}),o)}),o={autoPagingEach:r,autoPagingToArray:function(e,t){let o=e&&e.limit;if(!o)throw Error("You must pass a `limit` option to autoPagingToArray, e.g., `autoPagingToArray({limit: 1000});`.");if(o>1e4)throw Error("You cannot specify a limit of more than 10,000 items to fetch in `autoPagingToArray`; use `autoPagingEach` to iterate through longer lists.");return U(new Promise((e,t)=>{let n=[];r(e=>{if(n.push(e),n.length>=o)return!1}).then(()=>{e(n)}).catch(t)}),t)},next:()=>e.next(),return:()=>({}),["undefined"!=typeof Symbol&&Symbol.asyncIterator?Symbol.asyncIterator:"@@asyncIterator"]:()=>o};return o};function ee(e){return!!F([].slice.call(e)).ending_before}function et(e,t){if(this._stripe=e,t)throw Error("Support for curried url params was dropped in stripe-node v7.0.0. Instead, pass two ids.");this.basePath=M(this.basePath||e.getApiField("basePath")),this.resourcePath=this.path,this.path=M(this.path),this.initialize(...arguments)}function er(e,t){for(let r in t){if(!Object.prototype.hasOwnProperty.call(t,r))continue;let o=r[0].toLowerCase()+r.substring(1),n=new t[r](e);this[o]=n}}function eo(e,t){return function(e){return new er(e,t)}}et.extend=function(e){let t=this,r=Object.prototype.hasOwnProperty.call(e,"constructor")?e.constructor:function(...e){t.apply(this,e)};return Object.assign(r,t),r.prototype=Object.create(t.prototype),Object.assign(r.prototype,e),r},et.method=function(e){if(void 0!==e.path&&void 0!==e.fullPath)throw Error(`Method spec specified both a 'path' (${e.path}) and a 'fullPath' (${e.fullPath}).`);return function(...t){let r="function"==typeof t[t.length-1]&&t.pop();e.urlParams=function(e){let t=e.match(/\{\w+\}/g);return t?t.map(e=>e.replace(/[{}]/g,"")):[]}(e.fullPath||this.createResourcePathWithSymbols(e.path||""));let o=U(this._makeRequest(t,e,{}),r);return Object.assign(o,Y(this,t,e,o)),o}},et.MAX_BUFFERED_REQUEST_METRICS=100,et.prototype={_stripe:null,path:"",resourcePath:"",basePath:null,initialize(){},requestDataProcessor:null,validateRequest:null,createFullPath(e,t){let r=[this.basePath(t),this.path(t)];if("function"==typeof e){let o=e(t);o&&r.push(o)}else r.push(e);return this._joinUrlParts(r)},createResourcePathWithSymbols(e){return e?`/${this._joinUrlParts([this.resourcePath,e])}`:`/${this.resourcePath}`},_joinUrlParts:e=>e.join("/").replace(/\/{2,}/g,"/"),_getRequestOpts(e,t,r){var o;let n=(t.method||"GET").toUpperCase(),i=t.usage||[],a=t.urlParams||[],s=t.encode||(e=>e),l=!!t.fullPath,u=M(l?t.fullPath:t.path||""),c=l?t.fullPath:this.createResourcePathWithSymbols(t.path),d=[].slice.call(e),h=a.reduce((e,t)=>{let r=d.shift();if("string"!=typeof r)throw Error(`Stripe: Argument "${t}" must be a string, but got: ${r} (on API request to \`${n} ${c}\`)`);return e[t]=r,e},{}),p=s(Object.assign({},F(d),r)),f=q(d),m=f.host||t.host,y=!!t.streaming;if(d.filter(e=>null!=e).length)throw Error(`Stripe: Unknown arguments (${d}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options. (on API request to ${n} \`${c}\`)`);let v=l?u(h):this.createFullPath(u,h),P=Object.assign(f.headers,t.headers);t.validator&&t.validator(p,{headers:P});let g="GET"===t.method||"DELETE"===t.method;return{requestMethod:n,requestPath:v,bodyData:g?null:p,queryData:g?p:{},authenticator:null!==(o=f.authenticator)&&void 0!==o?o:null,headers:P,host:null!=m?m:null,streaming:y,settings:f.settings,usage:i}},_makeRequest(e,t,r){return new Promise((o,n)=>{var i;let a;try{a=this._getRequestOpts(e,t,r)}catch(e){n(e);return}let s=0===Object.keys(a.queryData).length,l=[a.requestPath,s?"":"?",N(a.queryData,W(a.requestPath))].join(""),{headers:u,settings:c}=a;this._stripe._requestSender._request(a.requestMethod,a.host,l,a.bodyData,a.authenticator,{headers:u,settings:c,streaming:a.streaming},a.usage,function(e,r){e?n(e):o(t.transformResponseData?t.transformResponseData(r):r)},null===(i=this.requestDataProcessor)||void 0===i?void 0:i.bind(this))})}};let en=et.method,ei=et.extend({retrieve:en({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}"}),list:en({method:"GET",fullPath:"/v1/financial_connections/accounts",methodType:"list"}),disconnect:en({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/disconnect"}),listOwners:en({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}/owners",methodType:"list"}),refresh:en({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/refresh"}),subscribe:en({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/subscribe"}),unsubscribe:en({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/unsubscribe"})}),ea=et.method,es=et.extend({retrieve:ea({method:"GET",fullPath:"/v1/entitlements/active_entitlements/{id}"}),list:ea({method:"GET",fullPath:"/v1/entitlements/active_entitlements",methodType:"list"})}),el=et.method,eu=et.extend({create:el({method:"POST",fullPath:"/v1/billing/alerts"}),retrieve:el({method:"GET",fullPath:"/v1/billing/alerts/{id}"}),list:el({method:"GET",fullPath:"/v1/billing/alerts",methodType:"list"}),activate:el({method:"POST",fullPath:"/v1/billing/alerts/{id}/activate"}),archive:el({method:"POST",fullPath:"/v1/billing/alerts/{id}/archive"}),deactivate:el({method:"POST",fullPath:"/v1/billing/alerts/{id}/deactivate"})}),ec=et.method,ed=et.extend({create:ec({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations"}),capture:ec({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/capture"}),expire:ec({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/expire"}),finalizeAmount:ec({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/finalize_amount"}),increment:ec({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/increment"}),respond:ec({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/fraud_challenges/respond"}),reverse:ec({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/reverse"})}),eh=et.method,ep=et.extend({retrieve:eh({method:"GET",fullPath:"/v1/issuing/authorizations/{authorization}"}),update:eh({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}"}),list:eh({method:"GET",fullPath:"/v1/issuing/authorizations",methodType:"list"}),approve:eh({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/approve"}),decline:eh({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/decline"})}),ef=et.method,em=et.extend({create:ef({method:"POST",fullPath:"/v1/tax/calculations"}),retrieve:ef({method:"GET",fullPath:"/v1/tax/calculations/{calculation}"}),listLineItems:ef({method:"GET",fullPath:"/v1/tax/calculations/{calculation}/line_items",methodType:"list"})}),ey=et.method,ev=et.extend({create:ey({method:"POST",fullPath:"/v1/issuing/cardholders"}),retrieve:ey({method:"GET",fullPath:"/v1/issuing/cardholders/{cardholder}"}),update:ey({method:"POST",fullPath:"/v1/issuing/cardholders/{cardholder}"}),list:ey({method:"GET",fullPath:"/v1/issuing/cardholders",methodType:"list"})}),eP=et.method,eg=et.extend({deliverCard:eP({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/deliver"}),failCard:eP({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/fail"}),returnCard:eP({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/return"}),shipCard:eP({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/ship"}),submitCard:eP({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/submit"})}),eT=et.method,e_=et.extend({create:eT({method:"POST",fullPath:"/v1/issuing/cards"}),retrieve:eT({method:"GET",fullPath:"/v1/issuing/cards/{card}"}),update:eT({method:"POST",fullPath:"/v1/issuing/cards/{card}"}),list:eT({method:"GET",fullPath:"/v1/issuing/cards",methodType:"list"})}),eE=et.method,eS=et.extend({create:eE({method:"POST",fullPath:"/v1/billing_portal/configurations"}),retrieve:eE({method:"GET",fullPath:"/v1/billing_portal/configurations/{configuration}"}),update:eE({method:"POST",fullPath:"/v1/billing_portal/configurations/{configuration}"}),list:eE({method:"GET",fullPath:"/v1/billing_portal/configurations",methodType:"list"})}),eb=et.method,eO=et.extend({create:eb({method:"POST",fullPath:"/v1/terminal/configurations"}),retrieve:eb({method:"GET",fullPath:"/v1/terminal/configurations/{configuration}"}),update:eb({method:"POST",fullPath:"/v1/terminal/configurations/{configuration}"}),list:eb({method:"GET",fullPath:"/v1/terminal/configurations",methodType:"list"}),del:eb({method:"DELETE",fullPath:"/v1/terminal/configurations/{configuration}"})}),ex=et.method,ew=et.extend({create:ex({method:"POST",fullPath:"/v1/test_helpers/confirmation_tokens"})}),eA=et.method,eG=et.extend({create:eA({method:"POST",fullPath:"/v1/terminal/connection_tokens"})}),eR=et.method,eC=et.extend({retrieve:eR({method:"GET",fullPath:"/v1/billing/credit_balance_summary"})}),ej=et.method,eI=et.extend({retrieve:ej({method:"GET",fullPath:"/v1/billing/credit_balance_transactions/{id}"}),list:ej({method:"GET",fullPath:"/v1/billing/credit_balance_transactions",methodType:"list"})}),ek=et.method,eD=et.extend({create:ek({method:"POST",fullPath:"/v1/billing/credit_grants"}),retrieve:ek({method:"GET",fullPath:"/v1/billing/credit_grants/{id}"}),update:ek({method:"POST",fullPath:"/v1/billing/credit_grants/{id}"}),list:ek({method:"GET",fullPath:"/v1/billing/credit_grants",methodType:"list"}),expire:ek({method:"POST",fullPath:"/v1/billing/credit_grants/{id}/expire"}),voidGrant:ek({method:"POST",fullPath:"/v1/billing/credit_grants/{id}/void"})}),eN=et.method,eM=et.extend({create:eN({method:"POST",fullPath:"/v1/treasury/credit_reversals"}),retrieve:eN({method:"GET",fullPath:"/v1/treasury/credit_reversals/{credit_reversal}"}),list:eN({method:"GET",fullPath:"/v1/treasury/credit_reversals",methodType:"list"})}),eF=et.method,eq=et.extend({fundCashBalance:eF({method:"POST",fullPath:"/v1/test_helpers/customers/{customer}/fund_cash_balance"})}),eL=et.method,eU=et.extend({create:eL({method:"POST",fullPath:"/v1/treasury/debit_reversals"}),retrieve:eL({method:"GET",fullPath:"/v1/treasury/debit_reversals/{debit_reversal}"}),list:eL({method:"GET",fullPath:"/v1/treasury/debit_reversals",methodType:"list"})}),eH=et.method,e$=et.extend({create:eH({method:"POST",fullPath:"/v1/issuing/disputes"}),retrieve:eH({method:"GET",fullPath:"/v1/issuing/disputes/{dispute}"}),update:eH({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}"}),list:eH({method:"GET",fullPath:"/v1/issuing/disputes",methodType:"list"}),submit:eH({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}/submit"})}),ez=et.method,eB=et.extend({retrieve:ez({method:"GET",fullPath:"/v1/radar/early_fraud_warnings/{early_fraud_warning}"}),list:ez({method:"GET",fullPath:"/v1/radar/early_fraud_warnings",methodType:"list"})}),eW=et.method,eK=et.extend({create:eW({method:"POST",fullPath:"/v2/core/event_destinations"}),retrieve:eW({method:"GET",fullPath:"/v2/core/event_destinations/{id}"}),update:eW({method:"POST",fullPath:"/v2/core/event_destinations/{id}"}),list:eW({method:"GET",fullPath:"/v2/core/event_destinations",methodType:"list"}),del:eW({method:"DELETE",fullPath:"/v2/core/event_destinations/{id}"}),disable:eW({method:"POST",fullPath:"/v2/core/event_destinations/{id}/disable"}),enable:eW({method:"POST",fullPath:"/v2/core/event_destinations/{id}/enable"}),ping:eW({method:"POST",fullPath:"/v2/core/event_destinations/{id}/ping"})}),eV=et.method,eJ=et.extend({retrieve(...e){return eV({method:"GET",fullPath:"/v2/core/events/{id}",transformResponseData:e=>this.addFetchRelatedObjectIfNeeded(e)}).apply(this,e)},list(...e){return eV({method:"GET",fullPath:"/v2/core/events",methodType:"list",transformResponseData:e=>Object.assign(Object.assign({},e),{data:e.data.map(this.addFetchRelatedObjectIfNeeded.bind(this))})}).apply(this,e)},addFetchRelatedObjectIfNeeded(e){return e.related_object&&e.related_object.url?Object.assign(Object.assign({},e),{fetchRelatedObject:()=>eV({method:"GET",fullPath:e.related_object.url}).apply(this,[{stripeAccount:e.context}])}):e}}),eQ=et.method,eX=et.extend({create:eQ({method:"POST",fullPath:"/v1/entitlements/features"}),retrieve:eQ({method:"GET",fullPath:"/v1/entitlements/features/{id}"}),update:eQ({method:"POST",fullPath:"/v1/entitlements/features/{id}"}),list:eQ({method:"GET",fullPath:"/v1/entitlements/features",methodType:"list"})}),eY=et.method,eZ=et.extend({create:eY({method:"POST",fullPath:"/v1/treasury/financial_accounts"}),retrieve:eY({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),update:eY({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),list:eY({method:"GET",fullPath:"/v1/treasury/financial_accounts",methodType:"list"}),close:eY({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/close"}),retrieveFeatures:eY({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"}),updateFeatures:eY({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"})}),e1=et.method,e0=et.extend({fail:e1({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/fail"}),returnInboundTransfer:e1({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/return"}),succeed:e1({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/succeed"})}),e2=et.method,e8=et.extend({create:e2({method:"POST",fullPath:"/v1/treasury/inbound_transfers"}),retrieve:e2({method:"GET",fullPath:"/v1/treasury/inbound_transfers/{id}"}),list:e2({method:"GET",fullPath:"/v1/treasury/inbound_transfers",methodType:"list"}),cancel:e2({method:"POST",fullPath:"/v1/treasury/inbound_transfers/{inbound_transfer}/cancel"})}),e6=et.method,e3=et.extend({create:e6({method:"POST",fullPath:"/v1/terminal/locations"}),retrieve:e6({method:"GET",fullPath:"/v1/terminal/locations/{location}"}),update:e6({method:"POST",fullPath:"/v1/terminal/locations/{location}"}),list:e6({method:"GET",fullPath:"/v1/terminal/locations",methodType:"list"}),del:e6({method:"DELETE",fullPath:"/v1/terminal/locations/{location}"})}),e4=et.method,e5=et.extend({create:e4({method:"POST",fullPath:"/v1/billing/meter_event_adjustments"})}),e9=et.method,e7=et.extend({create:e9({method:"POST",fullPath:"/v2/billing/meter_event_adjustments"})}),te=et.method,tt=et.extend({create:te({method:"POST",fullPath:"/v2/billing/meter_event_session"})}),tr=et.method,to=et.extend({create:tr({method:"POST",fullPath:"/v2/billing/meter_event_stream",host:"meter-events.stripe.com"})}),tn=et.method,ti=et.extend({create:tn({method:"POST",fullPath:"/v1/billing/meter_events"})}),ta=et.method,ts=et.extend({create:ta({method:"POST",fullPath:"/v2/billing/meter_events"})}),tl=et.method,tu=et.extend({create:tl({method:"POST",fullPath:"/v1/billing/meters"}),retrieve:tl({method:"GET",fullPath:"/v1/billing/meters/{id}"}),update:tl({method:"POST",fullPath:"/v1/billing/meters/{id}"}),list:tl({method:"GET",fullPath:"/v1/billing/meters",methodType:"list"}),deactivate:tl({method:"POST",fullPath:"/v1/billing/meters/{id}/deactivate"}),listEventSummaries:tl({method:"GET",fullPath:"/v1/billing/meters/{id}/event_summaries",methodType:"list"}),reactivate:tl({method:"POST",fullPath:"/v1/billing/meters/{id}/reactivate"})}),tc=et.method,td=et.extend({create:tc({method:"POST",fullPath:"/v1/climate/orders"}),retrieve:tc({method:"GET",fullPath:"/v1/climate/orders/{order}"}),update:tc({method:"POST",fullPath:"/v1/climate/orders/{order}"}),list:tc({method:"GET",fullPath:"/v1/climate/orders",methodType:"list"}),cancel:tc({method:"POST",fullPath:"/v1/climate/orders/{order}/cancel"})}),th=et.method,tp=et.extend({update:th({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}"}),fail:th({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/fail"}),post:th({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/post"}),returnOutboundPayment:th({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/return"})}),tf=et.method,tm=et.extend({create:tf({method:"POST",fullPath:"/v1/treasury/outbound_payments"}),retrieve:tf({method:"GET",fullPath:"/v1/treasury/outbound_payments/{id}"}),list:tf({method:"GET",fullPath:"/v1/treasury/outbound_payments",methodType:"list"}),cancel:tf({method:"POST",fullPath:"/v1/treasury/outbound_payments/{id}/cancel"})}),ty=et.method,tv=et.extend({update:ty({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}"}),fail:ty({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/fail"}),post:ty({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/post"}),returnOutboundTransfer:ty({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/return"})}),tP=et.method,tg=et.extend({create:tP({method:"POST",fullPath:"/v1/treasury/outbound_transfers"}),retrieve:tP({method:"GET",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}"}),list:tP({method:"GET",fullPath:"/v1/treasury/outbound_transfers",methodType:"list"}),cancel:tP({method:"POST",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}/cancel"})}),tT=et.method,t_=et.extend({activate:tT({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/activate"}),deactivate:tT({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/deactivate"}),reject:tT({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/reject"})}),tE=et.method,tS=et.extend({create:tE({method:"POST",fullPath:"/v1/issuing/personalization_designs"}),retrieve:tE({method:"GET",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),update:tE({method:"POST",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),list:tE({method:"GET",fullPath:"/v1/issuing/personalization_designs",methodType:"list"})}),tb=et.method,tO=et.extend({retrieve:tb({method:"GET",fullPath:"/v1/issuing/physical_bundles/{physical_bundle}"}),list:tb({method:"GET",fullPath:"/v1/issuing/physical_bundles",methodType:"list"})}),tx=et.method,tw=et.extend({retrieve:tx({method:"GET",fullPath:"/v1/climate/products/{product}"}),list:tx({method:"GET",fullPath:"/v1/climate/products",methodType:"list"})}),tA=et.method,tG=et.extend({presentPaymentMethod:tA({method:"POST",fullPath:"/v1/test_helpers/terminal/readers/{reader}/present_payment_method"})}),tR=et.method,tC=et.extend({create:tR({method:"POST",fullPath:"/v1/terminal/readers"}),retrieve:tR({method:"GET",fullPath:"/v1/terminal/readers/{reader}"}),update:tR({method:"POST",fullPath:"/v1/terminal/readers/{reader}"}),list:tR({method:"GET",fullPath:"/v1/terminal/readers",methodType:"list"}),del:tR({method:"DELETE",fullPath:"/v1/terminal/readers/{reader}"}),cancelAction:tR({method:"POST",fullPath:"/v1/terminal/readers/{reader}/cancel_action"}),processPaymentIntent:tR({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_payment_intent"}),processSetupIntent:tR({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_setup_intent"}),refundPayment:tR({method:"POST",fullPath:"/v1/terminal/readers/{reader}/refund_payment"}),setReaderDisplay:tR({method:"POST",fullPath:"/v1/terminal/readers/{reader}/set_reader_display"})}),tj=et.method,tI=et.extend({create:tj({method:"POST",fullPath:"/v1/test_helpers/treasury/received_credits"})}),tk=et.method,tD=et.extend({retrieve:tk({method:"GET",fullPath:"/v1/treasury/received_credits/{id}"}),list:tk({method:"GET",fullPath:"/v1/treasury/received_credits",methodType:"list"})}),tN=et.method,tM=et.extend({create:tN({method:"POST",fullPath:"/v1/test_helpers/treasury/received_debits"})}),tF=et.method,tq=et.extend({retrieve:tF({method:"GET",fullPath:"/v1/treasury/received_debits/{id}"}),list:tF({method:"GET",fullPath:"/v1/treasury/received_debits",methodType:"list"})}),tL=et.method,tU=et.extend({expire:tL({method:"POST",fullPath:"/v1/test_helpers/refunds/{refund}/expire"})}),tH=et.method,t$=et.extend({create:tH({method:"POST",fullPath:"/v1/tax/registrations"}),retrieve:tH({method:"GET",fullPath:"/v1/tax/registrations/{id}"}),update:tH({method:"POST",fullPath:"/v1/tax/registrations/{id}"}),list:tH({method:"GET",fullPath:"/v1/tax/registrations",methodType:"list"})}),tz=et.method,tB=et.extend({create:tz({method:"POST",fullPath:"/v1/reporting/report_runs"}),retrieve:tz({method:"GET",fullPath:"/v1/reporting/report_runs/{report_run}"}),list:tz({method:"GET",fullPath:"/v1/reporting/report_runs",methodType:"list"})}),tW=et.method,tK=et.extend({retrieve:tW({method:"GET",fullPath:"/v1/reporting/report_types/{report_type}"}),list:tW({method:"GET",fullPath:"/v1/reporting/report_types",methodType:"list"})}),tV=et.method,tJ=et.extend({create:tV({method:"POST",fullPath:"/v1/forwarding/requests"}),retrieve:tV({method:"GET",fullPath:"/v1/forwarding/requests/{id}"}),list:tV({method:"GET",fullPath:"/v1/forwarding/requests",methodType:"list"})}),tQ=et.method,tX=et.extend({retrieve:tQ({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs/{scheduled_query_run}"}),list:tQ({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs",methodType:"list"})}),tY=et.method,tZ=et.extend({create:tY({method:"POST",fullPath:"/v1/apps/secrets"}),list:tY({method:"GET",fullPath:"/v1/apps/secrets",methodType:"list"}),deleteWhere:tY({method:"POST",fullPath:"/v1/apps/secrets/delete"}),find:tY({method:"GET",fullPath:"/v1/apps/secrets/find"})}),t1=et.method,t0=et.extend({create:t1({method:"POST",fullPath:"/v1/billing_portal/sessions"})}),t2=et.method,t8=et.extend({create:t2({method:"POST",fullPath:"/v1/checkout/sessions"}),retrieve:t2({method:"GET",fullPath:"/v1/checkout/sessions/{session}"}),update:t2({method:"POST",fullPath:"/v1/checkout/sessions/{session}"}),list:t2({method:"GET",fullPath:"/v1/checkout/sessions",methodType:"list"}),expire:t2({method:"POST",fullPath:"/v1/checkout/sessions/{session}/expire"}),listLineItems:t2({method:"GET",fullPath:"/v1/checkout/sessions/{session}/line_items",methodType:"list"})}),t6=et.method,t3=et.extend({create:t6({method:"POST",fullPath:"/v1/financial_connections/sessions"}),retrieve:t6({method:"GET",fullPath:"/v1/financial_connections/sessions/{session}"})}),t4=et.method,t5=et.extend({retrieve:t4({method:"GET",fullPath:"/v1/tax/settings"}),update:t4({method:"POST",fullPath:"/v1/tax/settings"})}),t9=et.method,t7=et.extend({retrieve:t9({method:"GET",fullPath:"/v1/climate/suppliers/{supplier}"}),list:t9({method:"GET",fullPath:"/v1/climate/suppliers",methodType:"list"})}),re=et.method,rt=et.extend({create:re({method:"POST",fullPath:"/v1/test_helpers/test_clocks"}),retrieve:re({method:"GET",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),list:re({method:"GET",fullPath:"/v1/test_helpers/test_clocks",methodType:"list"}),del:re({method:"DELETE",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),advance:re({method:"POST",fullPath:"/v1/test_helpers/test_clocks/{test_clock}/advance"})}),rr=et.method,ro=et.extend({retrieve:rr({method:"GET",fullPath:"/v1/issuing/tokens/{token}"}),update:rr({method:"POST",fullPath:"/v1/issuing/tokens/{token}"}),list:rr({method:"GET",fullPath:"/v1/issuing/tokens",methodType:"list"})}),rn=et.method,ri=et.extend({retrieve:rn({method:"GET",fullPath:"/v1/treasury/transaction_entries/{id}"}),list:rn({method:"GET",fullPath:"/v1/treasury/transaction_entries",methodType:"list"})}),ra=et.method,rs=et.extend({createForceCapture:ra({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_force_capture"}),createUnlinkedRefund:ra({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_unlinked_refund"}),refund:ra({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/{transaction}/refund"})}),rl=et.method,ru=et.extend({retrieve:rl({method:"GET",fullPath:"/v1/financial_connections/transactions/{transaction}"}),list:rl({method:"GET",fullPath:"/v1/financial_connections/transactions",methodType:"list"})}),rc=et.method,rd=et.extend({retrieve:rc({method:"GET",fullPath:"/v1/issuing/transactions/{transaction}"}),update:rc({method:"POST",fullPath:"/v1/issuing/transactions/{transaction}"}),list:rc({method:"GET",fullPath:"/v1/issuing/transactions",methodType:"list"})}),rh=et.method,rp=et.extend({retrieve:rh({method:"GET",fullPath:"/v1/tax/transactions/{transaction}"}),createFromCalculation:rh({method:"POST",fullPath:"/v1/tax/transactions/create_from_calculation"}),createReversal:rh({method:"POST",fullPath:"/v1/tax/transactions/create_reversal"}),listLineItems:rh({method:"GET",fullPath:"/v1/tax/transactions/{transaction}/line_items",methodType:"list"})}),rf=et.method,rm=et.extend({retrieve:rf({method:"GET",fullPath:"/v1/treasury/transactions/{id}"}),list:rf({method:"GET",fullPath:"/v1/treasury/transactions",methodType:"list"})}),ry=et.method,rv=et.extend({create:ry({method:"POST",fullPath:"/v1/radar/value_list_items"}),retrieve:ry({method:"GET",fullPath:"/v1/radar/value_list_items/{item}"}),list:ry({method:"GET",fullPath:"/v1/radar/value_list_items",methodType:"list"}),del:ry({method:"DELETE",fullPath:"/v1/radar/value_list_items/{item}"})}),rP=et.method,rg=et.extend({create:rP({method:"POST",fullPath:"/v1/radar/value_lists"}),retrieve:rP({method:"GET",fullPath:"/v1/radar/value_lists/{value_list}"}),update:rP({method:"POST",fullPath:"/v1/radar/value_lists/{value_list}"}),list:rP({method:"GET",fullPath:"/v1/radar/value_lists",methodType:"list"}),del:rP({method:"DELETE",fullPath:"/v1/radar/value_lists/{value_list}"})}),rT=et.method,r_=et.extend({retrieve:rT({method:"GET",fullPath:"/v1/identity/verification_reports/{report}"}),list:rT({method:"GET",fullPath:"/v1/identity/verification_reports",methodType:"list"})}),rE=et.method,rS=et.extend({create:rE({method:"POST",fullPath:"/v1/identity/verification_sessions"}),retrieve:rE({method:"GET",fullPath:"/v1/identity/verification_sessions/{session}"}),update:rE({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}"}),list:rE({method:"GET",fullPath:"/v1/identity/verification_sessions",methodType:"list"}),cancel:rE({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/cancel"}),redact:rE({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/redact"})}),rb=et.method,rO=et.extend({create:rb({method:"POST",fullPath:"/v1/accounts"}),retrieve(e,...t){return"string"==typeof e?rb({method:"GET",fullPath:"/v1/accounts/{id}"}).apply(this,[e,...t]):(null==e&&[].shift.apply([e,...t]),rb({method:"GET",fullPath:"/v1/account"}).apply(this,[e,...t]))},update:rb({method:"POST",fullPath:"/v1/accounts/{account}"}),list:rb({method:"GET",fullPath:"/v1/accounts",methodType:"list"}),del:rb({method:"DELETE",fullPath:"/v1/accounts/{account}"}),createExternalAccount:rb({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts"}),createLoginLink:rb({method:"POST",fullPath:"/v1/accounts/{account}/login_links"}),createPerson:rb({method:"POST",fullPath:"/v1/accounts/{account}/persons"}),deleteExternalAccount:rb({method:"DELETE",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),deletePerson:rb({method:"DELETE",fullPath:"/v1/accounts/{account}/persons/{person}"}),listCapabilities:rb({method:"GET",fullPath:"/v1/accounts/{account}/capabilities",methodType:"list"}),listExternalAccounts:rb({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts",methodType:"list"}),listPersons:rb({method:"GET",fullPath:"/v1/accounts/{account}/persons",methodType:"list"}),reject:rb({method:"POST",fullPath:"/v1/accounts/{account}/reject"}),retrieveCurrent:rb({method:"GET",fullPath:"/v1/account"}),retrieveCapability:rb({method:"GET",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),retrieveExternalAccount:rb({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),retrievePerson:rb({method:"GET",fullPath:"/v1/accounts/{account}/persons/{person}"}),updateCapability:rb({method:"POST",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),updateExternalAccount:rb({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),updatePerson:rb({method:"POST",fullPath:"/v1/accounts/{account}/persons/{person}"})}),rx=et.method,rw=et.extend({create:rx({method:"POST",fullPath:"/v1/account_links"})}),rA=et.method,rG=et.extend({create:rA({method:"POST",fullPath:"/v1/account_sessions"})}),rR=et.method,rC=et.extend({create:rR({method:"POST",fullPath:"/v1/apple_pay/domains"}),retrieve:rR({method:"GET",fullPath:"/v1/apple_pay/domains/{domain}"}),list:rR({method:"GET",fullPath:"/v1/apple_pay/domains",methodType:"list"}),del:rR({method:"DELETE",fullPath:"/v1/apple_pay/domains/{domain}"})}),rj=et.method,rI=et.extend({retrieve:rj({method:"GET",fullPath:"/v1/application_fees/{id}"}),list:rj({method:"GET",fullPath:"/v1/application_fees",methodType:"list"}),createRefund:rj({method:"POST",fullPath:"/v1/application_fees/{id}/refunds"}),listRefunds:rj({method:"GET",fullPath:"/v1/application_fees/{id}/refunds",methodType:"list"}),retrieveRefund:rj({method:"GET",fullPath:"/v1/application_fees/{fee}/refunds/{id}"}),updateRefund:rj({method:"POST",fullPath:"/v1/application_fees/{fee}/refunds/{id}"})}),rk=et.method,rD=et.extend({retrieve:rk({method:"GET",fullPath:"/v1/balance"})}),rN=et.method,rM=et.extend({retrieve:rN({method:"GET",fullPath:"/v1/balance_transactions/{id}"}),list:rN({method:"GET",fullPath:"/v1/balance_transactions",methodType:"list"})}),rF=et.method,rq=et.extend({create:rF({method:"POST",fullPath:"/v1/charges"}),retrieve:rF({method:"GET",fullPath:"/v1/charges/{charge}"}),update:rF({method:"POST",fullPath:"/v1/charges/{charge}"}),list:rF({method:"GET",fullPath:"/v1/charges",methodType:"list"}),capture:rF({method:"POST",fullPath:"/v1/charges/{charge}/capture"}),search:rF({method:"GET",fullPath:"/v1/charges/search",methodType:"search"})}),rL=et.method,rU=et.extend({retrieve:rL({method:"GET",fullPath:"/v1/confirmation_tokens/{confirmation_token}"})}),rH=et.method,r$=et.extend({retrieve:rH({method:"GET",fullPath:"/v1/country_specs/{country}"}),list:rH({method:"GET",fullPath:"/v1/country_specs",methodType:"list"})}),rz=et.method,rB=et.extend({create:rz({method:"POST",fullPath:"/v1/coupons"}),retrieve:rz({method:"GET",fullPath:"/v1/coupons/{coupon}"}),update:rz({method:"POST",fullPath:"/v1/coupons/{coupon}"}),list:rz({method:"GET",fullPath:"/v1/coupons",methodType:"list"}),del:rz({method:"DELETE",fullPath:"/v1/coupons/{coupon}"})}),rW=et.method,rK=et.extend({create:rW({method:"POST",fullPath:"/v1/credit_notes"}),retrieve:rW({method:"GET",fullPath:"/v1/credit_notes/{id}"}),update:rW({method:"POST",fullPath:"/v1/credit_notes/{id}"}),list:rW({method:"GET",fullPath:"/v1/credit_notes",methodType:"list"}),listLineItems:rW({method:"GET",fullPath:"/v1/credit_notes/{credit_note}/lines",methodType:"list"}),listPreviewLineItems:rW({method:"GET",fullPath:"/v1/credit_notes/preview/lines",methodType:"list"}),preview:rW({method:"GET",fullPath:"/v1/credit_notes/preview"}),voidCreditNote:rW({method:"POST",fullPath:"/v1/credit_notes/{id}/void"})}),rV=et.method,rJ=et.extend({create:rV({method:"POST",fullPath:"/v1/customer_sessions"})}),rQ=et.method,rX=et.extend({create:rQ({method:"POST",fullPath:"/v1/customers"}),retrieve:rQ({method:"GET",fullPath:"/v1/customers/{customer}"}),update:rQ({method:"POST",fullPath:"/v1/customers/{customer}"}),list:rQ({method:"GET",fullPath:"/v1/customers",methodType:"list"}),del:rQ({method:"DELETE",fullPath:"/v1/customers/{customer}"}),createBalanceTransaction:rQ({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions"}),createFundingInstructions:rQ({method:"POST",fullPath:"/v1/customers/{customer}/funding_instructions"}),createSource:rQ({method:"POST",fullPath:"/v1/customers/{customer}/sources"}),createTaxId:rQ({method:"POST",fullPath:"/v1/customers/{customer}/tax_ids"}),deleteDiscount:rQ({method:"DELETE",fullPath:"/v1/customers/{customer}/discount"}),deleteSource:rQ({method:"DELETE",fullPath:"/v1/customers/{customer}/sources/{id}"}),deleteTaxId:rQ({method:"DELETE",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),listBalanceTransactions:rQ({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions",methodType:"list"}),listCashBalanceTransactions:rQ({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions",methodType:"list"}),listPaymentMethods:rQ({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods",methodType:"list"}),listSources:rQ({method:"GET",fullPath:"/v1/customers/{customer}/sources",methodType:"list"}),listTaxIds:rQ({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids",methodType:"list"}),retrieveBalanceTransaction:rQ({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),retrieveCashBalance:rQ({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance"}),retrieveCashBalanceTransaction:rQ({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions/{transaction}"}),retrievePaymentMethod:rQ({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods/{payment_method}"}),retrieveSource:rQ({method:"GET",fullPath:"/v1/customers/{customer}/sources/{id}"}),retrieveTaxId:rQ({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),search:rQ({method:"GET",fullPath:"/v1/customers/search",methodType:"search"}),updateBalanceTransaction:rQ({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),updateCashBalance:rQ({method:"POST",fullPath:"/v1/customers/{customer}/cash_balance"}),updateSource:rQ({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}"}),verifySource:rQ({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}/verify"})}),rY=et.method,rZ=et.extend({retrieve:rY({method:"GET",fullPath:"/v1/disputes/{dispute}"}),update:rY({method:"POST",fullPath:"/v1/disputes/{dispute}"}),list:rY({method:"GET",fullPath:"/v1/disputes",methodType:"list"}),close:rY({method:"POST",fullPath:"/v1/disputes/{dispute}/close"})}),r1=et.method,r0=et.extend({create:r1({method:"POST",fullPath:"/v1/ephemeral_keys",validator:(e,t)=>{if(!t.headers||!t.headers["Stripe-Version"])throw Error("Passing apiVersion in a separate options hash is required to create an ephemeral key. See https://stripe.com/docs/api/versioning?lang=node")}}),del:r1({method:"DELETE",fullPath:"/v1/ephemeral_keys/{key}"})}),r2=et.method,r8=et.extend({retrieve:r2({method:"GET",fullPath:"/v1/events/{id}"}),list:r2({method:"GET",fullPath:"/v1/events",methodType:"list"})}),r6=et.method,r3=et.extend({retrieve:r6({method:"GET",fullPath:"/v1/exchange_rates/{rate_id}"}),list:r6({method:"GET",fullPath:"/v1/exchange_rates",methodType:"list"})}),r4=et.method,r5=et.extend({create:r4({method:"POST",fullPath:"/v1/file_links"}),retrieve:r4({method:"GET",fullPath:"/v1/file_links/{link}"}),update:r4({method:"POST",fullPath:"/v1/file_links/{link}"}),list:r4({method:"GET",fullPath:"/v1/file_links",methodType:"list"})}),r9=(e,t,r)=>{let o=(Math.round(1e16*Math.random())+Math.round(1e16*Math.random())).toString();r["Content-Type"]=`multipart/form-data; boundary=${o}`;let n=new TextEncoder,i=new Uint8Array(0),a=n.encode("\r\n");function s(e){let t=i,r=e instanceof Uint8Array?e:new Uint8Array(n.encode(e));(i=new Uint8Array(t.length+r.length+2)).set(t),i.set(r,t.length),i.set(a,i.length-2)}function l(e){return`"${e.replace(/"|"/g,"%22").replace(/\r\n|\r|\n/g," ")}"`}let u=function(e){let t={},r=(e,o)=>{Object.entries(e).forEach(([e,n])=>{let i=o?`${o}[${e}]`:e;if(function(e){let t=typeof e;return("function"===t||"object"===t)&&!!e}(n)){if(!(n instanceof Uint8Array)&&!Object.prototype.hasOwnProperty.call(n,"data"))return r(n,i);t[i]=n}else t[i]=String(n)})};return r(e,null),t}(t);for(let e in u){if(!Object.prototype.hasOwnProperty.call(u,e))continue;let t=u[e];s(`--${o}`),Object.prototype.hasOwnProperty.call(t,"data")?(s(`Content-Disposition: form-data; name=${l(e)}; filename=${l(t.name||"blob")}`),s(`Content-Type: ${t.type||"application/octet-stream"}`),s(""),s(t.data)):(s(`Content-Disposition: form-data; name=${l(e)}`),s(""),s(t))}return s(`--${o}--`),i},r7=et.method,oe=et.extend({create:r7({method:"POST",fullPath:"/v1/files",headers:{"Content-Type":"multipart/form-data"},host:"files.stripe.com"}),retrieve:r7({method:"GET",fullPath:"/v1/files/{file}"}),list:r7({method:"GET",fullPath:"/v1/files",methodType:"list"}),requestDataProcessor:function(e,t,r,o){if(t=t||{},"POST"!==e)return o(null,N(t));this._stripe._platformFunctions.tryBufferData(t).then(t=>o(null,r9(e,t,r))).catch(e=>o(e,null))}}),ot=et.method,or=et.extend({create:ot({method:"POST",fullPath:"/v1/invoiceitems"}),retrieve:ot({method:"GET",fullPath:"/v1/invoiceitems/{invoiceitem}"}),update:ot({method:"POST",fullPath:"/v1/invoiceitems/{invoiceitem}"}),list:ot({method:"GET",fullPath:"/v1/invoiceitems",methodType:"list"}),del:ot({method:"DELETE",fullPath:"/v1/invoiceitems/{invoiceitem}"})}),oo=et.method,on=et.extend({retrieve:oo({method:"GET",fullPath:"/v1/invoice_payments/{invoice_payment}"}),list:oo({method:"GET",fullPath:"/v1/invoice_payments",methodType:"list"})}),oi=et.method,oa=et.extend({retrieve:oi({method:"GET",fullPath:"/v1/invoice_rendering_templates/{template}"}),list:oi({method:"GET",fullPath:"/v1/invoice_rendering_templates",methodType:"list"}),archive:oi({method:"POST",fullPath:"/v1/invoice_rendering_templates/{template}/archive"}),unarchive:oi({method:"POST",fullPath:"/v1/invoice_rendering_templates/{template}/unarchive"})}),os=et.method,ol=et.extend({create:os({method:"POST",fullPath:"/v1/invoices"}),retrieve:os({method:"GET",fullPath:"/v1/invoices/{invoice}"}),update:os({method:"POST",fullPath:"/v1/invoices/{invoice}"}),list:os({method:"GET",fullPath:"/v1/invoices",methodType:"list"}),del:os({method:"DELETE",fullPath:"/v1/invoices/{invoice}"}),addLines:os({method:"POST",fullPath:"/v1/invoices/{invoice}/add_lines"}),createPreview:os({method:"POST",fullPath:"/v1/invoices/create_preview"}),finalizeInvoice:os({method:"POST",fullPath:"/v1/invoices/{invoice}/finalize"}),listLineItems:os({method:"GET",fullPath:"/v1/invoices/{invoice}/lines",methodType:"list"}),markUncollectible:os({method:"POST",fullPath:"/v1/invoices/{invoice}/mark_uncollectible"}),pay:os({method:"POST",fullPath:"/v1/invoices/{invoice}/pay"}),removeLines:os({method:"POST",fullPath:"/v1/invoices/{invoice}/remove_lines"}),search:os({method:"GET",fullPath:"/v1/invoices/search",methodType:"search"}),sendInvoice:os({method:"POST",fullPath:"/v1/invoices/{invoice}/send"}),updateLines:os({method:"POST",fullPath:"/v1/invoices/{invoice}/update_lines"}),updateLineItem:os({method:"POST",fullPath:"/v1/invoices/{invoice}/lines/{line_item_id}"}),voidInvoice:os({method:"POST",fullPath:"/v1/invoices/{invoice}/void"})}),ou=et.method,oc=et.extend({retrieve:ou({method:"GET",fullPath:"/v1/mandates/{mandate}"})}),od=et.method,oh="connect.stripe.com",op=et.extend({basePath:"/",authorizeUrl(e,t){e=e||{};let r="oauth/authorize";return(t=t||{}).express&&(r=`express/${r}`),e.response_type||(e.response_type="code"),e.client_id||(e.client_id=this._stripe.getClientId()),e.scope||(e.scope="read_write"),`https://${oh}/${r}?${N(e)}`},token:od({method:"POST",path:"oauth/token",host:oh}),deauthorize(e,...t){return e.client_id||(e.client_id=this._stripe.getClientId()),od({method:"POST",path:"oauth/deauthorize",host:oh}).apply(this,[e,...t])}}),of=et.method,om=et.extend({create:of({method:"POST",fullPath:"/v1/payment_intents"}),retrieve:of({method:"GET",fullPath:"/v1/payment_intents/{intent}"}),update:of({method:"POST",fullPath:"/v1/payment_intents/{intent}"}),list:of({method:"GET",fullPath:"/v1/payment_intents",methodType:"list"}),applyCustomerBalance:of({method:"POST",fullPath:"/v1/payment_intents/{intent}/apply_customer_balance"}),cancel:of({method:"POST",fullPath:"/v1/payment_intents/{intent}/cancel"}),capture:of({method:"POST",fullPath:"/v1/payment_intents/{intent}/capture"}),confirm:of({method:"POST",fullPath:"/v1/payment_intents/{intent}/confirm"}),incrementAuthorization:of({method:"POST",fullPath:"/v1/payment_intents/{intent}/increment_authorization"}),search:of({method:"GET",fullPath:"/v1/payment_intents/search",methodType:"search"}),verifyMicrodeposits:of({method:"POST",fullPath:"/v1/payment_intents/{intent}/verify_microdeposits"})}),oy=et.method,ov=et.extend({create:oy({method:"POST",fullPath:"/v1/payment_links"}),retrieve:oy({method:"GET",fullPath:"/v1/payment_links/{payment_link}"}),update:oy({method:"POST",fullPath:"/v1/payment_links/{payment_link}"}),list:oy({method:"GET",fullPath:"/v1/payment_links",methodType:"list"}),listLineItems:oy({method:"GET",fullPath:"/v1/payment_links/{payment_link}/line_items",methodType:"list"})}),oP=et.method,og=et.extend({create:oP({method:"POST",fullPath:"/v1/payment_method_configurations"}),retrieve:oP({method:"GET",fullPath:"/v1/payment_method_configurations/{configuration}"}),update:oP({method:"POST",fullPath:"/v1/payment_method_configurations/{configuration}"}),list:oP({method:"GET",fullPath:"/v1/payment_method_configurations",methodType:"list"})}),oT=et.method,o_=et.extend({create:oT({method:"POST",fullPath:"/v1/payment_method_domains"}),retrieve:oT({method:"GET",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),update:oT({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),list:oT({method:"GET",fullPath:"/v1/payment_method_domains",methodType:"list"}),validate:oT({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}/validate"})}),oE=et.method,oS=et.extend({create:oE({method:"POST",fullPath:"/v1/payment_methods"}),retrieve:oE({method:"GET",fullPath:"/v1/payment_methods/{payment_method}"}),update:oE({method:"POST",fullPath:"/v1/payment_methods/{payment_method}"}),list:oE({method:"GET",fullPath:"/v1/payment_methods",methodType:"list"}),attach:oE({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/attach"}),detach:oE({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/detach"})}),ob=et.method,oO=et.extend({create:ob({method:"POST",fullPath:"/v1/payouts"}),retrieve:ob({method:"GET",fullPath:"/v1/payouts/{payout}"}),update:ob({method:"POST",fullPath:"/v1/payouts/{payout}"}),list:ob({method:"GET",fullPath:"/v1/payouts",methodType:"list"}),cancel:ob({method:"POST",fullPath:"/v1/payouts/{payout}/cancel"}),reverse:ob({method:"POST",fullPath:"/v1/payouts/{payout}/reverse"})}),ox=et.method,ow=et.extend({create:ox({method:"POST",fullPath:"/v1/plans"}),retrieve:ox({method:"GET",fullPath:"/v1/plans/{plan}"}),update:ox({method:"POST",fullPath:"/v1/plans/{plan}"}),list:ox({method:"GET",fullPath:"/v1/plans",methodType:"list"}),del:ox({method:"DELETE",fullPath:"/v1/plans/{plan}"})}),oA=et.method,oG=et.extend({create:oA({method:"POST",fullPath:"/v1/prices"}),retrieve:oA({method:"GET",fullPath:"/v1/prices/{price}"}),update:oA({method:"POST",fullPath:"/v1/prices/{price}"}),list:oA({method:"GET",fullPath:"/v1/prices",methodType:"list"}),search:oA({method:"GET",fullPath:"/v1/prices/search",methodType:"search"})}),oR=et.method,oC=et.extend({create:oR({method:"POST",fullPath:"/v1/products"}),retrieve:oR({method:"GET",fullPath:"/v1/products/{id}"}),update:oR({method:"POST",fullPath:"/v1/products/{id}"}),list:oR({method:"GET",fullPath:"/v1/products",methodType:"list"}),del:oR({method:"DELETE",fullPath:"/v1/products/{id}"}),createFeature:oR({method:"POST",fullPath:"/v1/products/{product}/features"}),deleteFeature:oR({method:"DELETE",fullPath:"/v1/products/{product}/features/{id}"}),listFeatures:oR({method:"GET",fullPath:"/v1/products/{product}/features",methodType:"list"}),retrieveFeature:oR({method:"GET",fullPath:"/v1/products/{product}/features/{id}"}),search:oR({method:"GET",fullPath:"/v1/products/search",methodType:"search"})}),oj=et.method,oI=et.extend({create:oj({method:"POST",fullPath:"/v1/promotion_codes"}),retrieve:oj({method:"GET",fullPath:"/v1/promotion_codes/{promotion_code}"}),update:oj({method:"POST",fullPath:"/v1/promotion_codes/{promotion_code}"}),list:oj({method:"GET",fullPath:"/v1/promotion_codes",methodType:"list"})}),ok=et.method,oD=et.extend({create:ok({method:"POST",fullPath:"/v1/quotes"}),retrieve:ok({method:"GET",fullPath:"/v1/quotes/{quote}"}),update:ok({method:"POST",fullPath:"/v1/quotes/{quote}"}),list:ok({method:"GET",fullPath:"/v1/quotes",methodType:"list"}),accept:ok({method:"POST",fullPath:"/v1/quotes/{quote}/accept"}),cancel:ok({method:"POST",fullPath:"/v1/quotes/{quote}/cancel"}),finalizeQuote:ok({method:"POST",fullPath:"/v1/quotes/{quote}/finalize"}),listComputedUpfrontLineItems:ok({method:"GET",fullPath:"/v1/quotes/{quote}/computed_upfront_line_items",methodType:"list"}),listLineItems:ok({method:"GET",fullPath:"/v1/quotes/{quote}/line_items",methodType:"list"}),pdf:ok({method:"GET",fullPath:"/v1/quotes/{quote}/pdf",host:"files.stripe.com",streaming:!0})}),oN=et.method,oM=et.extend({create:oN({method:"POST",fullPath:"/v1/refunds"}),retrieve:oN({method:"GET",fullPath:"/v1/refunds/{refund}"}),update:oN({method:"POST",fullPath:"/v1/refunds/{refund}"}),list:oN({method:"GET",fullPath:"/v1/refunds",methodType:"list"}),cancel:oN({method:"POST",fullPath:"/v1/refunds/{refund}/cancel"})}),oF=et.method,oq=et.extend({retrieve:oF({method:"GET",fullPath:"/v1/reviews/{review}"}),list:oF({method:"GET",fullPath:"/v1/reviews",methodType:"list"}),approve:oF({method:"POST",fullPath:"/v1/reviews/{review}/approve"})}),oL=et.method,oU=et.extend({list:oL({method:"GET",fullPath:"/v1/setup_attempts",methodType:"list"})}),oH=et.method,o$=et.extend({create:oH({method:"POST",fullPath:"/v1/setup_intents"}),retrieve:oH({method:"GET",fullPath:"/v1/setup_intents/{intent}"}),update:oH({method:"POST",fullPath:"/v1/setup_intents/{intent}"}),list:oH({method:"GET",fullPath:"/v1/setup_intents",methodType:"list"}),cancel:oH({method:"POST",fullPath:"/v1/setup_intents/{intent}/cancel"}),confirm:oH({method:"POST",fullPath:"/v1/setup_intents/{intent}/confirm"}),verifyMicrodeposits:oH({method:"POST",fullPath:"/v1/setup_intents/{intent}/verify_microdeposits"})}),oz=et.method,oB=et.extend({create:oz({method:"POST",fullPath:"/v1/shipping_rates"}),retrieve:oz({method:"GET",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),update:oz({method:"POST",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),list:oz({method:"GET",fullPath:"/v1/shipping_rates",methodType:"list"})}),oW=et.method,oK=et.extend({create:oW({method:"POST",fullPath:"/v1/sources"}),retrieve:oW({method:"GET",fullPath:"/v1/sources/{source}"}),update:oW({method:"POST",fullPath:"/v1/sources/{source}"}),listSourceTransactions:oW({method:"GET",fullPath:"/v1/sources/{source}/source_transactions",methodType:"list"}),verify:oW({method:"POST",fullPath:"/v1/sources/{source}/verify"})}),oV=et.method,oJ=et.extend({create:oV({method:"POST",fullPath:"/v1/subscription_items"}),retrieve:oV({method:"GET",fullPath:"/v1/subscription_items/{item}"}),update:oV({method:"POST",fullPath:"/v1/subscription_items/{item}"}),list:oV({method:"GET",fullPath:"/v1/subscription_items",methodType:"list"}),del:oV({method:"DELETE",fullPath:"/v1/subscription_items/{item}"})}),oQ=et.method,oX=et.extend({create:oQ({method:"POST",fullPath:"/v1/subscription_schedules"}),retrieve:oQ({method:"GET",fullPath:"/v1/subscription_schedules/{schedule}"}),update:oQ({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}"}),list:oQ({method:"GET",fullPath:"/v1/subscription_schedules",methodType:"list"}),cancel:oQ({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/cancel"}),release:oQ({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/release"})}),oY=et.method,oZ=et.extend({create:oY({method:"POST",fullPath:"/v1/subscriptions"}),retrieve:oY({method:"GET",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),update:oY({method:"POST",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),list:oY({method:"GET",fullPath:"/v1/subscriptions",methodType:"list"}),cancel:oY({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),deleteDiscount:oY({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}/discount"}),resume:oY({method:"POST",fullPath:"/v1/subscriptions/{subscription}/resume"}),search:oY({method:"GET",fullPath:"/v1/subscriptions/search",methodType:"search"})}),o1=et.method,o0=et.extend({retrieve:o1({method:"GET",fullPath:"/v1/tax_codes/{id}"}),list:o1({method:"GET",fullPath:"/v1/tax_codes",methodType:"list"})}),o2=et.method,o8=et.extend({create:o2({method:"POST",fullPath:"/v1/tax_ids"}),retrieve:o2({method:"GET",fullPath:"/v1/tax_ids/{id}"}),list:o2({method:"GET",fullPath:"/v1/tax_ids",methodType:"list"}),del:o2({method:"DELETE",fullPath:"/v1/tax_ids/{id}"})}),o6=et.method,o3=et.extend({create:o6({method:"POST",fullPath:"/v1/tax_rates"}),retrieve:o6({method:"GET",fullPath:"/v1/tax_rates/{tax_rate}"}),update:o6({method:"POST",fullPath:"/v1/tax_rates/{tax_rate}"}),list:o6({method:"GET",fullPath:"/v1/tax_rates",methodType:"list"})}),o4=et.method,o5=et.extend({create:o4({method:"POST",fullPath:"/v1/tokens"}),retrieve:o4({method:"GET",fullPath:"/v1/tokens/{token}"})}),o9=et.method,o7=et.extend({create:o9({method:"POST",fullPath:"/v1/topups"}),retrieve:o9({method:"GET",fullPath:"/v1/topups/{topup}"}),update:o9({method:"POST",fullPath:"/v1/topups/{topup}"}),list:o9({method:"GET",fullPath:"/v1/topups",methodType:"list"}),cancel:o9({method:"POST",fullPath:"/v1/topups/{topup}/cancel"})}),ne=et.method,nt=et.extend({create:ne({method:"POST",fullPath:"/v1/transfers"}),retrieve:ne({method:"GET",fullPath:"/v1/transfers/{transfer}"}),update:ne({method:"POST",fullPath:"/v1/transfers/{transfer}"}),list:ne({method:"GET",fullPath:"/v1/transfers",methodType:"list"}),createReversal:ne({method:"POST",fullPath:"/v1/transfers/{id}/reversals"}),listReversals:ne({method:"GET",fullPath:"/v1/transfers/{id}/reversals",methodType:"list"}),retrieveReversal:ne({method:"GET",fullPath:"/v1/transfers/{transfer}/reversals/{id}"}),updateReversal:ne({method:"POST",fullPath:"/v1/transfers/{transfer}/reversals/{id}"})}),nr=et.method,no=et.extend({create:nr({method:"POST",fullPath:"/v1/webhook_endpoints"}),retrieve:nr({method:"GET",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),update:nr({method:"POST",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),list:nr({method:"GET",fullPath:"/v1/webhook_endpoints",methodType:"list"}),del:nr({method:"DELETE",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"})}),nn=eo("apps",{Secrets:tZ}),ni=eo("billing",{Alerts:eu,CreditBalanceSummary:eC,CreditBalanceTransactions:eI,CreditGrants:eD,MeterEventAdjustments:e5,MeterEvents:ti,Meters:tu}),na=eo("billingPortal",{Configurations:eS,Sessions:t0}),ns=eo("checkout",{Sessions:t8}),nl=eo("climate",{Orders:td,Products:tw,Suppliers:t7}),nu=eo("entitlements",{ActiveEntitlements:es,Features:eX}),nc=eo("financialConnections",{Accounts:ei,Sessions:t3,Transactions:ru}),nd=eo("forwarding",{Requests:tJ}),nh=eo("identity",{VerificationReports:r_,VerificationSessions:rS}),np=eo("issuing",{Authorizations:ep,Cardholders:ev,Cards:e_,Disputes:e$,PersonalizationDesigns:tS,PhysicalBundles:tO,Tokens:ro,Transactions:rd}),nf=eo("radar",{EarlyFraudWarnings:eB,ValueListItems:rv,ValueLists:rg}),nm=eo("reporting",{ReportRuns:tB,ReportTypes:tK}),ny=eo("sigma",{ScheduledQueryRuns:tX}),nv=eo("tax",{Calculations:em,Registrations:t$,Settings:t5,Transactions:rp}),nP=eo("terminal",{Configurations:eO,ConnectionTokens:eG,Locations:e3,Readers:tC}),ng=eo("testHelpers",{ConfirmationTokens:ew,Customers:eq,Refunds:tU,TestClocks:rt,Issuing:eo("issuing",{Authorizations:ed,Cards:eg,PersonalizationDesigns:t_,Transactions:rs}),Terminal:eo("terminal",{Readers:tG}),Treasury:eo("treasury",{InboundTransfers:e0,OutboundPayments:tp,OutboundTransfers:tv,ReceivedCredits:tI,ReceivedDebits:tM})}),nT=eo("treasury",{CreditReversals:eM,DebitReversals:eU,FinancialAccounts:eZ,InboundTransfers:e8,OutboundPayments:tm,OutboundTransfers:tg,ReceivedCredits:tD,ReceivedDebits:tq,TransactionEntries:ri,Transactions:rm}),n_=eo("v2",{Billing:eo("billing",{MeterEventAdjustments:e7,MeterEventSession:tt,MeterEventStream:to,MeterEvents:ts}),Core:eo("core",{EventDestinations:eK,Events:eJ})}),nE="api.stripe.com",nS="/v1/",nb="2025-03-31.basil",nO=["name","version","url","partner_id"],nx=["authenticator","apiVersion","typescript","maxNetworkRetries","httpAgent","httpClient","timeout","host","port","protocol","telemetry","appInfo","stripeAccount","stripeContext"],nw=e=>new K(e,et.MAX_BUFFERED_REQUEST_METRICS);var nA=function(e,t=nw){function r(n,i={}){if(!(this instanceof r))return new r(n,i);let a=this._getPropsFromConfig(i);this._platformFunctions=e,Object.defineProperty(this,"_emitter",{value:this._platformFunctions.createEmitter(),enumerable:!1,configurable:!1,writable:!1}),this.VERSION=r.PACKAGE_VERSION,this.on=this._emitter.on.bind(this._emitter),this.once=this._emitter.once.bind(this._emitter),this.off=this._emitter.removeListener.bind(this._emitter);let s=a.httpAgent||null;this._api={host:a.host||nE,port:a.port||"443",protocol:a.protocol||"https",basePath:nS,version:a.apiVersion||nb,timeout:$("timeout",a.timeout,8e4),maxNetworkRetries:$("maxNetworkRetries",a.maxNetworkRetries,2),agent:s,httpClient:a.httpClient||(s?this._platformFunctions.createNodeHttpClient(s):this._platformFunctions.createDefaultHttpClient()),dev:!1,stripeAccount:a.stripeAccount||null,stripeContext:a.stripeContext||null};let l=a.typescript||!1;l!==r.USER_AGENT.typescript&&(r.USER_AGENT.typescript=l),a.appInfo&&this._setAppInfo(a.appInfo),this._prepResources(),this._setAuthenticator(n,a.authenticator),this.errors=o,this.webhooks=r.webhooks,this._prevRequestMetrics=[],this._enableTelemetry=!1!==a.telemetry,this._requestSender=t(this),this.StripeResource=r.StripeResource}return r.PACKAGE_VERSION="18.0.0",r.USER_AGENT=Object.assign({bindings_version:r.PACKAGE_VERSION,lang:"node",publisher:"stripe",uname:null,typescript:!1},void 0===I?{}:{lang_version:I.version,platform:I.platform}),r.StripeResource=et,r.resources=n,r.HttpClient=i,r.HttpClientResponse=a,r.CryptoProvider=u,r.webhooks=function(e){let t={DEFAULT_TOLERANCE:300,signature:null,constructEvent(e,r,o,n,i,a){try{this.signature.verifyHeader(e,r,o,n||t.DEFAULT_TOLERANCE,i,a)}catch(e){throw e instanceof c&&(e.message+="\nUse `await constructEventAsync(...)` instead of `constructEvent(...)`"),e}return e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},async constructEventAsync(e,r,o,n,i,a){return await this.signature.verifyHeaderAsync(e,r,o,n||t.DEFAULT_TOLERANCE,i,a),e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},generateTestHeaderString:function(e){let t=l(e),r=t.signature||t.cryptoProvider.computeHMACSignature(t.payloadString,t.secret);return t.generateHeaderString(r)},generateTestHeaderStringAsync:async function(e){let t=l(e),r=t.signature||await t.cryptoProvider.computeHMACSignatureAsync(t.payloadString,t.secret);return t.generateHeaderString(r)}},r={EXPECTED_SCHEME:"v1",verifyHeader(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:d,details:h,suspectPayloadType:p}=n(e,t,this.EXPECTED_SCHEME),f=/\s/.test(r),m=(l=l||s()).computeHMACSignature(o(d,h),r);return i(d,c,h,m,a,p,f,u),!0},async verifyHeaderAsync(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:d,details:h,suspectPayloadType:p}=n(e,t,this.EXPECTED_SCHEME),f=/\s/.test(r);l=l||s();let m=await l.computeHMACSignatureAsync(o(d,h),r);return i(d,c,h,m,a,p,f,u)}};function o(e,t){return`${t.timestamp}.${e}`}function n(e,t,r){if(!e)throw new w(t,e,{message:"No webhook payload was provided."});let o="string"!=typeof e&&!(e instanceof Uint8Array),n=new TextDecoder("utf8"),i=e instanceof Uint8Array?n.decode(e):e;if(Array.isArray(t))throw Error("Unexpected: An array was passed as a header, which should not be possible for the stripe-signature header.");if(null==t||""==t)throw new w(t,e,{message:"No stripe-signature header value was provided."});let a=t instanceof Uint8Array?n.decode(t):t,s="string"!=typeof a?null:a.split(",").reduce((e,t)=>{let o=t.split("=");return"t"===o[0]&&(e.timestamp=parseInt(o[1],10)),o[0]===r&&e.signatures.push(o[1]),e},{timestamp:-1,signatures:[]});if(!s||-1===s.timestamp)throw new w(a,i,{message:"Unable to extract timestamp and signatures from header"});if(!s.signatures.length)throw new w(a,i,{message:"No signatures found with expected scheme"});return{decodedPayload:i,decodedHeader:a,details:s,suspectPayloadType:o}}function i(t,r,o,n,i,a,s,l){let u=!!o.signatures.filter(e.secureCompare.bind(e,n)).length,c="\nLearn more about webhook signing and explore webhook integration examples for various frameworks at https://docs.stripe.com/webhooks/signature",d=s?"\n\nNote: The provided signing secret contains whitespace. This often indicates an extra newline or space is in the value":"";if(!u){if(a)throw new w(r,t,{message:"Webhook payload must be provided as a string or a Buffer (https://nodejs.org/api/buffer.html) instance representing the _raw_ request body.Payload was provided as a parsed JavaScript object instead. \nSignature verification is impossible without access to the original signed material. \n"+c+"\n"+d});throw new w(r,t,{message:"No signatures found matching the expected signature for payload. Are you passing the raw request body you received from Stripe? \n If a webhook request is being forwarded by a third-party tool, ensure that the exact request body, including JSON formatting and new line style, is preserved.\n"+c+"\n"+d})}let h=Math.floor(("number"==typeof l?l:Date.now())/1e3)-o.timestamp;if(i>0&&h>i)throw new w(r,t,{message:"Timestamp outside the tolerance zone"});return!0}let a=null;function s(){return a||(a=e.createDefaultCryptoProvider()),a}function l(e){if(!e)throw new g({message:"Options are required"});let t=Math.floor(e.timestamp)||Math.floor(Date.now()/1e3),o=e.scheme||r.EXPECTED_SCHEME,n=e.cryptoProvider||s(),i=`${t}.${e.payload}`;return Object.assign(Object.assign({},e),{timestamp:t,scheme:o,cryptoProvider:n,payloadString:i,generateHeaderString:e=>`t=${t},${o}=${e}`})}return t.signature=r,t}(e),r.errors=o,r.createNodeHttpClient=e.createNodeHttpClient,r.createFetchHttpClient=e.createFetchHttpClient,r.createNodeCryptoProvider=e.createNodeCryptoProvider,r.createSubtleCryptoProvider=e.createSubtleCryptoProvider,r.prototype={_appInfo:void 0,on:null,off:null,once:null,VERSION:null,StripeResource:null,webhooks:null,errors:null,_api:null,_prevRequestMetrics:null,_emitter:null,_enableTelemetry:null,_requestSender:null,_platformFunctions:null,rawRequest(e,t,r,o){return this._requestSender._rawRequest(e,t,r,o)},_setAuthenticator(e,t){if(e&&t)throw Error("Can't specify both apiKey and authenticator");if(!e&&!t)throw Error("Neither apiKey nor config.authenticator provided");this._authenticator=e?z(e):t},_setAppInfo(e){if(e&&"object"!=typeof e)throw Error("AppInfo must be an object.");if(e&&!e.name)throw Error("AppInfo.name is required");e=e||{},this._appInfo=nO.reduce((t,r)=>("string"==typeof e[r]&&((t=t||{})[r]=e[r]),t),void 0)},_setApiField(e,t){this._api[e]=t},getApiField(e){return this._api[e]},setClientId(e){this._clientId=e},getClientId(){return this._clientId},getConstant:e=>{switch(e){case"DEFAULT_HOST":return nE;case"DEFAULT_PORT":return"443";case"DEFAULT_BASE_PATH":return nS;case"DEFAULT_API_VERSION":return nb;case"DEFAULT_TIMEOUT":return 8e4;case"MAX_NETWORK_RETRY_DELAY_SEC":return 5;case"INITIAL_NETWORK_RETRY_DELAY_SEC":return .5}return r[e]},getMaxNetworkRetries(){return this.getApiField("maxNetworkRetries")},_setApiNumberField(e,t,r){let o=$(e,t,r);this._setApiField(e,o)},getMaxNetworkRetryDelay:()=>5,getInitialNetworkRetryDelay:()=>.5,getClientUserAgent(e){return this.getClientUserAgentSeeded(r.USER_AGENT,e)},getClientUserAgentSeeded(e,t){this._platformFunctions.getUname().then(r=>{var o;let n={};for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=encodeURIComponent(null!==(o=e[t])&&void 0!==o?o:"null"));n.uname=encodeURIComponent(r||"UNKNOWN");let i=this.getApiField("httpClient");i&&(n.httplib=encodeURIComponent(i.getClientName())),this._appInfo&&(n.application=this._appInfo),t(JSON.stringify(n))})},getAppInfoAsString(){if(!this._appInfo)return"";let e=this._appInfo.name;return this._appInfo.version&&(e+=`/${this._appInfo.version}`),this._appInfo.url&&(e+=` (${this._appInfo.url})`),e},getTelemetryEnabled(){return this._enableTelemetry},_prepResources(){for(let e in n)Object.prototype.hasOwnProperty.call(n,e)&&(this["OAuth"===e?"oauth":e[0].toLowerCase()+e.substring(1)]=new n[e](this))},_getPropsFromConfig(e){if(!e)return{};let t="string"==typeof e;if(!(e===Object(e)&&!Array.isArray(e))&&!t)throw Error("Config must either be an object or a string");if(t)return{apiVersion:e};if(Object.keys(e).filter(e=>!nx.includes(e)).length>0)throw Error(`Config object may only contain the following: ${nx.join(", ")}`);return e},parseThinEvent(e,t,r,o,n,i){return this.webhooks.constructEvent(e,t,r,o,n,i)}},r}(new y)}}]);