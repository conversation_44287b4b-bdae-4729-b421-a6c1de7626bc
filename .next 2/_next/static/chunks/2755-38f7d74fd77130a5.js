(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2755],{72755:function(e,a,t){"use strict";t.r(a),t.d(a,{FourSellerIntegration:function(){return o},MarketplaceIntegration:function(){return r}});var s=t(85893),c=t(67294);t(25675),t(41664);var n=t(5162),l=t.n(n),r=()=>{let[e,a]=(0,c.useState)("dashboard"),[t,n]=(0,c.useState)(!1),[r,o]=(0,c.useState)(!1),[i,d]=(0,c.useState)(!1),m=e=>{switch(e){case"ebay":n(!t);break;case"amazon":o(!r);break;case"tiktok":d(!i)}};return(0,s.jsxs)("div",{className:l().marketplace<PERSON>ontaine<PERSON>,children:[(0,s.jsx)("h2",{className:l().marketplaceT<PERSON><PERSON>,children:"Marketplace Integrations"}),(0,s.jsxs)("div",{className:l().marketplaceTabs,children:[(0,s.jsx)("button",{className:"".concat(l().tabButton," ").concat("dashboard"===e?l().activeTab:""),onClick:()=>a("dashboard"),children:"Dashboard"}),(0,s.jsx)("button",{className:"".concat(l().tabButton," ").concat("orders"===e?l().activeTab:""),onClick:()=>a("orders"),children:"Orders"}),(0,s.jsx)("button",{className:"".concat(l().tabButton," ").concat("inventory"===e?l().activeTab:""),onClick:()=>a("inventory"),children:"Inventory"}),(0,s.jsx)("button",{className:"".concat(l().tabButton," ").concat("settings"===e?l().activeTab:""),onClick:()=>a("settings"),children:"Settings"})]}),(0,s.jsxs)("div",{className:l().marketplaceContent,children:["dashboard"===e&&(0,s.jsxs)("div",{className:l().marketplaceDashboard,children:[(0,s.jsxs)("div",{className:l().marketplaceCards,children:[(0,s.jsxs)("div",{className:l().marketplaceCard,children:[(0,s.jsxs)("div",{className:l().marketplaceCardHeader,children:[(0,s.jsx)("img",{src:"/images/ebay-logo.png",alt:"eBay",className:l().marketplaceLogo}),(0,s.jsx)("h3",{children:"eBay"})]}),(0,s.jsxs)("div",{className:l().marketplaceCardBody,children:[(0,s.jsxs)("div",{className:l().connectionStatus,children:[(0,s.jsx)("span",{className:"".concat(l().statusIndicator," ").concat(t?l().connected:l().disconnected)}),(0,s.jsx)("span",{children:t?"Connected":"Disconnected"})]}),t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:l().marketplaceStats,children:[(0,s.jsxs)("div",{className:l().statItem,children:[(0,s.jsx)("span",{className:l().statLabel,children:"Active Listings"}),(0,s.jsx)("span",{className:l().statValue,children:"24"})]}),(0,s.jsxs)("div",{className:l().statItem,children:[(0,s.jsx)("span",{className:l().statLabel,children:"Pending Orders"}),(0,s.jsx)("span",{className:l().statValue,children:"3"})]}),(0,s.jsxs)("div",{className:l().statItem,children:[(0,s.jsx)("span",{className:l().statLabel,children:"Total Sales"}),(0,s.jsx)("span",{className:l().statValue,children:"$1,245.67"})]})]}),(0,s.jsx)("button",{className:l().marketplaceButton,onClick:()=>m("ebay"),children:"Disconnect"})]}):(0,s.jsx)("button",{className:l().marketplaceButton,onClick:()=>m("ebay"),children:"Connect eBay Account"})]})]}),(0,s.jsxs)("div",{className:l().marketplaceCard,children:[(0,s.jsxs)("div",{className:l().marketplaceCardHeader,children:[(0,s.jsx)("img",{src:"/images/amazon-logo.png",alt:"Amazon",className:l().marketplaceLogo}),(0,s.jsx)("h3",{children:"Amazon"})]}),(0,s.jsxs)("div",{className:l().marketplaceCardBody,children:[(0,s.jsxs)("div",{className:l().connectionStatus,children:[(0,s.jsx)("span",{className:"".concat(l().statusIndicator," ").concat(r?l().connected:l().disconnected)}),(0,s.jsx)("span",{children:r?"Connected":"Disconnected"})]}),r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:l().marketplaceStats,children:[(0,s.jsxs)("div",{className:l().statItem,children:[(0,s.jsx)("span",{className:l().statLabel,children:"Active Listings"}),(0,s.jsx)("span",{className:l().statValue,children:"18"})]}),(0,s.jsxs)("div",{className:l().statItem,children:[(0,s.jsx)("span",{className:l().statLabel,children:"Pending Orders"}),(0,s.jsx)("span",{className:l().statValue,children:"5"})]}),(0,s.jsxs)("div",{className:l().statItem,children:[(0,s.jsx)("span",{className:l().statLabel,children:"Total Sales"}),(0,s.jsx)("span",{className:l().statValue,children:"$2,890.45"})]})]}),(0,s.jsx)("button",{className:l().marketplaceButton,onClick:()=>m("amazon"),children:"Disconnect"})]}):(0,s.jsx)("button",{className:l().marketplaceButton,onClick:()=>m("amazon"),children:"Connect Amazon Account"})]})]}),(0,s.jsxs)("div",{className:l().marketplaceCard,children:[(0,s.jsxs)("div",{className:l().marketplaceCardHeader,children:[(0,s.jsx)("img",{src:"/images/tiktok-logo.png",alt:"TikTok Shop",className:l().marketplaceLogo}),(0,s.jsx)("h3",{children:"TikTok Shop"})]}),(0,s.jsxs)("div",{className:l().marketplaceCardBody,children:[(0,s.jsxs)("div",{className:l().connectionStatus,children:[(0,s.jsx)("span",{className:"".concat(l().statusIndicator," ").concat(i?l().connected:l().disconnected)}),(0,s.jsx)("span",{children:i?"Connected":"Disconnected"})]}),i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:l().marketplaceStats,children:[(0,s.jsxs)("div",{className:l().statItem,children:[(0,s.jsx)("span",{className:l().statLabel,children:"Active Listings"}),(0,s.jsx)("span",{className:l().statValue,children:"12"})]}),(0,s.jsxs)("div",{className:l().statItem,children:[(0,s.jsx)("span",{className:l().statLabel,children:"Pending Orders"}),(0,s.jsx)("span",{className:l().statValue,children:"7"})]}),(0,s.jsxs)("div",{className:l().statItem,children:[(0,s.jsx)("span",{className:l().statLabel,children:"Total Sales"}),(0,s.jsx)("span",{className:l().statValue,children:"$945.20"})]})]}),(0,s.jsx)("button",{className:l().marketplaceButton,onClick:()=>m("tiktok"),children:"Disconnect"})]}):(0,s.jsx)("button",{className:l().marketplaceButton,onClick:()=>m("tiktok"),children:"Connect TikTok Shop"})]})]})]}),(0,s.jsxs)("div",{className:l().marketplaceSummary,children:[(0,s.jsx)("h3",{children:"Marketplace Summary"}),(0,s.jsxs)("div",{className:l().summaryStats,children:[(0,s.jsxs)("div",{className:l().summaryStatItem,children:[(0,s.jsx)("span",{className:l().summaryStatLabel,children:"Total Active Listings"}),(0,s.jsx)("span",{className:l().summaryStatValue,children:(t?24:0)+(r?18:0)+(i?12:0)})]}),(0,s.jsxs)("div",{className:l().summaryStatItem,children:[(0,s.jsx)("span",{className:l().summaryStatLabel,children:"Total Pending Orders"}),(0,s.jsx)("span",{className:l().summaryStatValue,children:(t?3:0)+(r?5:0)+(i?7:0)})]}),(0,s.jsxs)("div",{className:l().summaryStatItem,children:[(0,s.jsx)("span",{className:l().summaryStatLabel,children:"Total Sales"}),(0,s.jsxs)("span",{className:l().summaryStatValue,children:["$",((t?1245.67:0)+(r?2890.45:0)+(i?945.2:0)).toFixed(2)]})]})]})]})]}),"orders"===e&&(0,s.jsxs)("div",{className:l().marketplaceOrders,children:[(0,s.jsx)("h3",{children:"Marketplace Orders"}),(0,s.jsx)("p",{className:l().comingSoon,children:"Order management coming soon!"})]}),"inventory"===e&&(0,s.jsxs)("div",{className:l().marketplaceInventory,children:[(0,s.jsx)("h3",{children:"Inventory Management"}),(0,s.jsx)("p",{className:l().comingSoon,children:"Inventory management coming soon!"})]}),"settings"===e&&(0,s.jsxs)("div",{className:l().marketplaceSettings,children:[(0,s.jsx)("h3",{children:"Integration Settings"}),(0,s.jsx)("p",{className:l().comingSoon,children:"Integration settings coming soon!"})]})]})]})},o=()=>{let[e,a]=(0,c.useState)(!1),[t,n]=(0,c.useState)(""),[r,o]=(0,c.useState)(""),[i,d]=(0,c.useState)(!1),[m,u]=(0,c.useState)(null),[p,h]=(0,c.useState)({totalItems:0,inStock:0,lowStock:0,outOfStock:0});return(0,s.jsxs)("div",{className:l().fourSellerContainer,children:[(0,s.jsxs)("div",{className:l().fourSellerHeader,children:[(0,s.jsxs)("div",{className:l().fourSellerLogo,children:[(0,s.jsx)("img",{src:"/images/4seller-logo.png",alt:"4seller",className:l().fourSellerLogoImg}),(0,s.jsx)("h3",{children:"4seller Integration"})]}),(0,s.jsxs)("div",{className:l().connectionStatus,children:[(0,s.jsx)("span",{className:"".concat(l().statusIndicator," ").concat(e?l().connected:l().disconnected)}),(0,s.jsx)("span",{children:e?"Connected":"Disconnected"})]})]}),(0,s.jsx)("div",{className:l().fourSellerContent,children:e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:l().syncInfo,children:[(0,s.jsxs)("div",{className:l().lastSync,children:[(0,s.jsx)("span",{className:l().syncLabel,children:"Last Synchronized:"}),(0,s.jsx)("span",{className:l().syncValue,children:m?new Date(m).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Never"})]}),(0,s.jsx)("button",{className:l().syncButton,onClick:()=>{d(!0),setTimeout(()=>{u(new Date().toISOString()),h({totalItems:1245,inStock:987,lowStock:158,outOfStock:100}),d(!1)},2e3)},disabled:i,children:i?"Syncing...":"Sync Now"})]}),(0,s.jsxs)("div",{className:l().inventoryStats,children:[(0,s.jsxs)("div",{className:l().statCard,children:[(0,s.jsx)("div",{className:l().statValue,children:p.totalItems}),(0,s.jsx)("div",{className:l().statLabel,children:"Total Items"})]}),(0,s.jsxs)("div",{className:l().statCard,children:[(0,s.jsx)("div",{className:l().statValue,children:p.inStock}),(0,s.jsx)("div",{className:l().statLabel,children:"In Stock"})]}),(0,s.jsxs)("div",{className:l().statCard,children:[(0,s.jsx)("div",{className:l().statValue,children:p.lowStock}),(0,s.jsx)("div",{className:l().statLabel,children:"Low Stock"})]}),(0,s.jsxs)("div",{className:l().statCard,children:[(0,s.jsx)("div",{className:l().statValue,children:p.outOfStock}),(0,s.jsx)("div",{className:l().statLabel,children:"Out of Stock"})]})]}),(0,s.jsxs)("div",{className:l().actionButtons,children:[(0,s.jsx)("button",{className:l().viewInventoryButton,children:"View Inventory"}),(0,s.jsx)("button",{className:l().disconnectButton,onClick:()=>{a(!1),n(""),o(""),u(null),h({totalItems:0,inStock:0,lowStock:0,outOfStock:0})},children:"Disconnect"})]})]}):(0,s.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!t||!r){alert("Please enter both API Key and Store ID");return}d(!0),setTimeout(()=>{a(!0),u(new Date().toISOString()),h({totalItems:1245,inStock:987,lowStock:158,outOfStock:100}),d(!1)},1500)},className:l().connectionForm,children:[(0,s.jsxs)("div",{className:l().formGroup,children:[(0,s.jsx)("label",{htmlFor:"apiKey",children:"API Key"}),(0,s.jsx)("input",{type:"password",id:"apiKey",value:t,onChange:e=>n(e.target.value),placeholder:"Enter your 4seller API Key",required:!0})]}),(0,s.jsxs)("div",{className:l().formGroup,children:[(0,s.jsx)("label",{htmlFor:"storeId",children:"Store ID"}),(0,s.jsx)("input",{type:"text",id:"storeId",value:r,onChange:e=>o(e.target.value),placeholder:"Enter your 4seller Store ID",required:!0})]}),(0,s.jsx)("button",{type:"submit",className:l().connectButton,disabled:i,children:i?"Connecting...":"Connect to 4seller"})]})})]})}},5162:function(e){e.exports={marketplaceContainer:"Marketplace_marketplaceContainer__r9G6B",marketplaceTitle:"Marketplace_marketplaceTitle__712Bv",marketplaceTabs:"Marketplace_marketplaceTabs__N48DM",tabButton:"Marketplace_tabButton__c89F7",activeTab:"Marketplace_activeTab__KQa5_",marketplaceContent:"Marketplace_marketplaceContent__fDkk2",marketplaceCards:"Marketplace_marketplaceCards__HIFUJ",marketplaceCard:"Marketplace_marketplaceCard__AhxXi",marketplaceCardHeader:"Marketplace_marketplaceCardHeader__PAoZU",marketplaceLogo:"Marketplace_marketplaceLogo__tSh6y",marketplaceCardBody:"Marketplace_marketplaceCardBody__EjOE3",connectionStatus:"Marketplace_connectionStatus__JyVe5",statusIndicator:"Marketplace_statusIndicator__GfwvD",connected:"Marketplace_connected__OaPpT",disconnected:"Marketplace_disconnected__TC1n2",marketplaceStats:"Marketplace_marketplaceStats__972TQ",statItem:"Marketplace_statItem__FGa5u",statLabel:"Marketplace_statLabel__XkVtv",statValue:"Marketplace_statValue__Cxws9",marketplaceButton:"Marketplace_marketplaceButton__3Otc7",marketplaceSummary:"Marketplace_marketplaceSummary__t_DiN",summaryStats:"Marketplace_summaryStats__q_KH8",summaryStatItem:"Marketplace_summaryStatItem___uO4C",summaryStatLabel:"Marketplace_summaryStatLabel__bwtkG",summaryStatValue:"Marketplace_summaryStatValue__oUQ_F",comingSoon:"Marketplace_comingSoon__UVkGq",fourSellerContainer:"Marketplace_fourSellerContainer__DhDP5",fourSellerHeader:"Marketplace_fourSellerHeader__v7yGp",fourSellerLogo:"Marketplace_fourSellerLogo__UyaCc",fourSellerLogoImg:"Marketplace_fourSellerLogoImg__vfnqF",fourSellerContent:"Marketplace_fourSellerContent__IelB9",connectionForm:"Marketplace_connectionForm____SzN",formGroup:"Marketplace_formGroup__u2efu",connectButton:"Marketplace_connectButton__lw0w7",syncInfo:"Marketplace_syncInfo__SyCMX",lastSync:"Marketplace_lastSync__sDcuS",syncLabel:"Marketplace_syncLabel___d98D",syncValue:"Marketplace_syncValue__gUoVW",syncButton:"Marketplace_syncButton__JFv5P",inventoryStats:"Marketplace_inventoryStats__1IX_P",statCard:"Marketplace_statCard__8fQy7",actionButtons:"Marketplace_actionButtons__U07PU",viewInventoryButton:"Marketplace_viewInventoryButton__jBAxN",disconnectButton:"Marketplace_disconnectButton__bFC46"}}}]);