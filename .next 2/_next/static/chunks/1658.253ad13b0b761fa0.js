(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1658],{91658:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return _}});var c=a(85893),s=a(67294);a(25675);var i=a(41664),r=a.n(i),o=a(16013),n=a.n(o),d=t=>{let{initialValue:e=1,min:a=1,max:i=99,onChange:r}=t,[o,d]=(0,s.useState)(e);return(0,c.jsxs)("div",{className:n().quantitySelector,children:[(0,c.jsx)("button",{className:n().button,onClick:()=>{if(o>a){let t=o-1;d(t),r&&r(t)}},disabled:o<=a,children:"-"}),(0,c.jsx)("input",{type:"text",value:o,onChange:t=>{let e=parseInt(t.target.value,10);!isNaN(e)&&e>=a&&e<=i&&(d(e),r&&r(e))},className:n().input}),(0,c.jsx)("button",{className:n().button,onClick:()=>{if(o<i){let t=o+1;d(t),r&&r(t)}},disabled:o>=i,children:"+"})]})},l=a(44464),u=a.n(l),_=t=>{let{products:e,title:a="Popular Products"}=t,[i,o]=(0,s.useState)(e.reduce((t,e)=>(t[e.id]=1,t),{})),n=(t,e)=>{o(a=>({...a,[t]:e}))},l=async t=>{try{if(!(await fetch("/api/cart",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:t,quantity:i[t]})})).ok)throw Error("Failed to add to cart");alert("Successfully added ".concat(i[t]," of product ID ").concat(t," to cart"))}catch(t){console.error("Error adding to cart:",t),alert("Failed to add to cart. Please try again.")}};return(0,c.jsx)("section",{className:u().productList,children:(0,c.jsxs)("div",{className:u().container,children:[(0,c.jsx)("h2",{className:u().title,children:a}),(0,c.jsx)("p",{className:u().subtitle,children:"Browse our selection of high-quality repair parts and tools"}),(0,c.jsxs)("div",{className:u().categories,children:[(0,c.jsx)("button",{className:"".concat(u().categoryButton," ").concat(u().active),children:"All"}),(0,c.jsx)("button",{className:u().categoryButton,children:"iPhone Parts"}),(0,c.jsx)("button",{className:u().categoryButton,children:"Samsung Parts"}),(0,c.jsx)("button",{className:u().categoryButton,children:"iPad Parts"}),(0,c.jsx)("button",{className:u().categoryButton,children:"Tools"})]}),(0,c.jsx)("div",{className:u().products,children:e.map(t=>(0,c.jsxs)("div",{className:u().product,children:[(0,c.jsx)("div",{className:u().imageContainer,children:(0,c.jsx)("img",{src:t.imageUrl||"/images/placeholder.png",alt:t.name,className:u().image})}),(0,c.jsxs)("div",{className:u().content,children:[(0,c.jsx)("div",{className:u().category,children:t.category}),(0,c.jsx)("h3",{className:u().name,children:t.name}),(0,c.jsx)("div",{className:u().price,children:t.discount_percentage>0?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)("span",{className:u().originalPrice,children:["$",(t.price/(1-t.discount_percentage/100)).toFixed(2)]}),(0,c.jsxs)("span",{className:u().salePrice,children:["$",t.price.toFixed(2)]})]}):(0,c.jsxs)("span",{children:["$",t.price.toFixed(2)]})}),(0,c.jsx)(d,{initialValue:i[t.id],onChange:e=>n(t.id,e)}),(0,c.jsx)("button",{className:u().addToCart,onClick:()=>l(t.id),children:"Add to Cart"})]})]},t.id))}),(0,c.jsx)("div",{className:u().viewMore,children:(0,c.jsx)(r(),{href:"/products",className:u().viewMoreLink,children:"View More Products"})})]})})}},44464:function(t){t.exports={productList:"ProductList_productList__e7F09",container:"ProductList_container__Y8Ikq",title:"ProductList_title__5nv2l",subtitle:"ProductList_subtitle__6qBiW",categories:"ProductList_categories__YKbrV",categoryButton:"ProductList_categoryButton__t5avI",active:"ProductList_active__lyTi_",products:"ProductList_products__COjlj",product:"ProductList_product__R4OD6",imageContainer:"ProductList_imageContainer__PW2P0",image:"ProductList_image__z3N2X",content:"ProductList_content__4Fca5",category:"ProductList_category__BcTtk",name:"ProductList_name__YIS3S",price:"ProductList_price__M48zy",addToCart:"ProductList_addToCart__JY4jE",viewMore:"ProductList_viewMore__qSnKY",viewMoreLink:"ProductList_viewMoreLink__0D9bK"}},16013:function(t){t.exports={quantitySelector:"QuantitySelector_quantitySelector__quwG9",button:"QuantitySelector_button__f7P5N",input:"QuantitySelector_input__za5C8"}}}]);