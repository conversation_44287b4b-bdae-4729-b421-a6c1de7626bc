/* Hero section styles */
.hero {
  position: relative;
  height: 500px;
  background-color: #f0f5ff;
  overflow: hidden;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  height: 100%;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-image: url('/images/backgrounds/hero-bg.jpg');
  background-size: cover;
  background-position: center;
  opacity: 0.2;
}

.hero-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: #0066cc;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  max-width: 600px;
  color: #333;
}

.hero-cta {
  display: inline-block;
  background-color: #0066cc;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 30px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.hero-cta:hover {
  background-color: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 102, 204, 0.3);
}

/* Featured categories section */
.featured-categories {
  padding: 60px 0;
}

.section-title {
  text-align: center;
  margin-bottom: 40px;
  font-size: 2rem;
  font-weight: 700;
  color: #333;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.category-card {
  position: relative;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.category-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-card:hover .category-image {
  transform: scale(1.05);
}

.category-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 15px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
}

.category-name {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

/* Featured products section */
.featured-products {
  padding: 60px 0;
  background-color: #f9fafb;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.view-all-link {
  display: block;
  text-align: center;
  margin-top: 40px;
  color: #0066cc;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.2s ease;
}

.view-all-link:hover {
  color: #0056b3;
}

/* Testimonials section */
.testimonials {
  padding: 60px 0;
}

.testimonials-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.testimonial-card {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.testimonial-content {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 20px;
  color: #333;
}

.testimonial-author {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-info h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

.author-info p {
  margin: 0;
  font-size: 0.875rem;
  color: #666;
}

/* Newsletter section */
.newsletter {
  padding: 60px 0;
  background-color: #f0f5ff;
}

.newsletter-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.newsletter-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: #333;
}

.newsletter-description {
  margin-bottom: 30px;
  color: #666;
}

.newsletter-form {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
}

.newsletter-input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #d1d5db;
  border-right: none;
  border-radius: 4px 0 0 4px;
  font-size: 1rem;
}

.newsletter-input:focus {
  outline: none;
  border-color: #0066cc;
}

.newsletter-button {
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 0 20px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.newsletter-button:hover {
  background-color: #0056b3;
}

/* Responsive styles */
@media (max-width: 768px) {
  .hero {
    height: 400px;
  }
  
  .hero-content {
    padding: 60px 20px;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
  }
  
  .categories-grid,
  .products-grid,
  .testimonials-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
  
  .newsletter-form {
    flex-direction: column;
  }
  
  .newsletter-input {
    border-right: 1px solid #d1d5db;
    border-radius: 4px;
    margin-bottom: 10px;
  }
  
  .newsletter-button {
    border-radius: 4px;
    padding: 12px 20px;
  }
}
