/* CSS Fixes for Glitching Issues */

/* Fix for overlapping UI elements */
.chatbotContainer {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 900; /* Reduced z-index */
}

.whatsappContainer {
  position: fixed;
  bottom: 20px;
  right: 90px;
  z-index: 900; /* Reduced z-index */
}

.accessibilityButton {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 900; /* Reduced z-index */
}

.notificationsContainer {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 900; /* Reduced z-index */
}

/* Fix for modal overlay */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000; /* Consistent z-index */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Fix for double headers */
header + header {
  display: none !important;
}

/* Fix for layout issues */
body {
  overflow-x: hidden;
  width: 100%;
  position: relative;
}

/* Fix for flickering elements */
* {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Fix for cart page layout */
.cart_container {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 30px;
}

@media (max-width: 991px) {
  .cart_container {
    grid-template-columns: 1fr;
  }
}

/* Fix for footer spacing */
.footer {
  margin-top: 2rem;
}

/* Fix for menu bar positioning */
.menu-bar {
  justify-content: flex-start !important;
  padding-left: 1rem;
}

/* Fix for dropdown menu items */
.dropdown-menu a {
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  clear: both;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}

/* Fix for header navigation spacing */
.nav-links {
  gap: 1.5rem !important;
}

/* Fix for product price display */
.product .price span:nth-child(2) {
  display: none !important;
}

/* Fix for layout container */
.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

/* Fix for rendering performance */
* {
  transform: translateZ(0);
}

/* Fix for transition glitches */
.transition-fix {
  transition: none !important;
}

/* Fix for Safari rendering issues */
@media not all and (min-resolution:.001dpcm) { 
  @supports (-webkit-appearance:none) {
    .safari-fix {
      -webkit-transform: translateZ(0);
    }
  }
}
