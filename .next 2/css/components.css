/* Header styles */
.header {
  background-color: #0066cc;
  color: white;
  padding: 15px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.logo {
  font-size: 24px;
  font-weight: 700;
}

.logo a {
  color: white;
  text-decoration: none;
}

.search-bar {
  flex: 1;
  max-width: 400px;
}

.search-bar form {
  display: flex;
  position: relative;
}

.search-bar input {
  width: 100%;
  padding: 10px 40px 10px 15px;
  border: none;
  border-radius: 30px;
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  font-size: 14px;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.search-bar input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-bar input:focus {
  outline: none;
  background-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.search-bar button {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navigation {
  display: flex;
  gap: 20px;
}

.navigation a {
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: opacity 0.2s ease;
}

.navigation a:hover {
  opacity: 0.8;
}

.sign-in-link {
  padding: 6px 12px;
  border: 1px solid white;
  border-radius: 4px;
}

.sign-in-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Footer styles */
.footer {
  background-color: #1a1a1a;
  color: #f5f5f5;
  padding: 40px 0 20px;
  margin-top: 60px;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-top {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.footer-column h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: white;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: #b3b3b3;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-links a:hover {
  color: white;
}

.footer-bottom {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #333;
}

.footer-copyright {
  color: #b3b3b3;
  font-size: 14px;
}

.footer-social {
  display: flex;
  gap: 15px;
}

.footer-social a {
  color: #b3b3b3;
  font-size: 20px;
  transition: color 0.2s ease;
}

.footer-social a:hover {
  color: white;
}

/* Product card styles */
.product-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.product-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: #ff3e3e;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.product-details {
  padding: 15px;
}

.product-category {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
}

.original-price {
  text-decoration: line-through;
  color: #999;
  font-size: 14px;
}

.current-price {
  font-size: 18px;
  font-weight: 600;
  color: #0066cc;
}

.product-actions {
  display: flex;
  justify-content: space-between;
}

.add-to-cart {
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-to-cart:hover {
  background-color: #0056b3;
}

.wishlist-button {
  background: none;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 8px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.wishlist-button:hover {
  background-color: #f3f4f6;
  color: #0066cc;
}

.wishlist-button.active {
  color: #ff3e3e;
  border-color: #ff3e3e;
}

/* Responsive styles */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .logo {
    margin-bottom: 10px;
  }
  
  .search-bar {
    max-width: 100%;
    margin-bottom: 10px;
  }
  
  .navigation {
    justify-content: space-between;
  }
  
  .footer-top {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 15px;
  }
}
