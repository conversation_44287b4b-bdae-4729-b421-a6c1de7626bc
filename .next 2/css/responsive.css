/* Responsive CSS for Phone Electronics Store */

/* Fix for overlapping UI elements */
.chatbotContainer {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 900;
  /* Reduced z-index */
}

.whatsappContainer {
  position: fixed;
  bottom: 20px;
  right: 90px;
  z-index: 900;
  /* Reduced z-index */
}

.accessibilityButton {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 900;
  /* Reduced z-index */
}

.notificationsContainer {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 900;
  /* Reduced z-index */
}

/* Fix for modal overlay */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  /* Consistent z-index */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Fix for double headers */
header+header {
  display: none !important;
}

/* Fix for flickering elements */
* {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Fix for cart page layout */
.cart_container {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 30px;
}

@media (max-width: 991px) {
  .cart_container {
    grid-template-columns: 1fr;
  }
}

/* Fix for menu bar positioning */
.menu-bar {
  justify-content: flex-start !important;
  padding-left: 1rem;
}

/* Fix for dropdown menu items */
.dropdown-menu a {
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  clear: both;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}

/* Fix for header navigation spacing */
.nav-links {
  gap: 1.5rem !important;
}

/* Fix for product price display */
.product .price span:nth-child(2) {
  display: none !important;
}

/* Fix for rendering performance */
* {
  transform: translateZ(0);
}

/* Fix for transition glitches */
.transition-fix {
  transition: none !important;
}

/* Fix for Safari rendering issues */
@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance:none) {
    .safari-fix {
      -webkit-transform: translateZ(0);
    }
  }
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Header */
.header {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  text-decoration: none;
  white-space: nowrap;
}

.search-form {
  flex: 1;
  max-width: 500px;
}

.search-form form {
  display: flex;
  position: relative;
}

.search-form input {
  width: 100%;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.875rem;
}

.search-form button {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  background: none;
  border: none;
  padding: 0 0.75rem;
  color: #666;
  cursor: pointer;
}

.search-form button:hover {
  color: var(--primary-color);
}

.nav-links {
  display: flex;
  gap: 1.5rem;
}

.nav-links a {
  color: #333;
  text-decoration: none;
  transition: color 0.2s;
}

.nav-links a:hover {
  color: var(--primary-color);
}

.mobile-menu-button {
  display: none;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-color);
}

@media (max-width: 767px) {
  .mobile-menu-button {
    display: flex;
  }
}

.header-left {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: var(--shadow-md);
  z-index: var(--z-dropdown);
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--border-color);
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.mobile-menu.open {
  display: block;
  max-height: 100vh;
}

.mobile-search {
  margin: var(--spacing-md);
  width: auto;
  max-width: 100%;
}

.mobile-nav {
  display: flex;
  flex-direction: column;
}

.mobile-nav-item {
  display: block;
  padding: var(--spacing-md);
  color: var(--text-color);
  text-decoration: none;
  border-bottom: 1px solid var(--border-color);
  font-size: var(--font-size-base);
  transition: background-color 0.2s;
}

.mobile-nav-item:hover {
  background-color: var(--secondary-color);
  text-decoration: none;
}

.mobile-nav-item.auth-link {
  color: var(--primary-color);
  font-weight: 500;
}

.mobile-nav-item.sign-out-button {
  background: none;
  border: none;
  text-align: left;
  width: 100%;
  cursor: pointer;
  color: var(--error-color);
}

/* Main content */
.main {
  min-height: calc(100vh - 140px);
  padding: 2rem 0;
}

/* Footer */
.footer {
  background-color: white;
  padding: 1.5rem 0;
  color: #666;
  margin-top: 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
  text-align: center;
}

.footer-section h3 {
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-lg);
}

.footer-nav {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer-nav a {
  color: var(--text-muted);
  text-decoration: none;
}

.footer-nav a:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.footer-bottom {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  text-align: center;
}

@media (min-width: 640px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    text-align: left;
  }
}

@media (min-width: 1024px) {
  .footer-content {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Grid layout */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

/* Cards */
.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease;
}

.card:hover {
  transform: translateY(-5px);
}

.card-image {
  height: 200px;
  background-color: #f0f0f0;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-content {
  padding: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.card-category {
  color: #666;
  margin-bottom: 0.5rem;
}

.card-price {
  font-weight: bold;
  font-size: 1.125rem;
  margin-bottom: 1rem;
}

/* Product detail */
.product-detail {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.product-grid {
  display: grid;
  grid-template-columns: 1fr;
}

.product-image {
  height: 300px;
  background-color: #f0f0f0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 1.5rem;
}

.product-price {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.discount-badge {
  display: inline-block;
  background-color: #fee2e2;
  color: #991b1b;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  margin-left: 0.5rem;
}

.specs-box {
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 1.5rem;
}

.spec-item {
  margin-bottom: 0.5rem;
}

.spec-label {
  font-weight: 500;
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 112, 243, 0.25);
}

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.form-check-input {
  margin-right: 0.5rem;
}

/* Buttons */
.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  cursor: pointer;
}

.btn-primary {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-success {
  color: #fff;
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-success:hover {
  background-color: #0e9f6e;
  border-color: #0e9f6e;
}

.btn-danger {
  color: #fff;
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-danger:hover {
  background-color: #dc2626;
  border-color: #dc2626;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

/* Alerts */
.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-success {
  color: #0f5132;
  background-color: #d1e7dd;
  border-color: #badbcc;
}

.alert-danger {
  color: #842029;
  background-color: #f8d7da;
  border-color: #f5c2c7;
}

.alert-warning {
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffecb5;
}

.alert-info {
  color: #055160;
  background-color: #cff4fc;
  border-color: #b6effb;
}

/* Responsive styles */
@media (min-width: 768px) {
  .product-grid {
    grid-template-columns: 1fr 1fr;
  }

  .product-image {
    height: 400px;
  }
}

/* Responsive breakpoints */
@media (max-width: 1023px) {
  .product-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 767px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }

  .header-content {
    flex-wrap: wrap;
  }

  .header-left {
    width: 100%;
  }

  .logo {
    flex: 1;
  }

  .search-form {
    order: 3;
    flex: 0 0 100%;
    max-width: 100%;
    margin-top: 0.5rem;
  }

  .desktop-nav {
    display: none !important;
  }

  /* Cart and checkout responsiveness */
  .cart-container {
    grid-template-columns: 1fr;
  }

  .cart-summary {
    order: -1;
    margin-bottom: var(--spacing-lg);
  }

  .checkout-container {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  /* Product details responsiveness */
  .product-details {
    flex-direction: column;
  }

  .product-image {
    width: 100%;
    margin-right: 0;
    margin-bottom: var(--spacing-lg);
  }

  .product-info {
    width: 100%;
  }
}

@media (max-width: 639px) {
  .grid {
    grid-template-columns: 1fr;
  }

  .card-image {
    height: 180px;
  }

  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  .product-price {
    font-size: 1.25rem;
  }

  /* Auth forms responsiveness */
  .auth-form {
    width: 100%;
    padding: var(--spacing-lg);
  }
}
