/* Styles for API-connected components */

/* Header search form */
.search-form {
  flex: 1;
  max-width: 400px;
  margin: 0 1rem;
}

.search-form form {
  display: flex;
  position: relative;
}

.search-form input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem 0 0 0.25rem;
  font-size: 0.875rem;
}

.search-form button {
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0 0.25rem 0.25rem 0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-form button:hover {
  background-color: #2563eb;
}

@media (max-width: 768px) {
  .header-content {
    flex-wrap: wrap;
  }

  .search-form {
    order: 3;
    max-width: 100%;
    margin: 0.5rem 0 0;
    flex: 0 0 100%;
  }
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
  gap: 1rem;
}

.pagination button {
  padding: 0.5rem 1rem;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination button:hover:not(.disabled) {
  background-color: #e5e7eb;
}

.pagination button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination span {
  font-size: 0.875rem;
  color: #4b5563;
}

/* Loading and error states */
.error-message {
  padding: 1rem;
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
}

.error-message button {
  margin-top: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
}

.error-message button:hover {
  background-color: #dc2626;
}

/* Product price display */
.card-price {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.original-price {
  text-decoration: line-through;
  color: #6b7280;
  font-size: 0.875rem;
}

.discounted-price {
  font-weight: bold;
  color: #ef4444;
}

.discount-badge {
  background-color: #fee2e2;
  color: #991b1b;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: bold;
}

/* Authentication styles */
.auth-form {
  max-width: 400px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.auth-form h1 {
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  font-size: 1rem;
}

.form-group small {
  display: block;
  margin-top: 0.25rem;
  color: #6b7280;
  font-size: 0.75rem;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 0.25rem;
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary {
  width: 100%;
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-primary:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

.btn-google {
  width: 100%;
  background-color: #fff;
  color: #333;
  border: 1px solid #d1d5db;
}

.btn-google:hover {
  background-color: #f3f4f6;
}

.auth-separator {
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
  color: #6b7280;
}

.auth-separator::before,
.auth-separator::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #d1d5db;
}

.auth-separator span {
  padding: 0 0.5rem;
}

.auth-link {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
}

.auth-link a {
  color: #3b82f6;
  text-decoration: none;
}

.auth-link a:hover {
  text-decoration: underline;
}

/* User menu styles */
.auth-loading {
  font-size: 0.875rem;
  color: #6b7280;
}

.auth-link-button {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border-radius: 0.25rem;
  text-decoration: none;
  transition: background-color 0.2s;
}

.auth-link-button:hover {
  background-color: #2563eb;
}

.user-menu-container {
  position: relative;
}

.user-menu-button {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-menu-button:hover {
  background-color: #e5e7eb;
}

.user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  width: 200px;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.user-menu-item {
  display: block;
  padding: 0.75rem 1rem;
  color: #374151;
  text-decoration: none;
  transition: background-color 0.2s;
  text-align: left;
  width: 100%;
  border: none;
  background: none;
  font-size: 0.875rem;
  cursor: pointer;
}

.user-menu-item:hover {
  background-color: #f3f4f6;
}

.sign-out-button {
  border-top: 1px solid #e5e7eb;
  color: #ef4444;
}

.sign-out-button:hover {
  background-color: #fee2e2;
}

/* Cart styles */
.cart-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.cart-table {
  width: 100%;
  border-collapse: collapse;
}

.cart-table th {
  text-align: left;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 500;
  color: #6b7280;
}

.cart-table td {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.cart-item-product {
  display: flex;
  align-items: center;
}

.cart-item-image {
  width: 80px;
  height: 80px;
  margin-right: 1rem;
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.cart-item-name {
  color: #111827;
  text-decoration: none;
  font-weight: 500;
}

.cart-item-name:hover {
  color: #3b82f6;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantity-control button {
  width: 30px;
  height: 30px;
  border: 1px solid #d1d5db;
  background-color: #f9fafb;
  border-radius: 0.25rem;
  font-size: 1rem;
  cursor: pointer;
}

.quantity-control button:hover:not(:disabled) {
  background-color: #f3f4f6;
}

.quantity-control button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.remove-item-button {
  padding: 0.5rem;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  color: #ef4444;
  cursor: pointer;
}

.remove-item-button:hover:not(:disabled) {
  background-color: #fee2e2;
}

.remove-item-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.cart-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 1.5rem;
}

.cart-summary {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
}

.cart-summary h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.summary-row.total {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
  font-weight: 600;
  font-size: 1.125rem;
}

.checkout-button {
  width: 100%;
  margin-top: 1.5rem;
}

.guest-checkout-notice {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
  font-size: 0.875rem;
  color: #6b7280;
}

.guest-checkout-notice a {
  color: #3b82f6;
  text-decoration: none;
}

.guest-checkout-notice a:hover {
  text-decoration: underline;
}

.empty-cart {
  text-align: center;
  padding: 3rem 0;
}

.empty-cart p {
  margin-bottom: 1.5rem;
  color: #6b7280;
}

@media (max-width: 768px) {
  .cart-container {
    grid-template-columns: 1fr;
  }

  .cart-table {
    display: block;
    overflow-x: auto;
  }
}

/* Add to Cart component styles */
.add-to-cart {
  margin-top: 1.5rem;
}

.quantity-selector {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  max-width: 150px;
}

.quantity-button {
  width: 40px;
  height: 40px;
  border: 1px solid #d1d5db;
  background-color: #f9fafb;
  border-radius: 0.25rem;
  font-size: 1.25rem;
  cursor: pointer;
}

.quantity-button:hover:not(:disabled) {
  background-color: #f3f4f6;
}

.quantity-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-input {
  flex: 1;
  height: 40px;
  margin: 0 0.5rem;
  padding: 0 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  text-align: center;
  font-size: 1rem;
}

.add-to-cart-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.add-to-cart-button {
  flex: 2;
}

.go-to-cart-button {
  flex: 1;
}

.success-message {
  padding: 0.75rem;
  background-color: #d1fae5;
  border: 1px solid #10b981;
  border-radius: 0.25rem;
  color: #065f46;
  margin-top: 1rem;
}

/* Checkout styles */
.checkout-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.checkout-form-container {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.checkout-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.form-section h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
}

.checkout-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 1.5rem;
}

.checkout-summary {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  align-self: start;
  position: sticky;
  top: 1rem;
}

.checkout-summary h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.checkout-items {
  margin-bottom: 1.5rem;
}

.checkout-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.checkout-item:last-child {
  border-bottom: none;
}

.checkout-item-image {
  width: 60px;
  height: 60px;
  margin-right: 1rem;
}

.checkout-item-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.checkout-item-details {
  flex: 1;
}

.checkout-item-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.checkout-item-price {
  font-size: 0.875rem;
  color: #6b7280;
}

.checkout-item-total {
  font-weight: 600;
}

.checkout-totals {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

/* Order confirmation styles */
.order-confirmation {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-top: 2rem;
}

.order-confirmation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.order-confirmation-header h1 {
  margin: 0;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-processing {
  background-color: #e0f2fe;
  color: #0369a1;
}

.status-shipped {
  background-color: #d1fae5;
  color: #065f46;
}

.status-delivered {
  background-color: #bbf7d0;
  color: #166534;
}

.status-cancelled {
  background-color: #fee2e2;
  color: #991b1b;
}

.order-confirmation-message {
  margin-bottom: 2rem;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.order-section {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
}

.order-section h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.order-items {
  margin-bottom: 1.5rem;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item-image {
  width: 80px;
  height: 80px;
  margin-right: 1rem;
}

.order-item-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.order-item-details {
  flex: 1;
}

.order-item-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.order-item-name a {
  color: #111827;
  text-decoration: none;
}

.order-item-name a:hover {
  color: #3b82f6;
}

.order-item-price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.order-item-quantity {
  font-size: 0.875rem;
  color: #6b7280;
}

.order-item-total {
  font-weight: 600;
}

.order-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.order-info-column h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.address-box,
.payment-box,
.status-box {
  background-color: #f9fafb;
  border-radius: 0.25rem;
  padding: 1rem;
}

.address-box p,
.payment-box p {
  margin: 0.25rem 0;
}

.status-item {
  display: flex;
  margin-bottom: 0.75rem;
}

.status-date {
  width: 150px;
  font-size: 0.875rem;
  color: #6b7280;
}

.status-info {
  flex: 1;
}

.status-info p {
  margin: 0.25rem 0 0;
  font-size: 0.875rem;
}

.order-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

/* User orders page styles */
.orders-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.order-card {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.order-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.order-card-info h3 {
  margin: 0 0 0.5rem;
  font-size: 1.125rem;
}

.order-card-info p {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.order-card-body {
  margin-bottom: 1.5rem;
}

.order-card-total {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
}

.order-card-footer {
  display: flex;
  justify-content: flex-end;
}

.empty-orders {
  text-align: center;
  padding: 3rem 0;
}

.empty-orders p {
  margin-bottom: 1.5rem;
  color: #6b7280;
}

/* Stripe integration styles */
.stripe-info {
  display: flex;
  align-items: center;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
}

.stripe-logo {
  height: 32px;
  margin-right: 1rem;
}

.stripe-checkout {
  width: 100%;
}

.checkout-success,
.checkout-cancel {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-top: 2rem;
  text-align: center;
}

.success-icon,
.cancel-icon {
  margin: 0 auto 1.5rem;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-icon {
  color: #10b981;
  background-color: #d1fae5;
}

.cancel-icon {
  color: #ef4444;
  background-color: #fee2e2;
}

.success-actions,
.cancel-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

@media (max-width: 768px) {
  .checkout-container {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .order-info-grid {
    grid-template-columns: 1fr;
  }

  .stripe-info {
    flex-direction: column;
    text-align: center;
  }

  .stripe-logo {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .success-actions,
  .cancel-actions {
    flex-direction: column;
  }
}
